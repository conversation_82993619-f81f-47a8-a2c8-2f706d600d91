package com.sy.erp.server.configuration;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Nacos数据源配置 - 完全依赖Nacos配置中心
 * 
 * 从Nacos的workflow.properties中获取所有数据源配置：
 * - spring.datasource.driver-class-name
 * - spring.datasource.url  
 * - spring.datasource.username
 * - spring.datasource.password
 * - spring.datasource.type
 * - spring.datasource.hikari.* (连接池配置)
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "true")
public class NacosDataSourceConfiguration {

    /**
     * 主数据源 - 从Nacos配置中心获取所有配置
     * 配置前缀：spring.datasource
     */
    @Bean(name = "dataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() {
        log.info("🔄 开始创建Nacos数据源配置");
        log.info("📊 所有配置（包括连接池配置）都将从Nacos的workflow.properties中获取");
        
        // 使用@ConfigurationProperties自动绑定Nacos配置
        // 包括：url, username, password, driver-class-name, type
        // 以及hikari连接池的所有配置
        HikariDataSource dataSource = new HikariDataSource();
        
        log.info("✅ Nacos数据源配置创建成功");
        return dataSource;
    }

    /**
     * 为了兼容现有代码，创建别名数据源
     */
    @Bean(name = "aimoProdMysql")
    public DataSource aimoProdMysql() {
        log.info("🔄 创建aimoProdMysql数据源别名（指向主数据源）");
        return dataSource();
    }

    @Bean(name = "aimoTestMysql")
    public DataSource aimoTestMysql() {
        log.info("🔄 创建aimoTestMysql数据源别名（指向主数据源）");
        return dataSource();
    }
}
