package com.sy.erp.server.configuration;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Nacos数据源配置 - 完全依赖Nacos配置中心
 * 
 * 从Nacos的workflow.properties中获取所有数据源配置：
 * - spring.datasource.driver-class-name
 * - spring.datasource.url  
 * - spring.datasource.username
 * - spring.datasource.password
 * - spring.datasource.type
 * - spring.datasource.hikari.* (连接池配置)
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "true")
public class NacosDataSourceConfiguration {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    /**
     * 主数据源 - 从Nacos配置中心获取所有配置
     */
    @Bean(name = "dataSource")
    @Primary
    public DataSource dataSource() {
        log.info("🔄 开始创建Nacos数据源配置");
        log.info("📊 从Nacos获取配置: url={}, username={}, driver={}", url, username, driverClassName);

        HikariDataSource dataSource = new HikariDataSource();

        // 手动设置基本配置
        dataSource.setJdbcUrl(url);  // 注意：HikariCP使用jdbcUrl而不是url
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);

        // 设置连接池配置
        dataSource.setMaximumPoolSize(20);
        dataSource.setMinimumIdle(5);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);
        dataSource.setLeakDetectionThreshold(60000);
        dataSource.setPoolName("ErpNacosPool");

        log.info("✅ Nacos数据源配置创建成功");
        return dataSource;
    }

    /**
     * 为了兼容现有代码，创建别名数据源
     */
    @Bean(name = "aimoProdMysql")
    public DataSource aimoProdMysql() {
        log.info("🔄 创建aimoProdMysql数据源别名（指向主数据源）");
        return dataSource();
    }

    @Bean(name = "aimoTestMysql")
    public DataSource aimoTestMysql() {
        log.info("🔄 创建aimoTestMysql数据源别名（指向主数据源）");
        return dataSource();
    }
}
