{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login-success.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login-success.vue", "mtime": 1752737748507}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIGltcG9ydCB7IGdldFBhcmFtcyB9IGZyb20gJ0AvbGlicy91dGlsJw0KICBleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTogJ0xvZ2luU3VjY2VzcycsDQogICAgZGF0YSAoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBmcmFtZUhlaWdodDogMCwNCiAgICAgICAgc2NyZWVuSGVpZ2h0OjANCiAgICAgIH0NCiAgICB9DQogICAgLA0KICAgIHdhdGNoOiB7DQogICAgICAnJHJvdXRlJyh2YWwpew0KICAgICAgICB0aGlzLmdldFJvdXRlckRhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgbWV0aG9kczogew0KICAgICAgZ2V0Um91dGVyRGF0YTogZnVuY3Rpb24gKCkgey8v6I635Y+WIGlmcmFtZSBzcmMg6Lev5b6EDQogICAgICAgIGNvbnN0IHBhcmFtcyA9IGdldFBhcmFtcyh3aW5kb3cubG9jYXRpb24uaHJlZik7DQogICAgICAgIGxldCB0b2tlbiA9IGRlY29kZVVSSUNvbXBvbmVudChwYXJhbXMudG9rZW4pOw0KICAgICAgICBpZih0b2tlbil7DQogICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJzZXRUb2tlbiIse3Rva2VuICwgYXV0bzp0cnVlfSkNCiAgICAgICAgICBpZiAod2luZG93Lm9wZW5lciAmJiAhd2luZG93Lm9wZW5lci5jbG9zZWQpIHsNCiAgICAgICAgICAgIHdpbmRvdy5wYXJlbnQub3BlbmVyLmxvY2F0aW9uLnJlbG9hZCgpOw0KICAgICAgICAgIH0NCiAgICAgICAgICB3aW5kb3cuY2xvc2UoKTsNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICB9LA0KICAgIG1vdW50ZWQoKXsNCiAgICAgIHRoaXMuZ2V0Um91dGVyRGF0YSgpDQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["login-success.vue"], "names": [], "mappings": ";AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login-success.vue", "sourceRoot": "src/view/login", "sourcesContent": ["<template id=\"success\">\r\n  <div>\r\n    登录成功\r\n  </div>\r\n</template>\r\n<script>\r\n  import { getParams } from '@/libs/util'\r\n  export default {\r\n    name: 'LoginSuccess',\r\n    data () {\r\n      return {\r\n        frameHeight: 0,\r\n        screenHeight:0\r\n      }\r\n    }\r\n    ,\r\n    watch: {\r\n      '$route'(val){\r\n        this.getRouterData()\r\n      }\r\n    },\r\n    methods: {\r\n      getRouterData: function () {//获取 iframe src 路径\r\n        const params = getParams(window.location.href);\r\n        let token = decodeURIComponent(params.token);\r\n        if(token){\r\n          this.$store.commit(\"setToken\",{token , auto:true})\r\n          if (window.opener && !window.opener.closed) {\r\n            window.parent.opener.location.reload();\r\n          }\r\n          window.close();\r\n        }\r\n      },\r\n    },\r\n    mounted(){\r\n      this.getRouterData()\r\n    }\r\n  }\r\n</script>\r\n"]}]}