{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\index.vue", "mtime": 1752737748512}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/menus", "sourcesContent": ["<template>\r\n  <div>\r\n    <Row :gutter=\"8\">\r\n      <Col :lg=\"11\" :xl=\"11\" :xxl=\"6\">\r\n      <Card :shadow=\"true\">\r\n        <tree-table style=\"overflow: auto\" expand-key=\"menuName\" @radio-click=\"rowClick\" :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"false\"\r\n                    :columns=\"columns\" :data=\"data\" class=\"card-tree-table\" >\r\n          <template v-slot:status=\"scope\">\r\n            <Badge v-for=\"v in statusOps\" v-if=\"v.key === scope.row.status\"\r\n                   :status=\"v.key === 0?'success':'error'\" v-bind:key=\"v.key\"></Badge>\r\n            <Icon :type=\"scope.row.icon\" size=\"16\"/>\r\n          </template>\r\n        </tree-table>\r\n      </Card>\r\n      </Col>\r\n      <Col :lg=\"13\" :xl=\"13\" :xxl=\"10\">\r\n      <Card :shadow=\"true\">\r\n        <div class=\"search-con search-con-top\">\r\n          <ButtonGroup>\r\n            <Button v-if=\"hasAuthority('menuAdd')\" type=\"primary\" @click=\"handleReset\">添加</Button>\r\n            <Button v-if=\"hasAuthority('menuDel')\" :disabled=\"!(formItem.id )\" type=\"primary\"\r\n                    @click=\"confirmModal = true\">删除\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Modal v-model=\"confirmModal\" title=\"提示\" @on-ok=\"handleRemove\">\r\n            确定删除,菜单资源【{{formItem.menuName}}】吗?{{formItem.children && formItem.children.length > 0 ? '存在子菜单,将一起删除.是否继续?' : ''}}\r\n          </Modal>\r\n        </div>\r\n        <Form ref=\"menuForm\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"80\">\r\n          <FormItem label=\"上级菜单\" prop=\"parentId\">\r\n            <treeselect v-model=\"formItem.parentId\" :options=\"selectTreeData\" :default-expand-level=\"1\" :normalizer=\"treeSelectNormalizer\"/>\r\n          </FormItem>\r\n          <FormItem label=\"菜单标识\" prop=\"menuCode\">\r\n            <Input v-model=\"formItem.menuCode\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"菜单名称\" prop=\"menuName\">\r\n            <Input v-model=\"formItem.menuName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"页面地址\" prop=\"path\">\r\n            <Input v-model=\"formItem.path\" placeholder=\"请输入内容\">\r\n            <Select v-model=\"formItem.scheme\" style=\"width: 80px\">\r\n              <Option value=\"/\">/</Option>\r\n              <Option value=\"http://\">http://</Option>\r\n              <Option value=\"https://\">https://</Option>\r\n            </Select>\r\n            <Select v-model=\"formItem.target\" style=\"width: 100px\">\r\n              <Option value=\"_self\">窗口内打开</Option>\r\n              <Option :disabled=\"formItem.scheme==='/'\" value=\"_blank\">新窗口打开</Option>\r\n            </Select>\r\n            </Input>\r\n            <span v-if=\"formItem.scheme === '/'\">前端组件：/view/module/{{formItem.path}}.vue</span>\r\n            <span v-else>跳转地址：{{formItem.scheme}}{{formItem.path}}</span>\r\n          </FormItem>\r\n          <FormItem label=\"图标\">\r\n            <Input v-model=\"formItem.icon\" placeholder=\"请输入内容\">\r\n            <Icon size=\"22\" :type=\"formItem.icon\" />\r\n            <Poptip width=\"600\" placement=\"bottom\">\r\n              <Button icon=\"ios-search\"></Button>\r\n              <div slot=\"content\">\r\n                <ul class=\"icons\">\r\n                  <li class=\"icons-item\" :title=\"item\" @click=\"onIconClick(item)\" v-for=\"item in selectIcons\">\r\n                    <Icon :type=\"item\" size=\"28\"/>\r\n                    <p>{{item}}</p>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </Poptip>\r\n            </Input>\r\n          </FormItem>\r\n          <FormItem label=\"优先级\">\r\n            <InputNumber v-model=\"formItem.priority\"></InputNumber>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"描述\">\r\n            <Input v-model=\"formItem.menuDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button @click=\"handleSubmit\" :loading=\"saving\" type=\"primary\" v-if=\"hasAuthority('menuEdit') || hasAuthority('menuAdd')\">保存</Button>\r\n            <Button @click=\"handleReset\" style=\"margin-left: 8px\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </Card>\r\n      </Col>\r\n      <Col :lg=\"11\" :xl=\"11\" :xxl=\"8\" class=\"lastCol\">\r\n      <Card :shadow=\"true\">\r\n        <menu-action :value=\"formItem\"></menu-action>\r\n      </Card>\r\n      </Col>\r\n    </Row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {listConvertTree} from '@/libs/util'\r\nimport Menu from '@/api/base/menu'\r\nimport MenuAction from './menu-action.vue'\r\nimport icons from './icons'\r\nimport Common from \"@/api/basic/common\";\r\nexport default {\r\n    name: 'systemMenu',\r\n    components: {\r\n      MenuAction\r\n    },\r\n    data () {\r\n      const validateEn = (rule, value, callback) => {\r\n        let reg = /^[_a-zA-Z0-9]+$/\r\n        if (value === '') {\r\n          callback(new Error('菜单标识不能为空'))\r\n        } else if (value !== '' && !reg.test(value)) {\r\n          callback(new Error('只允许字母、数字、下划线'))\r\n        } else {\r\n          callback()\r\n        }\r\n      }\r\n      return {\r\n        statusOps: Common.statusOps,\r\n        confirmModal: false,\r\n        saving: false,\r\n        visible: false,\r\n        selectIcons: icons,\r\n        selectTreeData: [{\r\n          id: 0,\r\n          menuName: '无'\r\n        }],\r\n        formItemRules: {\r\n          parentId: [\r\n            {required: true, message: '上级菜单', trigger: 'blur'}\r\n          ],\r\n          menuCode: [\r\n            {required: true, validator: validateEn, trigger: 'blur'}\r\n          ],\r\n          menuName: [\r\n            {required: true, message: '菜单名称不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          menuCode: '',\r\n          menuName: '',\r\n          icon: 'md-document',\r\n          path: '',\r\n          scheme: '/',\r\n          target: '_self',\r\n          status: 1,\r\n          parentId: '0',\r\n          priority: 0,\r\n          menuDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '菜单名称',\r\n            key: 'menuName',\r\n            minWidth: '200px'\r\n          },\r\n          {\r\n            title: '状态',\r\n            key: 'status',\r\n            type: 'template',\r\n            minWidth: '100px',\r\n            slot: 'status'\r\n          },\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      treeSelectNormalizer (node) {\r\n        return {\r\n          id: node.id,\r\n          label: node.menuName,\r\n          children: node.children\r\n        }\r\n      },\r\n      setSelectTree (data) {\r\n        this.selectTreeData = data\r\n      },\r\n      rowClick (data) {\r\n        this.handleReset()\r\n        if (data) {\r\n          this.formItem = Object.assign({}, data.row)\r\n        }\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          menuCode: '',\r\n          menuName: '',\r\n          icon: 'md-document',\r\n          path: '',\r\n          scheme: '/',\r\n          target: '_self',\r\n          status: 0,\r\n          parentId: '0',\r\n          priority: 0,\r\n          menuDesc: ''\r\n        }\r\n        this.$refs['menuForm'].resetFields()\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        this.$refs['menuForm'].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true\r\n            if (this.formItem.id) {\r\n              Menu.edit(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            } else {\r\n              Menu.add(this.formItem).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('保存成功')\r\n                }\r\n                this.handleSearch()\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      handleRemove () {\r\n        Menu.remove(this.formItem.id).then(res => {\r\n          this.handleReset()\r\n          this.handleSearch()\r\n          if (res['code'] === 0) {\r\n            this.$Message.success('删除成功')\r\n          }\r\n        })\r\n      },\r\n      onIconClick (item) {\r\n        this.formItem.icon = item\r\n      },\r\n      handleSearch () {\r\n        Menu.getAll().then(res => {\r\n          if(res['code'] ===0){\r\n            let opt = {\r\n              primaryKey: 'id',\r\n              parentKey: 'parentId',\r\n              startPid: '0'\r\n            }\r\n            this.data = listConvertTree(res.data, opt)\r\n            this.setSelectTree(this.data)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n<style lang=\"less\">\r\n  .icons {\r\n    overflow: auto;\r\n    zoom: 1;\r\n    height: 300px;\r\n  }\r\n\r\n  .icons-item {\r\n    float: left;\r\n    margin: 6px;\r\n    width: 60px;\r\n    text-align: center;\r\n    list-style: none;\r\n    cursor: pointer;\r\n    color: #5c6b77;\r\n    transition: all .2s ease;\r\n    position: relative;\r\n  }\r\n\r\n  .icons-item p {\r\n    word-break: break-all;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .card-tree-table{\r\n    max-height: 700px;\r\n  }\r\n  @media only screen and (max-width: 1366px) {//1366px宽度以下单独样式\r\n    .card-tree-table{\r\n      max-height: 605px;\r\n    }\r\n    .lastCol{\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n</style>\r\n"]}]}