{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\App.vue", "mtime": 1752737748393}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc2l6ZXsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCn0NCmh0bWwsYm9keXsNCiAgLnNpemU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZzogMDsNCn0NCiNhcHAgew0KICAuc2l6ZTsNCiAgLml2dS1jYXJkLWJvZHkgew0KICAgIHBhZGRpbmc6IDEwcHggMTBweCA2cHggMTBweDsNCiAgfQ0KICAuaXZ1LXRhYmxlLXdyYXBwZXJ7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KfQ0KLml2dS1idG4+Lml2dS1pY29uIHsNCiAgICAgIGxpbmUtaGVpZ2h0OiAwLjUgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'App',\r\n  created() {\r\n    try {\r\n      document.body.removeChild(document.getElementById('appLoading'))\r\n      setTimeout(function() {\r\n        document.getElementById('app').style.display = 'block';\r\n      }, 500)\r\n    } catch (e) {\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\">\r\n.size{\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\nhtml,body{\r\n  .size;\r\n  overflow: hidden;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n#app {\r\n  .size;\r\n  .ivu-card-body {\r\n    padding: 10px 10px 6px 10px;\r\n  }\r\n  .ivu-table-wrapper{\r\n    overflow: hidden;\r\n  }\r\n}\r\n.ivu-btn>.ivu-icon {\r\n      line-height: 0.5 !important;\r\n}\r\n</style>\r\n"]}]}