{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\components\\department-select\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\components\\department-select\\index.vue", "mtime": 1752737748479}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CommonApi", "Department", "name", "props", "groupName", "type", "String", "parentId", "companyId", "defaultExpandLevel", "Number", "default", "value", "Array", "disabledCompany", "Boolean", "width", "limit", "appendToBody", "placeholder", "needNotGroup", "groupDisabled", "disabled", "disabledLevel", "data", "treeData", "myValue", "undefined", "multiple", "mounted", "_this", "getDictionaryValueBy", "then", "res", "itemValue", "JSON", "parse", "isSingle", "companyIds", "company", "split", "parents", "getAllCompany", "res1", "isArray", "allCompany", "length", "filter", "v", "includes", "id", "getAll", "res2", "allDeparts", "getTreeData", "children", "treeArr", "for<PERSON>ach", "push", "apply", "_toConsumableArray", "finally", "watch", "newValue", "$emit", "computed", "realTreeData", "find", "bindData", "map", "item", "items", "defaultExpandLevelR", "methods", "_this2", "resultArr", "getTreeArr", "arr", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "_iterator2", "_step2", "child", "isDisabled", "level", "err", "e", "f", "_iterator3", "_step3", "_loop", "obj", "departmentName", "companyName", "companyNameSimple", "onOpen", "onClose", "inputChange"], "sources": ["src/components/department-select/index.vue"], "sourcesContent": ["<!--\r\n@create date 2020-03-02\r\n@desc 部门选择控件\r\n-->\r\n\r\n<template>\r\n  <div class=\"departmentSelect\">\r\n    <treeselect\r\n      v-model=\"myValue\"\r\n      :options=\"realTreeData\"\r\n      :default-expand-level=\"defaultExpandLevelR\"\r\n      :normalizer=\"\r\n        node => ({\r\n          id: node.id,\r\n          label: node.departmentName,\r\n          children: node.children\r\n        })\"\r\n      :placeholder=\"placeholder || '请选择'\"\r\n      :style=\"{ width }\"\r\n      :multiple=\"multiple\"\r\n      noChildrenText=\"无选项\"\r\n      noOptionsText=\"无匹配项\"\r\n      noResultsText=\"无匹配项\"\r\n      :clearable=\"true\"\r\n      :limit=\"limit || 1\"\r\n      :limitText=\"count => `+ ${count}`\"\r\n      :flat=\"true\"\r\n      :autoDeselectDescendants=\"true\"\r\n      :autoSelectDescendants=\"true\"\r\n      :disableBranchNodes=\"false\"\r\n      :alwaysOpen=\"false\"\r\n      :appendToBody=\"appendToBody\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n      @input=\"inputChange\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <template v-slot:value-label=\"{node}\">\r\n        <div :title=\"multiple? node.raw.departmentName : node.raw.fullPathName\">\r\n\r\n          {{ multiple || groupName === \"reimburse-department\" ? node.raw.departmentName\r\n              : node.raw.fullPathName }}\r\n        </div>\r\n      </template>\r\n      <template v-if=\"groupName === 'amz-operation-center' ||groupName === 'reimburse-department'\" v-slot:option-label=\"{ node }\">\r\n        <div :title=\"multiple? node.raw.departmentName: node.raw.fullPathName\">\r\n          {{multiple || groupName === \"reimburse-department\"? node.raw.departmentName : node.raw.departmentName}}\r\n        </div>\r\n      </template>\r\n    </treeselect>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\"; //字典查询接口\r\nimport Department from \"@/api/base/department\"; //部门信息接口\r\nexport default {\r\n  name: \"department-select\",\r\n  props: {\r\n    groupName: {\r\n      // 区分组件数据源的数据字典配置名称\r\n      type: String\r\n    },\r\n    parentId: {\r\n      // 父组件传入的parentId\r\n      type: String\r\n    },\r\n    companyId: {\r\n      // 父组件传入的companyId\r\n      type: String\r\n    },\r\n    defaultExpandLevel: {\r\n      // 大于等于两家公司选项时，默认展开的树状层级，只有一家公司的时候默认展开第一级，否则不展开\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    value: {\r\n      type: [String, Array, Number]\r\n    },\r\n    disabledCompany: {\r\n      // 多选模式是否可以选择公司，仅多选模式有效\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    width: {\r\n      // 控件宽度，默认210px\r\n      type: String,\r\n      default: \"210px\"\r\n    },\r\n    limit: {\r\n      // 多选模式下最多允许显示的标签数\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    appendToBody: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    placeholder: {\r\n      type: String\r\n    },\r\n    needNotGroup: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    groupDisabled: { type: Boolean, default: false }, //是否禁用level=3的组不能选择，在售Listing部分的部门选择有用到\r\n    disabled: { type: Boolean, default: false },\r\n    disabledLevel: { type: Number }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      myValue: undefined,\r\n      multiple: true\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    const { groupName } = this;\r\n    // 字典获取\r\n    CommonApi.getDictionaryValueBy(\"department_select_group\",groupName).then(res => {\r\n      if (res && res[\"code\"] === 0){\r\n        const itemValue = JSON.parse(res.data);\r\n        const { isSingle } = itemValue; // 取到角色标志和部门id集合\r\n        const companyIds = itemValue.company ? itemValue.company.split(\",\") : []; // 配置的company ids\r\n        const parents = itemValue.parentId ? itemValue.parentId.split(\",\") : []; // 配置的parent ids\r\n        this.multiple = isSingle !== \"true\";\r\n        CommonApi.getAllCompany().then(res1 => {\r\n          if (res1 && Array.isArray(res1.data)) {\r\n            const allCompany =\r\n              companyIds.length > 0? res1.data.filter(v => companyIds.includes(v.id)) : res1.data;\r\n            Department.getAll().then(res2 => {\r\n                if (res2 && Array.isArray(res2.data)) {\r\n                  const allDeparts =parents.length > 0? res2.data.filter(v =>parents.includes(v.parentId) ||parents.includes(v.id)): res2.data;\r\n                  const treeData = this.getTreeData(allCompany, allDeparts);\r\n                  if (groupName === \"amz-operation-center\") {\r\n                    this.treeData = treeData[0] ? treeData[0].children : [];\r\n                  } else if (groupName === \"reimburse-department\") {\r\n                    const treeArr = [];\r\n                    treeData.forEach(v => {\r\n                      treeArr.push(...(v.children || []));\r\n                    });\r\n                    this.treeData = treeArr;\r\n                  } else this.treeData = treeData;\r\n                }\r\n              }).finally(() => {});\r\n          }\r\n        });\r\n      }\r\n    });\r\n  },\r\n\r\n  watch: {\r\n    value(newValue) {\r\n      //父组件传入\r\n      const { multiple } = this;\r\n      //多选模式下必须保证myValue是数组或undefined\r\n      this.myValue = multiple\r\n        ? Array.isArray(newValue)\r\n          ? newValue\r\n          : []\r\n        : newValue;\r\n    },\r\n    myValue(newValue) {\r\n      //本组件双向绑定\r\n      this.$emit(\"input\", newValue);\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    realTreeData() {\r\n      // 真正取的treeData数组\r\n      const { companyId, treeData, multiple } = this;\r\n      const companyIds = companyId ? companyId.split(\",\") : []; // 配置的company ids\r\n      if (companyId === \"0\" && multiple === false) {\r\n        // 级联单选时，不传id不提供任何选项\r\n        return [];\r\n      }\r\n      if (companyIds.length === 1) {\r\n        return (\r\n          (treeData.find(v => companyIds.includes(v.id)) || {}).children || []\r\n        );\r\n      }\r\n      let bindData = companyIds.length > 0? treeData.filter(v => companyIds.includes(v.id)): treeData;\r\n      if (this.needNotGroup)\r\n        bindData.map(item => {\r\n          item.children &&\r\n            item.children.map(items => {\r\n              delete items.children;\r\n              return items;\r\n            });\r\n          return item;\r\n        });\r\n      return bindData;\r\n    },\r\n    defaultExpandLevelR() {\r\n      const { defaultExpandLevel} = this;\r\n      return defaultExpandLevel;\r\n    }\r\n  },\r\n  methods: {\r\n    getTreeData(allCompany, allDeparts) {\r\n\r\n      const { disabledCompany, multiple } = this;\r\n      const resultArr = [];\r\n      const getTreeArr = arr => {\r\n        const treeArr = [];\r\n        for (const item of arr) {\r\n          if (item.parentId === \"0\") {\r\n            treeArr.push(item);\r\n          }\r\n          for (const child of arr) {\r\n            child.isDisabled = this.groupDisabled ? child.level >= 3 : false;\r\n            if (this.disabledLevel && child.level === this.disabledLevel) {\r\n              child.isDisabled = true;\r\n            }\r\n            if (child.parentId === item.id) {\r\n              if (!item.children) {\r\n                item.children = [];\r\n                item.isDisabled = false;\r\n              }\r\n              item.children.push(child);\r\n            }\r\n          }\r\n        }\r\n        return treeArr;\r\n      };\r\n      for (const item of allCompany) {\r\n        const obj = {\r\n          departmentName: item.companyName,\r\n          id: item.id,\r\n          companyNameSimple: item.companyNameSimple,\r\n          children: [],\r\n          isDisabled: multiple ? disabledCompany : true\r\n        };\r\n        const children = allDeparts.filter(v => v.companyId === item.id);\r\n        obj.children = getTreeArr(children);\r\n        resultArr.push(obj);\r\n      }\r\n      return resultArr;\r\n    },\r\n\r\n    onOpen() {\r\n      this.$emit(\"open\");\r\n    },\r\n    onClose(value) {\r\n      this.$emit(\"close\", value);\r\n    },\r\n    inputChange(value) {\r\n      this.$emit(\"inputChange\", value);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" src=\"./index.less\" />\r\n"], "mappings": ";;;;;;;;;;;;AAsDA,OAAAA,SAAA;AACA,OAAAC,UAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACA;MACAC,IAAA,EAAAC;IACA;IACAC,QAAA;MACA;MACAF,IAAA,EAAAC;IACA;IACAE,SAAA;MACA;MACAH,IAAA,EAAAC;IACA;IACAG,kBAAA;MACA;MACAJ,IAAA,EAAAK,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAP,IAAA,GAAAC,MAAA,EAAAO,KAAA,EAAAH,MAAA;IACA;IACAI,eAAA;MACA;MACAT,IAAA,EAAAU,OAAA;MACAJ,OAAA;IACA;IACAK,KAAA;MACA;MACAX,IAAA,EAAAC,MAAA;MACAK,OAAA;IACA;IACAM,KAAA;MACA;MACAZ,IAAA,EAAAK,MAAA;MACAC,OAAA;IACA;IACAO,YAAA;MACAb,IAAA,EAAAU,OAAA;MACAJ,OAAA;IACA;IACAQ,WAAA;MACAd,IAAA,EAAAC;IACA;IACAc,YAAA;MACAf,IAAA,EAAAU,OAAA;MACAJ,OAAA;IACA;IACAU,aAAA;MAAAhB,IAAA,EAAAU,OAAA;MAAAJ,OAAA;IAAA;IAAA;IACAW,QAAA;MAAAjB,IAAA,EAAAU,OAAA;MAAAJ,OAAA;IAAA;IACAY,aAAA;MAAAlB,IAAA,EAAAK;IAAA;EACA;EACAc,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,OAAA,EAAAC,SAAA;MACAC,QAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAA1B,SAAA,QAAAA,SAAA;IACA;IACAJ,SAAA,CAAA+B,oBAAA,4BAAA3B,SAAA,EAAA4B,IAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA;QACA,IAAAC,SAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,GAAA,CAAAT,IAAA;QACA,IAAAa,QAAA,GAAAH,SAAA,CAAAG,QAAA;QACA,IAAAC,UAAA,GAAAJ,SAAA,CAAAK,OAAA,GAAAL,SAAA,CAAAK,OAAA,CAAAC,KAAA;QACA,IAAAC,OAAA,GAAAP,SAAA,CAAA3B,QAAA,GAAA2B,SAAA,CAAA3B,QAAA,CAAAiC,KAAA;QACAV,KAAA,CAAAF,QAAA,GAAAS,QAAA;QACArC,SAAA,CAAA0C,aAAA,GAAAV,IAAA,WAAAW,IAAA;UACA,IAAAA,IAAA,IAAA9B,KAAA,CAAA+B,OAAA,CAAAD,IAAA,CAAAnB,IAAA;YACA,IAAAqB,UAAA,GACAP,UAAA,CAAAQ,MAAA,OAAAH,IAAA,CAAAnB,IAAA,CAAAuB,MAAA,WAAAC,CAAA;cAAA,OAAAV,UAAA,CAAAW,QAAA,CAAAD,CAAA,CAAAE,EAAA;YAAA,KAAAP,IAAA,CAAAnB,IAAA;YACAvB,UAAA,CAAAkD,MAAA,GAAAnB,IAAA,WAAAoB,IAAA;cACA,IAAAA,IAAA,IAAAvC,KAAA,CAAA+B,OAAA,CAAAQ,IAAA,CAAA5B,IAAA;gBACA,IAAA6B,UAAA,GAAAZ,OAAA,CAAAK,MAAA,OAAAM,IAAA,CAAA5B,IAAA,CAAAuB,MAAA,WAAAC,CAAA;kBAAA,OAAAP,OAAA,CAAAQ,QAAA,CAAAD,CAAA,CAAAzC,QAAA,KAAAkC,OAAA,CAAAQ,QAAA,CAAAD,CAAA,CAAAE,EAAA;gBAAA,KAAAE,IAAA,CAAA5B,IAAA;gBACA,IAAAC,QAAA,GAAAK,KAAA,CAAAwB,WAAA,CAAAT,UAAA,EAAAQ,UAAA;gBACA,IAAAjD,SAAA;kBACA0B,KAAA,CAAAL,QAAA,GAAAA,QAAA,MAAAA,QAAA,IAAA8B,QAAA;gBACA,WAAAnD,SAAA;kBACA,IAAAoD,OAAA;kBACA/B,QAAA,CAAAgC,OAAA,WAAAT,CAAA;oBACAQ,OAAA,CAAAE,IAAA,CAAAC,KAAA,CAAAH,OAAA,EAAAI,kBAAA,CAAAZ,CAAA,CAAAO,QAAA;kBACA;kBACAzB,KAAA,CAAAL,QAAA,GAAA+B,OAAA;gBACA,OAAA1B,KAAA,CAAAL,QAAA,GAAAA,QAAA;cACA;YACA,GAAAoC,OAAA;UACA;QACA;MACA;IACA;EACA;EAEAC,KAAA;IACAlD,KAAA,WAAAA,MAAAmD,QAAA;MACA;MACA,IAAAnC,QAAA,QAAAA,QAAA;MACA;MACA,KAAAF,OAAA,GAAAE,QAAA,GACAf,KAAA,CAAA+B,OAAA,CAAAmB,QAAA,IACAA,QAAA,GACA,KACAA,QAAA;IACA;IACArC,OAAA,WAAAA,QAAAqC,QAAA;MACA;MACA,KAAAC,KAAA,UAAAD,QAAA;IACA;EACA;EAEAE,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,IAAA1D,SAAA,QAAAA,SAAA;QAAAiB,QAAA,QAAAA,QAAA;QAAAG,QAAA,QAAAA,QAAA;MACA,IAAAU,UAAA,GAAA9B,SAAA,GAAAA,SAAA,CAAAgC,KAAA;MACA,IAAAhC,SAAA,YAAAoB,QAAA;QACA;QACA;MACA;MACA,IAAAU,UAAA,CAAAQ,MAAA;QACA,OACA,CAAArB,QAAA,CAAA0C,IAAA,WAAAnB,CAAA;UAAA,OAAAV,UAAA,CAAAW,QAAA,CAAAD,CAAA,CAAAE,EAAA;QAAA,UAAAK,QAAA;MAEA;MACA,IAAAa,QAAA,GAAA9B,UAAA,CAAAQ,MAAA,OAAArB,QAAA,CAAAsB,MAAA,WAAAC,CAAA;QAAA,OAAAV,UAAA,CAAAW,QAAA,CAAAD,CAAA,CAAAE,EAAA;MAAA,KAAAzB,QAAA;MACA,SAAAL,YAAA,EACAgD,QAAA,CAAAC,GAAA,WAAAC,IAAA;QACAA,IAAA,CAAAf,QAAA,IACAe,IAAA,CAAAf,QAAA,CAAAc,GAAA,WAAAE,KAAA;UACA,OAAAA,KAAA,CAAAhB,QAAA;UACA,OAAAgB,KAAA;QACA;QACA,OAAAD,IAAA;MACA;MACA,OAAAF,QAAA;IACA;IACAI,mBAAA,WAAAA,oBAAA;MACA,IAAA/D,kBAAA,QAAAA,kBAAA;MACA,OAAAA,kBAAA;IACA;EACA;EACAgE,OAAA;IACAnB,WAAA,WAAAA,YAAAT,UAAA,EAAAQ,UAAA;MAAA,IAAAqB,MAAA;MAEA,IAAA5D,eAAA,QAAAA,eAAA;QAAAc,QAAA,QAAAA,QAAA;MACA,IAAA+C,SAAA;MACA,IAAAC,UAAA,YAAAA,WAAAC,GAAA;QACA,IAAArB,OAAA;QAAA,IAAAsB,SAAA,GAAAC,0BAAA,CACAF,GAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAb,IAAA,GAAAU,KAAA,CAAApE,KAAA;YACA,IAAA0D,IAAA,CAAA/D,QAAA;cACAiD,OAAA,CAAAE,IAAA,CAAAY,IAAA;YACA;YAAA,IAAAc,UAAA,GAAAL,0BAAA,CACAF,GAAA;cAAAQ,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAAH,CAAA,MAAAI,MAAA,GAAAD,UAAA,CAAAF,CAAA,IAAAC,IAAA;gBAAA,IAAAG,KAAA,GAAAD,MAAA,CAAAzE,KAAA;gBACA0E,KAAA,CAAAC,UAAA,GAAAb,MAAA,CAAArD,aAAA,GAAAiE,KAAA,CAAAE,KAAA;gBACA,IAAAd,MAAA,CAAAnD,aAAA,IAAA+D,KAAA,CAAAE,KAAA,KAAAd,MAAA,CAAAnD,aAAA;kBACA+D,KAAA,CAAAC,UAAA;gBACA;gBACA,IAAAD,KAAA,CAAA/E,QAAA,KAAA+D,IAAA,CAAApB,EAAA;kBACA,KAAAoB,IAAA,CAAAf,QAAA;oBACAe,IAAA,CAAAf,QAAA;oBACAe,IAAA,CAAAiB,UAAA;kBACA;kBACAjB,IAAA,CAAAf,QAAA,CAAAG,IAAA,CAAA4B,KAAA;gBACA;cACA;YAAA,SAAAG,GAAA;cAAAL,UAAA,CAAAM,CAAA,CAAAD,GAAA;YAAA;cAAAL,UAAA,CAAAO,CAAA;YAAA;UACA;QAAA,SAAAF,GAAA;UAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;QAAA;UAAAX,SAAA,CAAAa,CAAA;QAAA;QACA,OAAAnC,OAAA;MACA;MAAA,IAAAoC,UAAA,GAAAb,0BAAA,CACAlC,UAAA;QAAAgD,MAAA;MAAA;QAAA,IAAAC,KAAA,YAAAA,MAAA;UAAA,IAAAxB,IAAA,GAAAuB,MAAA,CAAAjF,KAAA;UACA,IAAAmF,GAAA;YACAC,cAAA,EAAA1B,IAAA,CAAA2B,WAAA;YACA/C,EAAA,EAAAoB,IAAA,CAAApB,EAAA;YACAgD,iBAAA,EAAA5B,IAAA,CAAA4B,iBAAA;YACA3C,QAAA;YACAgC,UAAA,EAAA3D,QAAA,GAAAd,eAAA;UACA;UACA,IAAAyC,QAAA,GAAAF,UAAA,CAAAN,MAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAxC,SAAA,KAAA8D,IAAA,CAAApB,EAAA;UAAA;UACA6C,GAAA,CAAAxC,QAAA,GAAAqB,UAAA,CAAArB,QAAA;UACAoB,SAAA,CAAAjB,IAAA,CAAAqC,GAAA;QACA;QAXA,KAAAH,UAAA,CAAAX,CAAA,MAAAY,MAAA,GAAAD,UAAA,CAAAV,CAAA,IAAAC,IAAA;UAAAW,KAAA;QAAA;MAWA,SAAAL,GAAA;QAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;MAAA;QAAAG,UAAA,CAAAD,CAAA;MAAA;MACA,OAAAhB,SAAA;IACA;IAEAwB,MAAA,WAAAA,OAAA;MACA,KAAAnC,KAAA;IACA;IACAoC,OAAA,WAAAA,QAAAxF,KAAA;MACA,KAAAoD,KAAA,UAAApD,KAAA;IACA;IACAyF,WAAA,WAAAA,YAAAzF,KAAA;MACA,KAAAoD,KAAA,gBAAApD,KAAA;IACA;EACA;AACA"}]}