{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearance\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearance\\index.vue", "mtime": 1752737748522}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;AA8GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/clearance/clearance", "sourcesContent": ["<!--\r\n@create date 2020-07-09\r\n@desc 报关订舱表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline @keydown.native.enter.prevent=\"handleSearch\">\r\n      <FormItem prop=\"date\">\r\n        <DatePicker type=\"daterange\" v-model=\"searchForm.date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                    placeholder=\"发货开始日期-发货结束日期\" style=\"width: 200px\"></DatePicker>\r\n      </FormItem>\r\n      <FormItem prop=\"thdOrder\">\r\n        <Input type=\"text\" v-model=\"searchForm.thdOrder\" placeholder=\"货件号\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"clearanceRank\">\r\n        <Input type=\"text\" v-model=\"searchForm.clearanceRank\" placeholder=\"合并编码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"consignorId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.consignorId\" placeholder=\"境内发货人\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in consignorList\" :value=\"item.id\" :key=\"index\">{{ item['consignorName'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"consigneeId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.consigneeId\" placeholder=\"境外收货人\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in consigneeList\" :value=\"item.id\" :key=\"index\">{{ item['consigneeName'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"providerId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.providerId\" placeholder=\"物流商\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in providerList\" :value=\"item.id\" :key=\"index\">{{ item['providerCode'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"channelId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.channelId\" placeholder=\"物流渠道\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in channelList\" :value=\"item.id\" :key=\"index\">{{ item['channelName'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"whCode\" :clear=\"true\">\r\n        <Input type=\"text\" v-model=\"searchForm.whCode\" placeholder=\"仓库代码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.country\" placeholder=\"目的国家\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"emailStatus\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.emailStatus\" placeholder=\"发送邮件\" style=\"width:160px\" :clear=\"true\">\r\n          <Option v-for=\"(item,index) in [{key:0,name:'否'},{key:1,name:'是'}]\" :value=\"item.key\" :key=\"index\">{{ item['name'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\r\n        <Button style=\"margin-left:10px\" @click=\"handleReset()\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <Button type=\"primary\" :disabled=\"selectData.length === 0\" @click=\"sendEmail\">发送邮件</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" ref=\"selectTable\"\r\n           @on-select=\"handleSelectRow\"\r\n           @on-select-cancel=\"handleCancelRow\"\r\n           @on-select-all=\"handleSelectAll\"\r\n           @on-select-all-cancel=\"handleSelectAll\" :max-height=\"autoTableHeight($refs.selectTable,55)\" >\r\n      <template v-slot:consignor=\"{ row }\">\r\n        <span v-for=\"(item, index) in consignorList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['consignorId']\">{{ item['consignorName'] }}</span>\r\n      </template>\r\n      <template v-slot:consignee=\"{ row }\">\r\n        <span v-for=\"(item, index) in consigneeList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['consigneeId']\">{{ item['consigneeName'] }}</span>\r\n      </template>\r\n      <template v-slot:provider=\"{ row }\">\r\n        <span v-for=\"(item, index) in providerList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['providerId']\">{{ item['providerCode'] }}</span>\r\n      </template>\r\n      <template v-slot:providerChannel=\"{ row }\">\r\n        <span v-for=\"(item, index) in channelList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['channelId']\">{{ item['channelName'] }}</span>\r\n      </template>\r\n      <template v-slot:shipType=\"{ row }\">\r\n        <span v-for=\"(item, index) in shipTypeList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['shipType']\">{{ item['name'] }}</span>\r\n      </template>\r\n      <template v-slot:country=\"{ row }\">\r\n        <span v-for=\"(item, index) in countryList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['two_code'] === row['country']\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row,index}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"lookBill(row)\" style=\"margin:0 2px\">查看</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"backBill(row)\" style=\"margin:0 2px\">撤回</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'\r\n          :transfer=\"true\"></Page>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n    <ClearanceInfo ref=\"clearanceInfoRef\" :clearanceVisible=\"clearanceInfoVisible\" :onCancel=\"()=>clearanceInfoVisible=false\" :currencyList=\"currencyList\"\r\n                   :channelList=\"channelList\" :consignorList=\"consignorList\" :providerList=\"providerList\" :shipTypeList=\"shipTypeList\"  @onSuccess=\"handleSearch\"/>\r\n\r\n    <!-- 发送邮件弹窗 -->\r\n    <div v-if=\"clearanceEmailVisible\">\r\n      <CustomEmail ref=\"clearanceEmailRef\" :emailVisible=\"clearanceEmailVisible\" @emailCancel=\"()=>{this.clearanceEmailVisible=false}\"/>\r\n    </div>\r\n  </Card>\r\n</template>\r\n<script>\r\n\r\nimport {getYearDate, isEmpty} from '@/libs/tools'; // 引入非空判断方法\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport Common from \"@/api/basic/common\";\r\nimport Consignor from \"@/api/custom/consignor\";\r\nimport Consignee from \"@/api/custom/consignee\";\r\nimport Provider from \"@/api/logistics/provider\";\r\nimport ProviderChannel from \"@/api/logistics/providerChannel\";\r\nimport ClearanceInvoice from \"@/api/custom/clearanceInvoice\";\r\nimport WhAddress from \"@/api/custom/whAddress\";\r\nimport Currency from \"@/api/basf/currency\";\r\nimport ClearanceInfo from \"@/view/module/custom/clearance/clearanceInfo/index.vue\";\r\nimport CustomEmail from \"@/view/module/custom/custom/customInfo/customEmail.vue\";\r\nimport ProviderEmail from \"@/api/logistics/providerEmail\";\r\nimport {autoTableHeight} from '@/libs/tools'; // 引入非空判断方法\r\nexport default {\r\n  name: 'clearance',\r\n  components: {CustomEmail, LogModel,ClearanceInfo},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      modal: false,\r\n      yesNoOps: Common.yesNoOps,\r\n      logVisible: false,\r\n      saving: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      loading: false,\r\n      selectData:[],\r\n      clearanceInfoVisible:false,\r\n      clearanceEmailVisible:false,\r\n      title: '',\r\n      form:{id:null,},\r\n      searchForm: {\r\n        startDate: null,\r\n        endDate: null,\r\n        emailStatus: null,\r\n        consignorId: null,\r\n        consigneeId: null,\r\n        providerId: null,\r\n        channelId: null,\r\n        customRank: null,\r\n        date: [],\r\n      },\r\n      date: {start: null, end: null},\r\n      columns: [{type: 'selection',width: 70,align: 'center',fixed:'left',},\r\n        {title: '发货日期',key: 'picDate',align: 'center',width: 100,},\r\n        {title: '平台单号',key: 'thdOrder',width: 150,tooltip:true,align: 'center'},\r\n        {title: '平台跟踪号',key: 'thdRef',width: 150,tooltip:true,align: 'center'},\r\n        {title: '国内发货人',key: 'consignorId',align: 'center',width: 110,slot:\"consignor\"},\r\n        {title: '国外收货人',key: 'consigneeId',align: 'center',width: 110,slot:\"consignee\"},\r\n        {title: '物流商',key: 'providerId',align: 'center',width: 110,slot:\"provider\"},\r\n        {title: '物流渠道',key: 'channelId',align: 'center',width: 110,slot:\"providerChannel\"},\r\n        {title: '物流方式',key: 'shipType',align: 'center',width: 120,slot:\"shipType\"},\r\n        {title: '清关国',key: 'country',align: 'center',width: 100,slot:\"country\"},\r\n        {title: '仓库地址',key: 'address',tooltip:true,align: 'center',width: 150,},\r\n        {title: '仓库代码',key: 'whCode',align: 'center',width: 110,},\r\n        {title: '总箱数',key: 'boxQty',align: 'center',width: 100,},\r\n        {title: '总毛重（千克）',key: 'grossWeight',width: 90,align: 'center'},\r\n        {title: '总数量',key: 'qty',align: 'center',width: 100,},\r\n        {title: '清关合并编号',key: 'clearanceRank',width: 90,align: 'center'},\r\n        {title: '是否已发邮件',key: 'emailStatus',width:110,align: 'center',\r\n          render:(h,params)=>{\r\n          return h('span',{},  this.yesNoOps.filter(item=>item['key'] === params.row.emailStatus).map(item=>item['name']).join())\r\n        }},\r\n        {title: '生成人员',key: 'createUser',align: 'center',width: 110,},\r\n        {title: '生成日期',key: 'createTime',align: 'center',width: 100,},\r\n        {title: '备注',key: 'remark',align: 'center',width: 100,},\r\n        {title: '操作',key: 'none',slot:'action',align: 'center',width: 150,fixed:'left'}],\r\n      data: [],\r\n      consignorList: [],\r\n      consigneeList: [],\r\n      providerList: [],\r\n      shipTypeList: [],\r\n      addressList: [],\r\n      channelList: [],\r\n      countryList: [],\r\n      currencyList:[],\r\n      businessType:1,\r\n      refType: null,\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getAllConsignor();\r\n    this.getAllConsignee();\r\n    this.getAllProvider();\r\n    this.getAllProviderChannel();\r\n    this.handleShipType();\r\n    this.getAllAddress();\r\n    this.getCountryList();\r\n    this.getLogRefType();\r\n    this.handleCurrency();\r\n  },\r\n  methods: {\r\n    sendEmail () {\r\n      const logisticsSet = new Set(this.selectData.map(item=>item['providerId']));\r\n      if(logisticsSet.size !== 1) return this.$Message.error(\"您好，您选择的数据存在不同货代，请重新选择同一个货代的数据！\");\r\n      const flag = this.selectData.every(item=> 0 === item['emailStatus']);\r\n      if(flag){\r\n        this.confirmSendEmail()\r\n      }else{\r\n        this.$Modal.confirm({\r\n          title: '提示！',\r\n          content: '您好，你选择得数据中已发送过邮件，请问是需要重新发送吗？',\r\n          onOk: () => {\r\n            this.confirmSendEmail()\r\n          }\r\n        });\r\n      }\r\n    },\r\n    confirmSendEmail(){\r\n      let providerId = this.selectData[0]['providerId'];\r\n      let country = this.selectData[0]['country'];\r\n      let templatePro = CommonApi.getDictionaryValueBy(\"email_config\",\"clearance_email_template\");\r\n      let providerEmailPro = ProviderEmail.getProviderEmail({\"parentId\":providerId,\"businessType\":this.businessType,\"country\":country});\r\n      let providerName = this.providerList.filter(item=>item['id'] === providerId).map(item=>item['providerName']).join();\r\n      let ids = this.selectData.map(item=>item['id']);\r\n      Promise.all([templatePro,providerEmailPro])\r\n        .then(res => {\r\n          //拼接主题\r\n          let title = providerName +`${getYearDate(new Date())}发票资料`;\r\n          let emailData = res[1]['data'];\r\n          let email = emailData['email'];\r\n          let ccEmail = emailData['ccEmail'];\r\n          //邮件模板内容\r\n          let template = res[0].data.replace(/\\n/g,'<br>');\r\n          this.clearanceEmailVisible = true;\r\n          setTimeout(()=>{\r\n            const {clearanceEmailRef} = this.$refs;\r\n            if (clearanceEmailRef) {\r\n              clearanceEmailRef.setDefault({\"email\":email,\"ccEmail\":ccEmail,\"title\":title,\"content\":template,\"type\":2,\"ids\":ids});\r\n            }\r\n          },500)\r\n        }).catch(() => {\r\n      })\r\n    },\r\n    handleCurrency() {\r\n      Currency.getAll().then(res => {\r\n        this.currencyList = res.data;\r\n      });\r\n    },\r\n    handleShipType() {\r\n      CommonApi.getDictionaryValueBy(\"logistics_base\", \"shipType\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shipTypeList = JSON.parse(res.data);\r\n        }\r\n      })\r\n    },\r\n    getAllAddress() {\r\n      WhAddress.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.addressList = res.data;\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n            this.handleSearch();\r\n          }\r\n        }\r\n      })\r\n    },\r\n    changeSelect(v, row) {\r\n      this.id = v ? row.id : null;\r\n      this.data.map(item => {\r\n        this.$set(item, 'single', false);\r\n        item['single'] = false;\r\n      })\r\n      if (this.id) {\r\n        this.data.filter(item => item['customRank'] === row['customRank'] && item['customStatus'] !== 1).map(item => {\r\n          this.$set(item, 'single', true);\r\n          item['single'] = true;\r\n        });\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo};\r\n      ClearanceInvoice.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.loading = false;\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n          this.selectData = [];\r\n        }\r\n      })\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    getAllConsignor() {\r\n      Consignor.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.consignorList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllConsignee() {\r\n      Consignee.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.consigneeList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllProvider() {\r\n      Provider.getAll({\"providerType\":\"大货物流商\"}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.providerList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllProviderChannel() {\r\n      ProviderChannel.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.channelList = res.data;\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n      this.pageInfo={\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      };\r\n    },\r\n    changeConsignor(v) {\r\n      if (!isEmpty(v)) {\r\n        this.consigneeForm.consignorName = v.label;\r\n        this.consigneeForm.parentId = v.value;\r\n      }\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      ClearanceInvoice.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    lookBill(row) {\r\n      const {clearanceInfoRef} = this.$refs;\r\n      if (clearanceInfoRef) {\r\n        clearanceInfoRef.setDefault(null, row['id']);\r\n      }\r\n      this.clearanceInfoVisible = true;\r\n    },\r\n    backBill(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          ClearanceInvoice.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    //全选与取消全选\r\n    handleSelectAll (selection){\r\n      if (selection.length === 0) {\r\n        let data = this.$refs.selectTable.data\r\n        for (let i = 0; i < data.length; i++) {\r\n          for (let j = 0; j < this.selectData.length; j++) {\r\n            if(data[i].id === this.selectData[j].id){\r\n              this.selectData.splice(j,1)\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        for( const t of selection){\r\n          if(this.selectData.find(c=> c.id === t.id)){ continue;}\r\n          this.selectData.push(t)\r\n        }\r\n      }\r\n    },\r\n    //  选中某一行\r\n    handleSelectRow (selection,row) {\r\n      this.selectData.push(row);\r\n    },\r\n    //  取消某一行\r\n    handleCancelRow (selection,row) {\r\n      this.selectData.map((item,index)=>{\r\n        if(item.id === row.id){\r\n          this.selectData.splice(index,1)\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}