{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue", "mtime": 1752737748513}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIkQ6L1VzZXJzL2FkbWluaS9EZXNrdG9wL2Rldi9zeV9kZWNsYXJlX3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyLmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovVXNlcnMvYWRtaW5pL0Rlc2t0b3AvZGV2L3N5X2RlY2xhcmVfdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgX3RvQ29uc3VtYWJsZUFycmF5IGZyb20gIkQ6L1VzZXJzL2FkbWluaS9EZXNrdG9wL2Rldi9zeV9kZWNsYXJlX3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90b0NvbnN1bWFibGVBcnJheS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0IHsgYXV0b1RhYmxlSGVpZ2h0IH0gZnJvbSAiQC9saWJzL3Rvb2xzLmpzIjsKaW1wb3J0IHsgZ2V0VG9rZW4sIGdldFVybCB9IGZyb20gJ0AvbGlicy91dGlsJzsKaW1wb3J0IE11bHRpcGxlIGZyb20gIkAvdmlldy9tb2R1bGUvY29tbW9uL211bHRpcGxlSW5wdXQudnVlIjsKaW1wb3J0IFByb2R1Y3QgZnJvbSAiQC9hcGkvYmFzZS9wcm9kdWN0IjsKaW1wb3J0IFNob3BTZWxlY3QgZnJvbSAiX2Mvc2hvcFNlbGVjdC9pbmRleC52dWUiOwppbXBvcnQgVXBjQ29kZSBmcm9tICJAL2FwaS9uZXdBcHBseS91cGNDb2RlIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJwcm9kdWN0TWFuYWdlIiwKICBjb21wb25lbnRzOiB7CiAgICBTaG9wU2VsZWN0OiBTaG9wU2VsZWN0LAogICAgTXVsdGlwbGU6IE11bHRpcGxlCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgdmFyIGggPSB0aGlzLiRjcmVhdGVFbGVtZW50OwogICAgcmV0dXJuIHsKICAgICAgYXV0b1RhYmxlSGVpZ2h0OiBhdXRvVGFibGVIZWlnaHQsCiAgICAgIHN5bmNWaXNpYmxlOiBmYWxzZSwKICAgICAgc3luY0xvYWRpbmc6IGZhbHNlLAogICAgICBzeW5jRm9ybTogewogICAgICAgICJjb2RlcyI6IG51bGwKICAgICAgfSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIG11bHRpVmFsdWVzQ29kZTogW10sCiAgICAgIHBvcFZpc2libGVDb2RlOiBmYWxzZSwKICAgICAgcG9wQ29udGVudENvZGU6IHVuZGVmaW5lZCwKICAgICAgbXVsdGlWYWx1ZXNOYW1lOiBbXSwKICAgICAgcG9wVmlzaWJsZU5hbWU6IGZhbHNlLAogICAgICBwb3BDb250ZW50TmFtZTogdW5kZWZpbmVkLAogICAgICBtdWx0aVZhbHVlc1NwZWM6IFtdLAogICAgICBwb3BWaXNpYmxlU3BlYzogZmFsc2UsCiAgICAgIHBvcENvbnRlbnRTcGVjOiB1bmRlZmluZWQsCiAgICAgIG1lcmdlQ29sdW1uczogWydpbmRleCcsICdjb2RlJywgJ25hbWUnLCAnc3BlYycsICdpc0NvbWJvJywgJ3N5bmNGbGFnJywgJ3VwZGF0ZVRpbWUnXSwKICAgICAgc3RhdHVzTGlzdDogW3sKICAgICAgICAia2V5IjogLTEsCiAgICAgICAgInZhbHVlIjogIuWFqOmDqCIKICAgICAgfSwgewogICAgICAgICJrZXkiOiAwLAogICAgICAgICJ2YWx1ZSI6ICLlkKYiCiAgICAgIH0sIHsKICAgICAgICAia2V5IjogMSwKICAgICAgICAidmFsdWUiOiAi5pivIgogICAgICB9XSwKICAgICAgaW1wb3J0VVJsOiBnZXRVcmwoKSArIFByb2R1Y3QucGF0aCArICcvaW1wb3J0RmlsZScsCiAgICAgIGxvZ2luSW5mbzogewogICAgICAgIEFjY2VwdDogJ2FwcGxpY2F0aW9uL2pzb24sdGV4dC9wbGFpbiwgKi8qLCAqLyonLAogICAgICAgIG1vZGU6ICdjb3JzJywKICAgICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyBnZXRUb2tlbigpCiAgICAgIH0sCiAgICAgIHNlYXJjaEZvcm06IHsKICAgICAgICBpc0NvbWJvOiBudWxsLAogICAgICAgIGlzVGhkOiBudWxsLAogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgbGltaXQ6IDEwCiAgICAgIH0sCiAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgIC8v6KGo5qC85pWw5o2uCiAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICcjJywKICAgICAgICBrZXk6ICdpbmRleCcsCiAgICAgICAgd2lkdGg6IDYwLAogICAgICAgIGFsaWduOiAnY2VudGVyJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnvJbnoIEnLAogICAgICAgIGtleTogJ2NvZGUnLAogICAgICAgIHdpZHRoOiAxNTAsCiAgICAgICAgcmVzaXphYmxlOiB0cnVlLAogICAgICAgIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKF8sIF9yZWYpIHsKICAgICAgICAgIHZhciByb3cgPSBfcmVmLnJvdzsKICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgewogICAgICAgICAgICAiZGlyZWN0aXZlcyI6IFt7CiAgICAgICAgICAgICAgbmFtZTogImNvcHl0ZXh0IiwKICAgICAgICAgICAgICB2YWx1ZTogcm93Wydjb2RlJ10KICAgICAgICAgICAgfV0KICAgICAgICAgIH0sIFtyb3dbJ2NvZGUnXV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn57G75Z6LJywKICAgICAgICBrZXk6ICduYW1lJywKICAgICAgICBtaW5XaWR0aDogMTIwLAogICAgICAgIHJlc2l6YWJsZTogdHJ1ZSwKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihfLCBfcmVmMikgewogICAgICAgICAgdmFyIHJvdyA9IF9yZWYyLnJvdzsKICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgewogICAgICAgICAgICAiZGlyZWN0aXZlcyI6IFt7CiAgICAgICAgICAgICAgbmFtZTogImNvcHl0ZXh0IiwKICAgICAgICAgICAgICB2YWx1ZTogcm93WyduYW1lJ10KICAgICAgICAgICAgfV0KICAgICAgICAgIH0sIFtyb3dbJ25hbWUnXV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6KeE5qC8JywKICAgICAgICBrZXk6ICdzcGVjJywKICAgICAgICBtaW5XaWR0aDogMjUwLAogICAgICAgIHJlc2l6YWJsZTogdHJ1ZSwKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihfLCBfcmVmMykgewogICAgICAgICAgdmFyIHJvdyA9IF9yZWYzLnJvdzsKICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgewogICAgICAgICAgICAiZGlyZWN0aXZlcyI6IFt7CiAgICAgICAgICAgICAgbmFtZTogImNvcHl0ZXh0IiwKICAgICAgICAgICAgICB2YWx1ZTogcm93WydzcGVjJ10KICAgICAgICAgICAgfV0KICAgICAgICAgIH0sIFtyb3dbJ3NwZWMnXV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5piv5ZCm57uE5ZCI5ZOBJywKICAgICAgICBrZXk6ICdpc0NvbWJvJywKICAgICAgICB3aWR0aDogMTIwLAogICAgICAgIHJlc2l6YWJsZTogdHJ1ZSwKICAgICAgICBzbG90OiAnaXNDb21ibycKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5piv5ZCm5ZCM5q2lJywKICAgICAgICBrZXk6ICdzeW5jRmxhZycsCiAgICAgICAgd2lkdGg6IDEyMCwKICAgICAgICByZXNpemFibGU6IHRydWUsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoXywgX3JlZjQpIHsKICAgICAgICAgIHZhciByb3cgPSBfcmVmNC5yb3c7CiAgICAgICAgICByZXR1cm4gaCgic3BhbiIsIHsKICAgICAgICAgICAgImRpcmVjdGl2ZXMiOiBbewogICAgICAgICAgICAgIG5hbWU6ICJjb3B5dGV4dCIsCiAgICAgICAgICAgICAgdmFsdWU6IHJvd1snc3luY0ZsYWcnXQogICAgICAgICAgICB9XQogICAgICAgICAgfSwgW3Jvd1snc3luY0ZsYWcnXV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5ZCM5q2l5pe26Ze0JywKICAgICAgICBrZXk6ICd1cGRhdGVUaW1lJywKICAgICAgICB3aWR0aDogMTgwLAogICAgICAgIHJlc2l6YWJsZTogdHJ1ZSwKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihfLCBfcmVmNSkgewogICAgICAgICAgdmFyIHJvdyA9IF9yZWY1LnJvdzsKICAgICAgICAgIHJldHVybiBoKCJzcGFuIiwgewogICAgICAgICAgICAiZGlyZWN0aXZlcyI6IFt7CiAgICAgICAgICAgICAgbmFtZTogImNvcHl0ZXh0IiwKICAgICAgICAgICAgICB2YWx1ZTogcm93Wyd1cGRhdGVUaW1lJ10KICAgICAgICAgICAgfV0KICAgICAgICAgIH0sIFtyb3dbJ3VwZGF0ZVRpbWUnXV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5a2Q57yW56CBJywKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAga2V5OiAnY2hpbGRTa3UnLAogICAgICAgIHdpZHRoOiAxMjAsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgX3JlZjYpIHsKICAgICAgICAgIHZhciByb3cgPSBfcmVmNi5yb3c7CiAgICAgICAgICByZXR1cm4gaCgic3BhbiIsIHsKICAgICAgICAgICAgImRpcmVjdGl2ZXMiOiBbewogICAgICAgICAgICAgIG5hbWU6ICJjb3B5dGV4dCIsCiAgICAgICAgICAgICAgdmFsdWU6IHJvdy5jaGlsZFNrdQogICAgICAgICAgICB9XQogICAgICAgICAgfSwgW3Jvdy5jaGlsZFNrdV0pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5a2Q6KeE5qC8JywKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAga2V5OiAnY2hpbGRTcGVjJywKICAgICAgICBtaW5XaWR0aDogMjAwLAogICAgICAgIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgsIF9yZWY3KSB7CiAgICAgICAgICB2YXIgcm93ID0gX3JlZjcucm93OwogICAgICAgICAgcmV0dXJuIGgoInNwYW4iLCB7CiAgICAgICAgICAgICJkaXJlY3RpdmVzIjogW3sKICAgICAgICAgICAgICBuYW1lOiAiY29weXRleHQiLAogICAgICAgICAgICAgIHZhbHVlOiByb3cuY2hpbGRTcGVjCiAgICAgICAgICAgIH1dCiAgICAgICAgICB9LCBbcm93LmNoaWxkU3BlY10pOwogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5a2Q5pWw6YePJywKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAga2V5OiAnaXRlbVF1YW50aXR5JywKICAgICAgICB3aWR0aDogODAsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgX3JlZjgpIHsKICAgICAgICAgIHZhciByb3cgPSBfcmVmOC5yb3c7CiAgICAgICAgICByZXR1cm4gaCgic3BhbiIsIHsKICAgICAgICAgICAgImRpcmVjdGl2ZXMiOiBbewogICAgICAgICAgICAgIG5hbWU6ICJjb3B5dGV4dCIsCiAgICAgICAgICAgICAgdmFsdWU6IHJvdy5pdGVtUXVhbnRpdHkKICAgICAgICAgICAgfV0KICAgICAgICAgIH0sIFtyb3cuaXRlbVF1YW50aXR5XSk7CiAgICAgICAgfQogICAgICB9XQogICAgfTsKICB9LAogIC8v57uE5Lu25Yid5aeL5YyW6L+b6KGM55qE5pON5L2cCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbG9zZURyb3Bkb3duQ29kZTogZnVuY3Rpb24gY2xvc2VEcm9wZG93bkNvZGUoKSB7CiAgICAgIC8v5YWz6Zet6L6T5YWl5paH5pys5qGGCiAgICAgIHZhciBwb3BDb250ZW50Q29kZSA9IHRoaXMucG9wQ29udGVudENvZGU7CiAgICAgIHZhciBtdWx0aXBsZVJlZkNvZGVSZWYgPSB0aGlzLiRyZWZzLm11bHRpcGxlUmVmQ29kZVJlZjsKICAgICAgdGhpcy5wb3BWaXNpYmxlQ29kZSA9IGZhbHNlOwogICAgICBpZiAoIXBvcENvbnRlbnRDb2RlKSByZXR1cm47CiAgICAgIHZhciBjb250ZW50ID0gcG9wQ29udGVudENvZGUgPyBwb3BDb250ZW50Q29kZS50cmltKCkucmVwbGFjZSgv77yML2csICIsIikgOiAnJzsKICAgICAgdGhpcy5tdWx0aVZhbHVlc0NvZGUgPSBjb250ZW50LnNwbGl0KCdcbicpLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiAhIXY7CiAgICAgIH0pOwogICAgICB0aGlzLm11bHRpVmFsdWVzQ29kZSA9IF90b0NvbnN1bWFibGVBcnJheShuZXcgU2V0KHRoaXMubXVsdGlWYWx1ZXNDb2RlKSk7CiAgICAgIGlmIChtdWx0aXBsZVJlZkNvZGVSZWYgJiYgbXVsdGlwbGVSZWZDb2RlUmVmLnNldFZhbHVlQXJyYXkpIHsKICAgICAgICBtdWx0aXBsZVJlZkNvZGVSZWYuc2V0VmFsdWVBcnJheSh0aGlzLm11bHRpVmFsdWVzQ29kZSk7CiAgICAgIH0KICAgIH0sCiAgICBjbG9zZURyb3Bkb3duTmFtZTogZnVuY3Rpb24gY2xvc2VEcm9wZG93bk5hbWUoKSB7CiAgICAgIC8v5YWz6Zet6L6T5YWl5paH5pys5qGGCiAgICAgIHZhciBwb3BDb250ZW50TmFtZSA9IHRoaXMucG9wQ29udGVudE5hbWU7CiAgICAgIHZhciBtdWx0aXBsZVJlZk5hbWVSZWYgPSB0aGlzLiRyZWZzLm11bHRpcGxlUmVmTmFtZVJlZjsKICAgICAgdGhpcy5wb3BWaXNpYmxlTmFtZSA9IGZhbHNlOwogICAgICBpZiAoIXBvcENvbnRlbnROYW1lKSByZXR1cm47CiAgICAgIHZhciBjb250ZW50ID0gcG9wQ29udGVudE5hbWUgPyBwb3BDb250ZW50TmFtZS50cmltKCkucmVwbGFjZSgv77yML2csICIsIikgOiAnJzsKICAgICAgdGhpcy5tdWx0aVZhbHVlc05hbWUgPSBjb250ZW50LnNwbGl0KCdcbicpLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiAhIXY7CiAgICAgIH0pOwogICAgICB0aGlzLm11bHRpVmFsdWVzTmFtZSA9IF90b0NvbnN1bWFibGVBcnJheShuZXcgU2V0KHRoaXMubXVsdGlWYWx1ZXNOYW1lKSk7CiAgICAgIGlmIChtdWx0aXBsZVJlZk5hbWVSZWYgJiYgbXVsdGlwbGVSZWZOYW1lUmVmLnNldFZhbHVlQXJyYXkpIHsKICAgICAgICBtdWx0aXBsZVJlZk5hbWVSZWYuc2V0VmFsdWVBcnJheSh0aGlzLm11bHRpVmFsdWVzQ29kZSk7CiAgICAgIH0KICAgIH0sCiAgICBjbG9zZURyb3Bkb3duU3BlYzogZnVuY3Rpb24gY2xvc2VEcm9wZG93blNwZWMoKSB7CiAgICAgIC8v5YWz6Zet6L6T5YWl5paH5pys5qGGCiAgICAgIHZhciBwb3BDb250ZW50U3BlYyA9IHRoaXMucG9wQ29udGVudFNwZWM7CiAgICAgIHZhciBtdWx0aXBsZVJlZlNwZWNSZWYgPSB0aGlzLiRyZWZzLm11bHRpcGxlUmVmU3BlY1JlZjsKICAgICAgdGhpcy5wb3BWaXNpYmxlU3BlYyA9IGZhbHNlOwogICAgICBpZiAoIXBvcENvbnRlbnRTcGVjKSByZXR1cm47CiAgICAgIHZhciBjb250ZW50ID0gcG9wQ29udGVudFNwZWMgPyBwb3BDb250ZW50U3BlYy50cmltKCkucmVwbGFjZSgv77yML2csICIsIikgOiAnJzsKICAgICAgdGhpcy5tdWx0aVZhbHVlc1NwZWMgPSBjb250ZW50LnNwbGl0KCdcbicpLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiAhIXY7CiAgICAgIH0pOwogICAgICB0aGlzLm11bHRpVmFsdWVzU3BlYyA9IF90b0NvbnN1bWFibGVBcnJheShuZXcgU2V0KHRoaXMubXVsdGlWYWx1ZXNTcGVjKSk7CiAgICAgIGlmIChtdWx0aXBsZVJlZlNwZWNSZWYgJiYgbXVsdGlwbGVSZWZTcGVjUmVmLnNldFZhbHVlQXJyYXkpIHsKICAgICAgICBtdWx0aXBsZVJlZlNwZWNSZWYuc2V0VmFsdWVBcnJheSh0aGlzLm11bHRpVmFsdWVzU3BlYyk7CiAgICAgIH0KICAgIH0sCiAgICBnZXRQYXJhbTogZnVuY3Rpb24gZ2V0UGFyYW0oKSB7CiAgICAgIHZhciBwYXJhbXMgPSBfb2JqZWN0U3ByZWFkKHt9LCB0aGlzLnNlYXJjaEZvcm0pOwogICAgICB2YXIgZ2V0U3RyID0gZnVuY3Rpb24gZ2V0U3RyKHZhbHVlKSB7CiAgICAgICAgcmV0dXJuIHZhbHVlICYmIEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUuam9pbigiJiMmIikgOiB1bmRlZmluZWQ7CiAgICAgIH07CiAgICAgIGlmICh0aGlzLm11bHRpVmFsdWVzQ29kZS5sZW5ndGggPiAwKSB7CiAgICAgICAgcGFyYW1zWyJjb2RlcyJdID0gZ2V0U3RyKHRoaXMubXVsdGlWYWx1ZXNDb2RlKTsKICAgICAgfQogICAgICBpZiAodGhpcy5tdWx0aVZhbHVlc05hbWUubGVuZ3RoID4gMCkgewogICAgICAgIHBhcmFtc1sibmFtZXMiXSA9IGdldFN0cih0aGlzLm11bHRpVmFsdWVzTmFtZSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMubXVsdGlWYWx1ZXNTcGVjLmxlbmd0aCA+IDApIHsKICAgICAgICBwYXJhbXNbInNwZWNzIl0gPSBnZXRTdHIodGhpcy5tdWx0aVZhbHVlc1NwZWMpOwogICAgICB9CiAgICAgIHJldHVybiBwYXJhbXM7CiAgICB9LAogICAgZ2V0RGF0YVNvdXJjZTogZnVuY3Rpb24gZ2V0RGF0YVNvdXJjZSgpIHsKICAgICAgdmFyIGRhdGEgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IFtdOwogICAgICB2YXIgcmVzdWx0ID0gW107CiAgICAgIHZhciBpbmRleCA9IDE7CiAgICAgIHZhciBfaXRlcmF0b3IgPSBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcihkYXRhKSwKICAgICAgICBfc3RlcDsKICAgICAgdHJ5IHsKICAgICAgICB2YXIgX2xvb3AgPSBmdW5jdGlvbiBfbG9vcCgpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICBpdGVtWydpbmRleCddID0gaW5kZXgrKzsKICAgICAgICAgIGlmIChpdGVtICYmIGl0ZW0uZGV0YWlsTGlzdCkgewogICAgICAgICAgICBpZiAoaXRlbS5kZXRhaWxMaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgICAgIGl0ZW0uZGV0YWlsTGlzdCA9IFt7fV07CiAgICAgICAgICAgICAgcmVzdWx0LnB1c2goaXRlbSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdmFyIHJvd1NwYW4gPSBpdGVtLmRldGFpbExpc3QubGVuZ3RoOwogICAgICAgICAgICAgIGl0ZW0uZGV0YWlsTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChjaGlsZCwgaW5kZXgpIHsKICAgICAgICAgICAgICAgIHZhciBvYmogPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGl0ZW0pLCB7fSwgewogICAgICAgICAgICAgICAgICByb3dTcGFuOiBpbmRleCA9PT0gMCA/IHJvd1NwYW4gOiAwLAogICAgICAgICAgICAgICAgICBjaGlsZFNrdTogY2hpbGQgJiYgY2hpbGQuY2hpbGRTa3UsCiAgICAgICAgICAgICAgICAgIGNoaWxkU3BlYzogY2hpbGQgJiYgY2hpbGQuY2hpbGRTcGVjLAogICAgICAgICAgICAgICAgICBpdGVtUXVhbnRpdHk6IGNoaWxkICYmIGNoaWxkLnF1YW50aXR5CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHJlc3VsdC5wdXNoKG9iaik7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIGZvciAoX2l0ZXJhdG9yLnMoKTsgIShfc3RlcCA9IF9pdGVyYXRvci5uKCkpLmRvbmU7KSB7CiAgICAgICAgICBfbG9vcCgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IuZigpOwogICAgICB9CiAgICAgIHJldHVybiByZXN1bHQ7CiAgICB9LAogICAgaGFuZGxlU3BhbjogZnVuY3Rpb24gaGFuZGxlU3BhbihfcmVmOSkgewogICAgICB2YXIgcm93ID0gX3JlZjkucm93LAogICAgICAgIGNvbHVtbiA9IF9yZWY5LmNvbHVtbjsKICAgICAgaWYgKHRoaXMubWVyZ2VDb2x1bW5zLmluY2x1ZGVzKGNvbHVtbi5rZXkpKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIHJvd3NwYW46IHJvdy5yb3dTcGFuLAogICAgICAgICAgY29sc3BhbjogMQogICAgICAgIH07CiAgICAgIH0KICAgIH0sCiAgICAvL+afpeivogogICAgaGFuZGxlU2VhcmNoOiBmdW5jdGlvbiBoYW5kbGVTZWFyY2goKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBwYXJhbXMgPSB0aGlzLmdldFBhcmFtKCk7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIFByb2R1Y3QubGlzdFBhZ2UocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzICYmIHJlc1snY29kZSddID09PSAwKSB7CiAgICAgICAgICBfdGhpcy50YWJsZURhdGEgPSBfdGhpcy5nZXREYXRhU291cmNlKHJlcy5kYXRhLnJlY29yZHMgfHwgW10pOwogICAgICAgICAgX3RoaXMuc2VhcmNoRm9ybS50b3RhbCA9IHBhcnNlSW50KHJlcy5kYXRhLnRvdGFsKTsKICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlUmVzZXQ6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0KCkgewogICAgICAvL+mHjee9rumqjOivgQogICAgICB0aGlzLiRyZWZzWydzZWFyY2hGb3JtUmVmJ10ucmVzZXRGaWVsZHMoKTsKICAgICAgdGhpcy5zZWFyY2hGb3JtLnNob3BzID0gW107CiAgICAgIHRoaXMucmVzZXRNdWx0aXBsZSh0cnVlKTsKICAgIH0sCiAgICByZXNldE11bHRpcGxlOiBmdW5jdGlvbiByZXNldE11bHRpcGxlKCkgewogICAgICB2YXIgY2xlYXJUeHQgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IGZhbHNlOwogICAgICBpZiAoY2xlYXJUeHQgPT09IHRydWUpIHsKICAgICAgICB0aGlzLm11bHRpVmFsdWVzQ29kZSA9IFtdOwogICAgICAgIHZhciBtdWx0aXBsZVJlZkNvZGVSZWYgPSB0aGlzLiRyZWZzLm11bHRpcGxlUmVmQ29kZVJlZjsKICAgICAgICBpZiAobXVsdGlwbGVSZWZDb2RlUmVmICYmIG11bHRpcGxlUmVmQ29kZVJlZi5zZXRWYWx1ZUFycmF5KSB7CiAgICAgICAgICBtdWx0aXBsZVJlZkNvZGVSZWYuc2V0VmFsdWVBcnJheShbXSk7CiAgICAgICAgfQogICAgICAgIHRoaXMubXVsdGlWYWx1ZXNOYW1lID0gW107CiAgICAgICAgdmFyIG11bHRpcGxlUmVmTmFtZVJlZiA9IHRoaXMuJHJlZnMubXVsdGlwbGVSZWZOYW1lUmVmOwogICAgICAgIGlmIChtdWx0aXBsZVJlZk5hbWVSZWYgJiYgbXVsdGlwbGVSZWZOYW1lUmVmLnNldFZhbHVlQXJyYXkpIHsKICAgICAgICAgIG11bHRpcGxlUmVmTmFtZVJlZi5zZXRWYWx1ZUFycmF5KFtdKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5tdWx0aVZhbHVlc1NwZWMgPSBbXTsKICAgICAgICB2YXIgbXVsdGlwbGVSZWZTcGVjUmVmID0gdGhpcy4kcmVmcy5tdWx0aXBsZVJlZlNwZWNSZWY7CiAgICAgICAgaWYgKG11bHRpcGxlUmVmU3BlY1JlZiAmJiBtdWx0aXBsZVJlZlNwZWNSZWYuc2V0VmFsdWVBcnJheSkgewogICAgICAgICAgbXVsdGlwbGVSZWZTcGVjUmVmLnNldFZhbHVlQXJyYXkoW10pOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnBvcENvbnRlbnRDb2RlID0gdW5kZWZpbmVkOwogICAgICB0aGlzLnBvcFZpc2libGVDb2RlID0gZmFsc2U7CiAgICAgIHRoaXMucG9wQ29udGVudE5hbWUgPSB1bmRlZmluZWQ7CiAgICAgIHRoaXMucG9wVmlzaWJsZU5hbWUgPSBmYWxzZTsKICAgICAgdGhpcy5wb3BDb250ZW50U3BlYyA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5wb3BWaXNpYmxlU3BlYyA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZVBhZ2U6IGZ1bmN0aW9uIGhhbmRsZVBhZ2UoY3VycmVudCkgewogICAgICB0aGlzLnNlYXJjaEZvcm0ucGFnZSA9IGN1cnJlbnQ7CiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgICB9LAogICAgaGFuZGxlUGFnZVNpemU6IGZ1bmN0aW9uIGhhbmRsZVBhZ2VTaXplKHNpemUpIHsKICAgICAgdGhpcy5zZWFyY2hGb3JtLmxpbWl0ID0gc2l6ZTsKICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsKICAgIH0sCiAgICBoYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBwYXJhbXMgPSB0aGlzLmdldFBhcmFtKCk7CiAgICAgIHBhcmFtc1snZmlsZU5hbWUnXSA9ICLkuqflk4Hkv6Hmga/ooagiICsgbmV3IERhdGUoKS5nZXRUaW1lKCkgKyAiLnhscyI7CiAgICAgIFByb2R1Y3QuZXhwb3J0RmlsZShwYXJhbXMsIGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBvblN5bmM6IGZ1bmN0aW9uIG9uU3luYygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIC8v54K55Ye756Gu5a6aCiAgICAgIHRoaXMuc3luY0xvYWRpbmcgPSB0cnVlOwogICAgICB2YXIgY29udGVudCA9IHRoaXMuc3luY0Zvcm0uY29kZXMgPyB0aGlzLnN5bmNGb3JtLmNvZGVzLnRyaW0oKS5yZXBsYWNlKC/vvIwvZywgIiwiKSA6ICcnOwogICAgICB2YXIgY29kZXMgPSBjb250ZW50LnNwbGl0KCdcbicpLmZpbHRlcihmdW5jdGlvbiAodikgewogICAgICAgIHJldHVybiAhIXY7CiAgICAgIH0pLmpvaW4oIiwiKTsKICAgICAgUHJvZHVjdC5zeW5jUHJvZHVjdCh7CiAgICAgICAgImNvZGVzIjogY29kZXMKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcyAmJiByZXNbJ2NvZGUnXSA9PT0gMCkgewogICAgICAgICAgX3RoaXMzLiRNZXNzYWdlLnN1Y2Nlc3MoIuaPkOS6pOWQjOatpeS7u+WKoeaIkOWKnyzku7vliqHmraPlnKjmiafooYwiKTsKICAgICAgICAgIF90aGlzMy5vbkNhbmNlbCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczMuJE1lc3NhZ2UuZXJyb3IocmVzWydtZXNzYWdlJ10pOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMy5zeW5jTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBkb3duVGVtcGxhdGU6IGZ1bmN0aW9uIGRvd25UZW1wbGF0ZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBwYXJhbXMgPSB7fTsKICAgICAgcGFyYW1zWydmaWxlTmFtZSddID0gIue7hOWQiOWTgeWvvOWFpeaooeadvy54bHMiOwogICAgICBQcm9kdWN0LmRvd25UZW1wbGF0ZShwYXJhbXMsIGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRFcnJvcjogZnVuY3Rpb24gaGFuZGxlSW1wb3J0RXJyb3IoZXJyLCBmaWxlKSB7CiAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IoZmlsZS5tZXNzYWdlKTsKICAgIH0sCiAgICBoYW5kbGVJbXBvcnRTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVJbXBvcnRTdWNjZXNzKHJlcykgewogICAgICB0aGlzLiRyZWZzWyd1cGxvYWRGaWxlUmVmJ10uY2xlYXJGaWxlcygpOwogICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsKICAgICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlTWF4U2l6ZTogZnVuY3Rpb24gaGFuZGxlTWF4U2l6ZSgpIHsKICAgICAgdGhpcy4kTWVzc2FnZS53YXJuaW5nKCflpKflsI/kuI3og73otoXov4cxME0uJyk7CiAgICB9LAogICAgaGFuZGxlSW1wb3J0Rm9ybWF0RXJyb3I6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydEZvcm1hdEVycm9yKGZpbGUpIHsKICAgICAgLy/moLzlvI/pqozor4HlpLHotKXnmoTpkqnlrZAKICAgICAgdGhpcy4kTW9kYWwuZXJyb3IoewogICAgICAgIHRpdGxlOiAn5paH5Lu25qC85byP5LiN5q2j56GuJywKICAgICAgICBjb250ZW50OiAn5paH5Lu2ICcgKyBmaWxlLm5hbWUgKyAn5qC85byP5LiN5q2j56Gu77yM5pSv5oyB5LiK5Lyg55qE5paH5Lu257G75Z6L77yaeGxzLHhsc3gnLAogICAgICAgIG9rVGV4dDogJ+ehruiupCcKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["autoTableHeight", "getToken", "getUrl", "Multiple", "Product", "ShopSelect", "UpcCode", "name", "components", "data", "h", "$createElement", "syncVisible", "syncLoading", "syncForm", "loading", "multiValuesCode", "popVisibleCode", "popContentCode", "undefined", "multiValuesName", "popVisibleName", "popContentName", "multiValuesSpec", "popVisibleSpec", "popContentSpec", "mergeColumns", "statusList", "importURl", "path", "loginInfo", "Accept", "mode", "Authorization", "searchForm", "isCombo", "isThd", "page", "limit", "tableData", "columns", "title", "key", "width", "align", "resizable", "render", "_", "_ref", "row", "value", "min<PERSON><PERSON><PERSON>", "_ref2", "_ref3", "slot", "_ref4", "_ref5", "_ref6", "child<PERSON><PERSON>", "_ref7", "childSpec", "_ref8", "itemQuantity", "mounted", "handleSearch", "methods", "closeDropdownCode", "multipleRefCodeRef", "$refs", "content", "trim", "replace", "split", "filter", "v", "_toConsumableArray", "Set", "setValueArray", "closeDropdownName", "multipleRefNameRef", "closeDropdownSpec", "multipleRefSpecRef", "getPara<PERSON>", "params", "_objectSpread", "getStr", "Array", "isArray", "join", "length", "getDataSource", "arguments", "result", "index", "_iterator", "_createForOfIteratorHelper", "_step", "_loop", "item", "detailList", "push", "rowSpan", "for<PERSON>ach", "child", "obj", "quantity", "s", "n", "done", "err", "e", "f", "handleSpan", "_ref9", "column", "includes", "rowspan", "colspan", "_this", "listPage", "then", "res", "records", "total", "parseInt", "finally", "handleReset", "resetFields", "shops", "resetMultiple", "clearTxt", "handlePage", "current", "handlePageSize", "size", "handleExport", "_this2", "Date", "getTime", "exportFile", "onSync", "_this3", "codes", "syncProduct", "$Message", "success", "onCancel", "error", "catch", "downTemplate", "_this4", "handleImportError", "file", "message", "handleImportSuccess", "clearFiles", "code", "handleMaxSize", "warning", "handleImportFormatError", "$Modal", "okText"], "sources": ["src/view/module/base/product/index.vue"], "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 产品管理\r\n-->\r\n\r\n<template>\r\n  <div class=\"search-con-top\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"code\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesCode = values || []; }\" ref=\"multipleRefCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"name\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品名称(回车分隔)\" @changeValue=\"(values)=>{ multiValuesName = values || []; }\" ref=\"multipleRefNameRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleName=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleName\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentName\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownName\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"spec\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品规格(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSpec = values || []; }\" ref=\"multipleRefSpecRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSpec=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSpec\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSpec\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSpec\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"isCombo\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isCombo\" placeholder=\"是否组合品\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem prop=\"isThd\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isThd\" placeholder=\"是否同步\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\">\r\n        <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\"\r\n                :format=\"['xls', 'xlsx']\"\r\n                :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button type=\"primary\" >上传文件</Button>\r\n        </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Button @click=\"()=>{syncVisible=true;}\" class=\"buttonMargin\" :loading=\"loading\">同步数据</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" :span-method=\"handleSpan\">\r\n          <template v-slot:isCombo=\"{row}\">\r\n            <Badge v-for=\"v in statusList\" :status=\"v.key === 0?'success':'warning'\" :text=\"v.value\" v-if=\"v.key === row.isCombo\" v-bind:key=\"v.key\"></Badge>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <Modal :title=\"'同步产品信息'\" width=\"500px\" @on-cancel=\"()=>{syncVisible=false; $refs['syncFormRef'].resetFields();}\" @on-ok=\"onSync\" :value=\"syncVisible\" >\r\n        <Form ref=\"syncFormRef\" :model=\"syncForm\" :label-width=\"100\">\r\n          <FormItem label=\"销售Sku\" prop=\"codes\">\r\n            <Input v-model=\"syncForm.codes\" type=\"textarea\" placeholder=\"请输入内容,多个以逗号隔开,最多支持10个\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\"></div>\r\n        <template v-slot:footer=\"{}\">\r\n          <Button @click=\"()=>{syncVisible=false;$refs['syncFormRef'].resetFields();}\" :disabled=\"syncLoading\">取消</Button>\r\n          <Button type=\"primary\" @click=\"onSync\" :loading=\"syncLoading\" style=\"margin-left: 15px\">确认</Button>\r\n        </template>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { autoTableHeight} from \"@/libs/tools.js\";\r\nimport { getToken, getUrl } from '@/libs/util';\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport Product from \"@/api/base/product\";\r\nimport ShopSelect from \"_c/shopSelect/index.vue\";\r\nimport UpcCode from \"@/api/newApply/upcCode\";\r\nexport default {\r\n  name: \"productManage\",\r\n  components: {ShopSelect, Multiple},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      syncVisible:false,\r\n      syncLoading:false,\r\n      syncForm:{\"codes\":null},\r\n      loading: false,\r\n      multiValuesCode:[],\r\n      popVisibleCode:false,\r\n      popContentCode: undefined,\r\n      multiValuesName:[],\r\n      popVisibleName:false,\r\n      popContentName: undefined,\r\n      multiValuesSpec:[],\r\n      popVisibleSpec:false,\r\n      popContentSpec: undefined,\r\n      mergeColumns:['index','code','name','spec','isCombo','syncFlag','updateTime'],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"否\"},{\"key\":1,\"value\":\"是\"}],\r\n      importURl: getUrl() + Product.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      searchForm: {\r\n        isCombo:null,\r\n        isThd:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{title: '#', key: 'index',width: 60,align: 'center'},\r\n        {title: '编码',key: 'code',width: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['code']}>{row['code']}</span>)},\r\n        {title: '类型',key: 'name',minWidth: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['name']}>{row['name']}</span>)},\r\n        {title: '规格',key: 'spec',minWidth: 250,resizable:true,render: (_, { row }) => (<span v-copytext={row['spec']}>{row['spec']}</span>)},\r\n        {title: '是否组合品',key: 'isCombo',width: 120,resizable:true,slot:'isCombo'},\r\n        {title: '是否同步',key: 'syncFlag',width: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['syncFlag']}>{row['syncFlag']}</span>)},\r\n        {title: '同步时间',key: 'updateTime',width: 180,resizable:true,render:(_, { row }) => (<span v-copytext={row['updateTime']}>{row['updateTime']}</span>)},\r\n        {title: '子编码', align: \"center\",key: 'childSku', width: 120, render: (h, {row}) => (<span v-copytext={row.childSku}>{row.childSku}</span>)},\r\n        {title: '子规格', align: \"center\",key: 'childSpec', minWidth: 200, render: (h, {row}) => (<span v-copytext={row.childSpec}>{row.childSpec}</span>)},\r\n        {title: '子数量', align: \"center\",key: 'itemQuantity', width: 80, render: (h, {row}) => (<span v-copytext={row.itemQuantity}>{row.itemQuantity}</span>)},\r\n      ],\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    closeDropdownCode() { //关闭输入文本框\r\n      const { popContentCode } = this;\r\n      const { multipleRefCodeRef } = this.$refs;\r\n      this.popVisibleCode = false;\r\n      if(!popContentCode) return;\r\n      const content = popContentCode ? popContentCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesCode = [...new Set(this.multiValuesCode)];\r\n      if(multipleRefCodeRef && multipleRefCodeRef.setValueArray){\r\n        multipleRefCodeRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownName() { //关闭输入文本框\r\n      const { popContentName } = this;\r\n      const { multipleRefNameRef } = this.$refs;\r\n      this.popVisibleName = false;\r\n      if(!popContentName) return;\r\n      const content = popContentName ? popContentName.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesName = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesName = [...new Set(this.multiValuesName)];\r\n      if(multipleRefNameRef && multipleRefNameRef.setValueArray){\r\n        multipleRefNameRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownSpec() { //关闭输入文本框\r\n      const { popContentSpec } = this;\r\n      const { multipleRefSpecRef } = this.$refs;\r\n      this.popVisibleSpec = false;\r\n      if(!popContentSpec) return;\r\n      const content = popContentSpec ? popContentSpec.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSpec = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSpec = [...new Set(this.multiValuesSpec)];\r\n      if(multipleRefSpecRef && multipleRefSpecRef.setValueArray){\r\n        multipleRefSpecRef.setValueArray(this.multiValuesSpec);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesCode.length > 0){\r\n        params[\"codes\"] = getStr(this.multiValuesCode);\r\n      }\r\n      if (this.multiValuesName.length > 0){\r\n        params[\"names\"] = getStr(this.multiValuesName);\r\n      }\r\n      if (this.multiValuesSpec.length > 0){\r\n        params[\"specs\"] = getStr(this.multiValuesSpec);\r\n      }\r\n      return params;\r\n    },\r\n    getDataSource(data = []) {\r\n      const result = [];\r\n      let index = 1;\r\n      for (const item of data) {\r\n        item['index'] = index++;\r\n        if (item && item.detailList) {\r\n          if (item.detailList.length === 0){\r\n            item.detailList = [{}];\r\n            result.push(item);\r\n          }else{\r\n            let rowSpan = item.detailList.length;\r\n            item.detailList.forEach((child, index) => {\r\n              const obj = {\r\n                ...item,\r\n                rowSpan: index ===0?rowSpan:0,\r\n                childSku: child && child.childSku,\r\n                childSpec: child && child.childSpec,\r\n                itemQuantity:child && child.quantity\r\n              };\r\n              result.push(obj);\r\n            });\r\n          }\r\n        }\r\n      }\r\n      return result;\r\n    },\r\n    handleSpan({ row, column}) {\r\n      if (this.mergeColumns.includes(column.key)) {\r\n        return {\r\n          rowspan: row.rowSpan,\r\n          colspan: 1\r\n        };\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      Product.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = this.getDataSource(res.data.records || []);\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesCode = [];\r\n        const { multipleRefCodeRef } = this.$refs;\r\n        if (multipleRefCodeRef && multipleRefCodeRef.setValueArray) {\r\n          multipleRefCodeRef.setValueArray([]);\r\n        }\r\n        this.multiValuesName = [];\r\n        const { multipleRefNameRef } = this.$refs;\r\n        if (multipleRefNameRef && multipleRefNameRef.setValueArray) {\r\n          multipleRefNameRef.setValueArray([]);\r\n        }\r\n        this.multiValuesSpec = [];\r\n        const { multipleRefSpecRef } = this.$refs;\r\n        if (multipleRefSpecRef && multipleRefSpecRef.setValueArray) {\r\n          multipleRefSpecRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentCode = undefined;\r\n      this.popVisibleCode = false;\r\n      this.popContentName = undefined;\r\n      this.popVisibleName = false;\r\n      this.popContentSpec = undefined;\r\n      this.popVisibleSpec = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"产品信息表\" + new Date().getTime() + \".xls\";\r\n      Product.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    onSync(){\r\n      //点击确定\r\n      this.syncLoading = true;\r\n      const content = this.syncForm.codes ? this.syncForm.codes.trim().replace(/，/g, \",\") : '';\r\n      let codes = content.split('\\n').filter(v=>!!v).join(\",\");\r\n      Product.syncProduct({\"codes\":codes}).then((res)=>{\r\n        if(res && res['code'] ===0){\r\n          this.$Message.success(\"提交同步任务成功,任务正在执行\");\r\n          this.onCancel();\r\n        }else{\r\n          this.$Message.error(res['message'])\r\n        }\r\n      }).catch(()=>{}).finally(()=>{this.syncLoading = false;});\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"组合品导入模板.xls\";\r\n      Product.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleImportError (err, file) {\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAiHA,SAAAA,eAAA;AACA,SAAAC,QAAA,EAAAC,MAAA;AACA,OAAAC,QAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,OAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAH,UAAA,EAAAA,UAAA;IAAAF,QAAA,EAAAA;EAAA;EACAM,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA;MACAX,eAAA,EAAAA,eAAA;MACAY,WAAA;MACAC,WAAA;MACAC,QAAA;QAAA;MAAA;MACAC,OAAA;MACAC,eAAA;MACAC,cAAA;MACAC,cAAA,EAAAC,SAAA;MACAC,eAAA;MACAC,cAAA;MACAC,cAAA,EAAAH,SAAA;MACAI,eAAA;MACAC,cAAA;MACAC,cAAA,EAAAN,SAAA;MACAO,YAAA;MACAC,UAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;QAAA;QAAA;MAAA;MACAC,SAAA,EAAA1B,MAAA,KAAAE,OAAA,CAAAyB,IAAA;MACAC,SAAA;QACAC,MAAA;QACAC,IAAA;QACAC,aAAA,cAAAhC,QAAA;MACA;MACAiC,UAAA;QACAC,OAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,SAAA;MAAA;MACAC,OAAA;QAAAC,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAE,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAAR,KAAA;QAAAC,GAAA;QAAAS,QAAA;QAAAN,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAK,KAAA;UAAA,IAAAH,GAAA,GAAAG,KAAA,CAAAH,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAAR,KAAA;QAAAC,GAAA;QAAAS,QAAA;QAAAN,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAM,KAAA;UAAA,IAAAJ,GAAA,GAAAI,KAAA,CAAAJ,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAAR,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAE,SAAA;QAAAS,IAAA;MAAA,GACA;QAAAb,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAE,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAQ,KAAA;UAAA,IAAAN,GAAA,GAAAM,KAAA,CAAAN,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAAR,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAE,SAAA;QAAAC,MAAA,WAAAA,OAAAC,CAAA,EAAAS,KAAA;UAAA,IAAAP,GAAA,GAAAO,KAAA,CAAAP,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA;YAAA;UAAA,IAAAA,GAAA;QAAA;MAAA,GACA;QAAAR,KAAA;QAAAG,KAAA;QAAAF,GAAA;QAAAC,KAAA;QAAAG,MAAA,WAAAA,OAAApC,CAAA,EAAA+C,KAAA;UAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA,CAAAS;YAAA;UAAA,IAAAT,GAAA,CAAAS,QAAA;QAAA;MAAA,GACA;QAAAjB,KAAA;QAAAG,KAAA;QAAAF,GAAA;QAAAS,QAAA;QAAAL,MAAA,WAAAA,OAAApC,CAAA,EAAAiD,KAAA;UAAA,IAAAV,GAAA,GAAAU,KAAA,CAAAV,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA,CAAAW;YAAA;UAAA,IAAAX,GAAA,CAAAW,SAAA;QAAA;MAAA,GACA;QAAAnB,KAAA;QAAAG,KAAA;QAAAF,GAAA;QAAAC,KAAA;QAAAG,MAAA,WAAAA,OAAApC,CAAA,EAAAmD,KAAA;UAAA,IAAAZ,GAAA,GAAAY,KAAA,CAAAZ,GAAA;UAAA,OAAAvC,CAAA;YAAA;cAAAH,IAAA;cAAA2C,KAAA,EAAAD,GAAA,CAAAa;YAAA;UAAA,IAAAb,GAAA,CAAAa,YAAA;QAAA;MAAA;IAEA;EACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAA;MAAA;MACA,IAAAhD,cAAA,QAAAA,cAAA;MACA,IAAAiD,kBAAA,QAAAC,KAAA,CAAAD,kBAAA;MACA,KAAAlD,cAAA;MACA,KAAAC,cAAA;MACA,IAAAmD,OAAA,GAAAnD,cAAA,GAAAA,cAAA,CAAAoD,IAAA,GAAAC,OAAA;MACA,KAAAvD,eAAA,GAAAqD,OAAA,CAAAG,KAAA,OAAAC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAA1D,eAAA,GAAA2D,kBAAA,KAAAC,GAAA,MAAA5D,eAAA;MACA,IAAAmD,kBAAA,IAAAA,kBAAA,CAAAU,aAAA;QACAV,kBAAA,CAAAU,aAAA,MAAA7D,eAAA;MACA;IACA;IACA8D,iBAAA,WAAAA,kBAAA;MAAA;MACA,IAAAxD,cAAA,QAAAA,cAAA;MACA,IAAAyD,kBAAA,QAAAX,KAAA,CAAAW,kBAAA;MACA,KAAA1D,cAAA;MACA,KAAAC,cAAA;MACA,IAAA+C,OAAA,GAAA/C,cAAA,GAAAA,cAAA,CAAAgD,IAAA,GAAAC,OAAA;MACA,KAAAnD,eAAA,GAAAiD,OAAA,CAAAG,KAAA,OAAAC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAtD,eAAA,GAAAuD,kBAAA,KAAAC,GAAA,MAAAxD,eAAA;MACA,IAAA2D,kBAAA,IAAAA,kBAAA,CAAAF,aAAA;QACAE,kBAAA,CAAAF,aAAA,MAAA7D,eAAA;MACA;IACA;IACAgE,iBAAA,WAAAA,kBAAA;MAAA;MACA,IAAAvD,cAAA,QAAAA,cAAA;MACA,IAAAwD,kBAAA,QAAAb,KAAA,CAAAa,kBAAA;MACA,KAAAzD,cAAA;MACA,KAAAC,cAAA;MACA,IAAA4C,OAAA,GAAA5C,cAAA,GAAAA,cAAA,CAAA6C,IAAA,GAAAC,OAAA;MACA,KAAAhD,eAAA,GAAA8C,OAAA,CAAAG,KAAA,OAAAC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA;MACA,KAAAnD,eAAA,GAAAoD,kBAAA,KAAAC,GAAA,MAAArD,eAAA;MACA,IAAA0D,kBAAA,IAAAA,kBAAA,CAAAJ,aAAA;QACAI,kBAAA,CAAAJ,aAAA,MAAAtD,eAAA;MACA;IACA;IACA2D,QAAA,WAAAA,SAAA;MACA,IAAAC,MAAA,GAAAC,aAAA,KACA,KAAAlD,UAAA,CACA;MACA,IAAAmD,MAAA,YAAAA,OAAAnC,KAAA;QAAA,OAAAA,KAAA,IAAAoC,KAAA,CAAAC,OAAA,CAAArC,KAAA,IAAAA,KAAA,CAAAsC,IAAA,UAAArE,SAAA;MAAA;MACA,SAAAH,eAAA,CAAAyE,MAAA;QACAN,MAAA,YAAAE,MAAA,MAAArE,eAAA;MACA;MACA,SAAAI,eAAA,CAAAqE,MAAA;QACAN,MAAA,YAAAE,MAAA,MAAAjE,eAAA;MACA;MACA,SAAAG,eAAA,CAAAkE,MAAA;QACAN,MAAA,YAAAE,MAAA,MAAA9D,eAAA;MACA;MACA,OAAA4D,MAAA;IACA;IACAO,aAAA,WAAAA,cAAA;MAAA,IAAAjF,IAAA,GAAAkF,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAxE,SAAA,GAAAwE,SAAA;MACA,IAAAC,MAAA;MACA,IAAAC,KAAA;MAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAtF,IAAA;QAAAuF,KAAA;MAAA;QAAA,IAAAC,KAAA,YAAAA,MAAA;UAAA,IAAAC,IAAA,GAAAF,KAAA,CAAA9C,KAAA;UACAgD,IAAA,YAAAL,KAAA;UACA,IAAAK,IAAA,IAAAA,IAAA,CAAAC,UAAA;YACA,IAAAD,IAAA,CAAAC,UAAA,CAAAV,MAAA;cACAS,IAAA,CAAAC,UAAA;cACAP,MAAA,CAAAQ,IAAA,CAAAF,IAAA;YACA;cACA,IAAAG,OAAA,GAAAH,IAAA,CAAAC,UAAA,CAAAV,MAAA;cACAS,IAAA,CAAAC,UAAA,CAAAG,OAAA,WAAAC,KAAA,EAAAV,KAAA;gBACA,IAAAW,GAAA,GAAApB,aAAA,CAAAA,aAAA,KACAc,IAAA;kBACAG,OAAA,EAAAR,KAAA,SAAAQ,OAAA;kBACA3C,QAAA,EAAA6C,KAAA,IAAAA,KAAA,CAAA7C,QAAA;kBACAE,SAAA,EAAA2C,KAAA,IAAAA,KAAA,CAAA3C,SAAA;kBACAE,YAAA,EAAAyC,KAAA,IAAAA,KAAA,CAAAE;gBAAA,EACA;gBACAb,MAAA,CAAAQ,IAAA,CAAAI,GAAA;cACA;YACA;UACA;QACA;QApBA,KAAAV,SAAA,CAAAY,CAAA,MAAAV,KAAA,GAAAF,SAAA,CAAAa,CAAA,IAAAC,IAAA;UAAAX,KAAA;QAAA;MAoBA,SAAAY,GAAA;QAAAf,SAAA,CAAAgB,CAAA,CAAAD,GAAA;MAAA;QAAAf,SAAA,CAAAiB,CAAA;MAAA;MACA,OAAAnB,MAAA;IACA;IACAoB,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAhE,GAAA,GAAAgE,KAAA,CAAAhE,GAAA;QAAAiE,MAAA,GAAAD,KAAA,CAAAC,MAAA;MACA,SAAAxF,YAAA,CAAAyF,QAAA,CAAAD,MAAA,CAAAxE,GAAA;QACA;UACA0E,OAAA,EAAAnE,GAAA,CAAAoD,OAAA;UACAgB,OAAA;QACA;MACA;IACA;IACA;IACArD,YAAA,WAAAA,aAAA;MAAA,IAAAsD,KAAA;MACA,IAAAnC,MAAA,QAAAD,QAAA;MACA,KAAAnE,OAAA;MACAX,OAAA,CAAAmH,QAAA,CAAApC,MAAA,EAAAqC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAH,KAAA,CAAA/E,SAAA,GAAA+E,KAAA,CAAA5B,aAAA,CAAA+B,GAAA,CAAAhH,IAAA,CAAAiH,OAAA;UACAJ,KAAA,CAAApF,UAAA,CAAAyF,KAAA,GAAAC,QAAA,CAAAH,GAAA,CAAAhH,IAAA,CAAAkH,KAAA;QACA;MACA,GAAAE,OAAA;QACAP,KAAA,CAAAvG,OAAA;MACA;IACA;IACA+G,WAAA,WAAAA,YAAA;MACA;MACA,KAAA1D,KAAA,kBAAA2D,WAAA;MACA,KAAA7F,UAAA,CAAA8F,KAAA;MACA,KAAAC,aAAA;IACA;IACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,QAAA,GAAAvC,SAAA,CAAAF,MAAA,QAAAE,SAAA,QAAAxE,SAAA,GAAAwE,SAAA;MACA,IAAAuC,QAAA;QACA,KAAAlH,eAAA;QACA,IAAAmD,kBAAA,QAAAC,KAAA,CAAAD,kBAAA;QACA,IAAAA,kBAAA,IAAAA,kBAAA,CAAAU,aAAA;UACAV,kBAAA,CAAAU,aAAA;QACA;QACA,KAAAzD,eAAA;QACA,IAAA2D,kBAAA,QAAAX,KAAA,CAAAW,kBAAA;QACA,IAAAA,kBAAA,IAAAA,kBAAA,CAAAF,aAAA;UACAE,kBAAA,CAAAF,aAAA;QACA;QACA,KAAAtD,eAAA;QACA,IAAA0D,kBAAA,QAAAb,KAAA,CAAAa,kBAAA;QACA,IAAAA,kBAAA,IAAAA,kBAAA,CAAAJ,aAAA;UACAI,kBAAA,CAAAJ,aAAA;QACA;MACA;MACA,KAAA3D,cAAA,GAAAC,SAAA;MACA,KAAAF,cAAA;MACA,KAAAK,cAAA,GAAAH,SAAA;MACA,KAAAE,cAAA;MACA,KAAAI,cAAA,GAAAN,SAAA;MACA,KAAAK,cAAA;IACA;IACA2G,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAlG,UAAA,CAAAG,IAAA,GAAA+F,OAAA;MACA,KAAApE,YAAA;IACA;IACAqE,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAApG,UAAA,CAAAI,KAAA,GAAAgG,IAAA;MACA,KAAAtE,YAAA;IACA;IACAuE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAzH,OAAA;MACA,IAAAoE,MAAA,QAAAD,QAAA;MACAC,MAAA,6BAAAsD,IAAA,GAAAC,OAAA;MACAtI,OAAA,CAAAuI,UAAA,CAAAxD,MAAA;QACAqD,MAAA,CAAAzH,OAAA;MACA;IACA;IACA6H,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAhI,WAAA;MACA,IAAAwD,OAAA,QAAAvD,QAAA,CAAAgI,KAAA,QAAAhI,QAAA,CAAAgI,KAAA,CAAAxE,IAAA,GAAAC,OAAA;MACA,IAAAuE,KAAA,GAAAzE,OAAA,CAAAG,KAAA,OAAAC,MAAA,WAAAC,CAAA;QAAA,SAAAA,CAAA;MAAA,GAAAc,IAAA;MACApF,OAAA,CAAA2I,WAAA;QAAA,SAAAD;MAAA,GAAAtB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAoB,MAAA,CAAAG,QAAA,CAAAC,OAAA;UACAJ,MAAA,CAAAK,QAAA;QACA;UACAL,MAAA,CAAAG,QAAA,CAAAG,KAAA,CAAA1B,GAAA;QACA;MACA,GAAA2B,KAAA,iBAAAvB,OAAA;QAAAgB,MAAA,CAAAhI,WAAA;MAAA;IACA;IACAwI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAvI,OAAA;MACA,IAAAoE,MAAA;MACAA,MAAA;MACA/E,OAAA,CAAAiJ,YAAA,CAAAlE,MAAA;QACAmE,MAAA,CAAAvI,OAAA;MACA;IACA;IACAwI,iBAAA,WAAAA,kBAAA1C,GAAA,EAAA2C,IAAA;MACA,KAAAR,QAAA,CAAAG,KAAA,CAAAK,IAAA,CAAAC,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAjC,GAAA;MACA,KAAArD,KAAA,kBAAAuF,UAAA;MACA,IAAAlC,GAAA,CAAAmC,IAAA;QACA,KAAA5F,YAAA;MACA;QACA,KAAAgF,QAAA,CAAAG,KAAA,CAAA1B,GAAA,CAAAgC,OAAA;MACA;IACA;IACAI,aAAA,WAAAA,cAAA;MACA,KAAAb,QAAA,CAAAc,OAAA;IACA;IACAC,uBAAA,WAAAA,wBAAAP,IAAA;MACA;MACA,KAAAQ,MAAA,CAAAb,KAAA;QACA1G,KAAA;QACA4B,OAAA,UAAAmF,IAAA,CAAAjJ,IAAA;QACA0J,MAAA;MACA;IACA;EACA;AACA"}]}