{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\bussinessLog\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\bussinessLog\\index.vue", "mtime": 1752737748509}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAgCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/bussinessLog", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n        <FormItem prop=\"logType\">\r\n          <Select v-model=\"pageInfo.logType\" placeholder=\"日志类别\" style=\"width:160px;\">\r\n            <Option value=\"0\">所有</Option>\r\n            <Option v-for=\"item in logTypeList\"  :key=\"item\" :value=\"item\">{{item}}</option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"userName\">\r\n          <Input type=\"text\" v-model=\"pageInfo.userName\" placeholder=\"用户名\" />\r\n        </FormItem>\r\n        <FormItem prop=\"logModule\">\r\n          <Input type=\"text\" v-model=\"pageInfo.logModule\" placeholder=\"日志模块\" />\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm()\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <Table :border=\"true\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-if=\"row.status===1\" status=\"success\" text=\"有效\" />\r\n          <Badge v-else-if=\"row.status===0\" status=\"warning\" text=\"无效\" />\r\n        </template>\r\n      </Table>\r\n      <Page size=\"small\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n  </div>\r\n</template>\r\n<script>\r\nimport BussinessLog from '@/api/basic/BussinessLog'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\n\r\nexport default {\r\n  name: 'BussinessLog',\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading: false,\r\n      logTypeList:[],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        logType: '',\r\n        userName: '',\r\n        logModule:''\r\n      },\r\n      columns: [{\r\n          title: '日志类型',\r\n          key: 'logType',\r\n          width: 130\r\n        },\r\n        {\r\n          title: '状态码',\r\n          key: 'logStatusCode',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '日志模块',\r\n          key: 'logModule',\r\n          width: 150\r\n        },\r\n        {\r\n          title: '日志内容',\r\n          key: 'logContent'\r\n        },\r\n\r\n        {\r\n          title: '用户名',\r\n          key: 'userName',\r\n          width: 120\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100\r\n        },\r\n        {\r\n          title: '创建时间',\r\n          key: 'createTime',\r\n          width: 200\r\n        }\r\n      ],\r\n      data: []\r\n    }\r\n  },\r\n  methods: {\r\n    getLogTypeList(){\r\n        BussinessLog.getLogType().then(res => {\r\n          if(res['code'] ===0 && res.data.length>0){\r\n            res.data.forEach((item)=>{this.logTypeList.push(item)})\r\n          }\r\n        }).finally(() => {  })\r\n\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page\r\n      }\r\n      this.loading = true\r\n      BussinessLog.list({ page: this.pageInfo.page, limit: this.pageInfo.limit, logType: this.pageInfo.logType, userName: this.pageInfo.userName, logModule: this.pageInfo.logModule }).then(res => {\r\n        this.data = res.data.records\r\n        this.pageInfo.total = parseInt(res.data.total)\r\n      }).finally(() => { this.loading = false })\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current\r\n      this.handleSearch()\r\n    },\r\n    handleResetForm() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handlePageSize(size) {\r\n      if(size >0){\r\n        this.pageInfo.page = 1;\r\n        this.pageInfo.limit = size\r\n      }\r\n      this.handleSearch()\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.getLogTypeList();\r\n    this.handleSearch();\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}