{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue", "mtime": 1752737748520}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["autoTableHeight", "Category", "name", "components", "data", "loading", "spin", "id", "customModel", "declareColumn", "title", "key", "min<PERSON><PERSON><PERSON>", "align", "clearanceColumn", "declareData", "clearanceData", "props", "onCancel", "type", "Function", "modelViewVisible", "Boolean", "allData", "Array", "currencyList", "countryList", "methods", "<PERSON><PERSON><PERSON><PERSON>", "loadData", "_this", "get<PERSON>y", "then", "res", "for<PERSON>ach", "item", "parentId", "parentClassName", "categoryName", "length", "countryObj", "currencyObj", "finally", "ChangeImg", "mounted"], "sources": ["src/view/module/custom/base/customClass/indexView.vue"], "sourcesContent": ["<template>\r\n  <Modal :width=\"1040\" :value=\"modelViewVisible\" :mask-closable=\"false\" :title=\"'查看报关类目'\" @on-cancel=\"onCancel\">\r\n    <Spin :fix=\"true\" v-if=\"spin\">加载中...</Spin>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">上级目录:</span><span class=\"WordsRight\">{{customModel.parentClassName}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">类目名称:</span><span class=\"WordsRight\">{{customModel.className}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">产品型号:</span><span class=\"WordsRight\">{{customModel.categoryName}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">中文报关名:</span><span  class=\"WordsRight\">{{customModel.customNameCn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">英文报关名:</span><span class=\"WordsRight\">{{customModel.customNameEn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关海关编码:</span><span class=\"WordsRight\">{{customModel.hsCode}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">材质:</span><span class=\"WordsRight\">{{customModel.material}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">用途:</span><span class=\"WordsRight\">{{customModel.purpose}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关单位:</span><span class=\"WordsRight\">{{customModel.unit}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div style=\"width:400px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">申报要素</p>\r\n          <Table :border=\"true\" :columns=\"declareColumn\" :data=\"declareData\"></Table>\r\n        </Card>\r\n      </div>\r\n      <div style=\"width:600px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">清关资料</p>\r\n          <Table :border=\"true\" :columns=\"clearanceColumn\" :data=\"clearanceData\"></Table>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" @click=\"onCancel\">关闭</Button>&nbsp;\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport {autoTableHeight} from \"@/libs/tools\";\r\nimport Category from \"@/api/custom/customClass\";\r\nexport default {\r\n  name: 'CategoryView',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading:false,\r\n      spin:false,\r\n      id:null,\r\n      customModel:{},\r\n      declareColumn:[{title: '类型',key: 'decKey', minWidth: 120, align: 'center'},\r\n        {title: '内容', key: 'content',minWidth: 120, align: 'center'}],\r\n      clearanceColumn:[{title: '国家',key: 'countryName', minWidth: 120, align: 'center'},\r\n        {title: '清关编码', key: 'hsCode',minWidth: 120, align: 'center'},\r\n        {title: '清关价格', key: 'price',minWidth: 120, align: 'center'},\r\n        {title: '清关币种', key: 'currencyName',minWidth: 120, align: 'center'}],\r\n      declareData:[],\r\n      clearanceData:[],\r\n      data:[]\r\n    }\r\n  },\r\n  props: {\r\n    onCancel: { type: Function },\r\n    modelViewVisible: {\r\n      type: Boolean,\r\n    },\r\n    allData:{type:Array},\r\n    currencyList:{type:Array},\r\n    countryList:{type:Array},\r\n  },\r\n  methods: {\r\n    setDefault(id) {\r\n      this.id = id;\r\n      this.loadData();\r\n    },\r\n    loadData(){\r\n      this.customModel={};\r\n      this.spin = true;\r\n      Category.getBy({\"id\": this.id,\"calc\":true}).then(res=>{\r\n        if (res['code'] === 0) {\r\n          this.customModel = res.data;\r\n          this.allData.forEach(item => {\r\n            if (item['id'] === this.customModel.parentId) {\r\n              this.customModel.parentClassName = item['className'];\r\n            }\r\n          })\r\n          if(!this.customModel.categoryName){\r\n            this.customModel.categoryName=null;\r\n          }\r\n          this.declareData = res.data['declarationElementList'];\r\n          this.clearanceData = res.data['clearanceElementList'];\r\n          if(this.clearanceData && this.countryList.length>0){\r\n            let countryObj = {};\r\n            this.countryList.forEach(item=>countryObj[item['two_code']] = item['name_cn'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['countryName']= countryObj[item['country']]\r\n            })\r\n            let currencyObj = {};\r\n            this.currencyList.forEach(item=>currencyObj[item['id']] = item['name'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['currencyName']= currencyObj[item['currency']]\r\n            })\r\n          }\r\n        }\r\n      }).finally(()=>{this.spin = false;})\r\n    },\r\n    ChangeImg(){\r\n      this.spin = true;\r\n      this.loadData();\r\n    },\r\n  },\r\n  mounted: function () {\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.wordsBox{\r\n  display: flex;\r\n  margin-bottom: 5px;\r\n}\r\n.divWidth{\r\n  width:330px;\r\n}\r\n.wordLeft{\r\n  display: inline-block;\r\n  width: 100px;\r\n  font-weight: bold;\r\n  white-space:nowrap;\r\n}\r\n.WordsRight{\r\n  position: relative;\r\n  width: 220px;\r\n  word-wrap: break-word;\r\n  word-break: normal;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;AAsCA,SAAAA,eAAA;AACA,OAAAC,QAAA;AACA;EACAC,IAAA;EACAC,UAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAJ,eAAA,EAAAA,eAAA;MACAK,OAAA;MACAC,IAAA;MACAC,EAAA;MACAC,WAAA;MACAC,aAAA;QAAAC,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA;MACAC,eAAA;QAAAJ,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA;MACAE,WAAA;MACAC,aAAA;MACAZ,IAAA;IACA;EACA;EACAa,KAAA;IACAC,QAAA;MAAAC,IAAA,EAAAC;IAAA;IACAC,gBAAA;MACAF,IAAA,EAAAG;IACA;IACAC,OAAA;MAAAJ,IAAA,EAAAK;IAAA;IACAC,YAAA;MAAAN,IAAA,EAAAK;IAAA;IACAE,WAAA;MAAAP,IAAA,EAAAK;IAAA;EACA;EACAG,OAAA;IACAC,UAAA,WAAAA,WAAArB,EAAA;MACA,KAAAA,EAAA,GAAAA,EAAA;MACA,KAAAsB,QAAA;IACA;IACAA,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAtB,WAAA;MACA,KAAAF,IAAA;MACAL,QAAA,CAAA8B,KAAA;QAAA,WAAAxB,EAAA;QAAA;MAAA,GAAAyB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAH,KAAA,CAAAtB,WAAA,GAAAyB,GAAA,CAAA7B,IAAA;UACA0B,KAAA,CAAAP,OAAA,CAAAW,OAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,WAAAL,KAAA,CAAAtB,WAAA,CAAA4B,QAAA;cACAN,KAAA,CAAAtB,WAAA,CAAA6B,eAAA,GAAAF,IAAA;YACA;UACA;UACA,KAAAL,KAAA,CAAAtB,WAAA,CAAA8B,YAAA;YACAR,KAAA,CAAAtB,WAAA,CAAA8B,YAAA;UACA;UACAR,KAAA,CAAAf,WAAA,GAAAkB,GAAA,CAAA7B,IAAA;UACA0B,KAAA,CAAAd,aAAA,GAAAiB,GAAA,CAAA7B,IAAA;UACA,IAAA0B,KAAA,CAAAd,aAAA,IAAAc,KAAA,CAAAJ,WAAA,CAAAa,MAAA;YACA,IAAAC,UAAA;YACAV,KAAA,CAAAJ,WAAA,CAAAQ,OAAA,WAAAC,IAAA;cAAA,OAAAK,UAAA,CAAAL,IAAA,gBAAAA,IAAA;YAAA;YACAL,KAAA,CAAAd,aAAA,CAAAkB,OAAA,WAAAC,IAAA;cACAA,IAAA,kBAAAK,UAAA,CAAAL,IAAA;YACA;YACA,IAAAM,WAAA;YACAX,KAAA,CAAAL,YAAA,CAAAS,OAAA,WAAAC,IAAA;cAAA,OAAAM,WAAA,CAAAN,IAAA,UAAAA,IAAA;YAAA;YACAL,KAAA,CAAAd,aAAA,CAAAkB,OAAA,WAAAC,IAAA;cACAA,IAAA,mBAAAM,WAAA,CAAAN,IAAA;YACA;UACA;QACA;MACA,GAAAO,OAAA;QAAAZ,KAAA,CAAAxB,IAAA;MAAA;IACA;IACAqC,SAAA,WAAAA,UAAA;MACA,KAAArC,IAAA;MACA,KAAAuB,QAAA;IACA;EACA;EACAe,OAAA,WAAAA,QAAA,GACA;AACA"}]}