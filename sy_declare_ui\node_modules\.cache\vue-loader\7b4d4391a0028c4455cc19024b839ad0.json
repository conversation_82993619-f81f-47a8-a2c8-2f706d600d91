{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\components\\department-select\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\components\\department-select\\index.vue", "mtime": 1752737748479}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAsDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/department-select", "sourcesContent": ["<!--\r\n@create date 2020-03-02\r\n@desc 部门选择控件\r\n-->\r\n\r\n<template>\r\n  <div class=\"departmentSelect\">\r\n    <treeselect\r\n      v-model=\"myValue\"\r\n      :options=\"realTreeData\"\r\n      :default-expand-level=\"defaultExpandLevelR\"\r\n      :normalizer=\"\r\n        node => ({\r\n          id: node.id,\r\n          label: node.departmentName,\r\n          children: node.children\r\n        })\"\r\n      :placeholder=\"placeholder || '请选择'\"\r\n      :style=\"{ width }\"\r\n      :multiple=\"multiple\"\r\n      noChildrenText=\"无选项\"\r\n      noOptionsText=\"无匹配项\"\r\n      noResultsText=\"无匹配项\"\r\n      :clearable=\"true\"\r\n      :limit=\"limit || 1\"\r\n      :limitText=\"count => `+ ${count}`\"\r\n      :flat=\"true\"\r\n      :autoDeselectDescendants=\"true\"\r\n      :autoSelectDescendants=\"true\"\r\n      :disableBranchNodes=\"false\"\r\n      :alwaysOpen=\"false\"\r\n      :appendToBody=\"appendToBody\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n      @input=\"inputChange\"\r\n      :disabled=\"disabled\"\r\n    >\r\n      <template v-slot:value-label=\"{node}\">\r\n        <div :title=\"multiple? node.raw.departmentName : node.raw.fullPathName\">\r\n\r\n          {{ multiple || groupName === \"reimburse-department\" ? node.raw.departmentName\r\n              : node.raw.fullPathName }}\r\n        </div>\r\n      </template>\r\n      <template v-if=\"groupName === 'amz-operation-center' ||groupName === 'reimburse-department'\" v-slot:option-label=\"{ node }\">\r\n        <div :title=\"multiple? node.raw.departmentName: node.raw.fullPathName\">\r\n          {{multiple || groupName === \"reimburse-department\"? node.raw.departmentName : node.raw.departmentName}}\r\n        </div>\r\n      </template>\r\n    </treeselect>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\"; //字典查询接口\r\nimport Department from \"@/api/base/department\"; //部门信息接口\r\nexport default {\r\n  name: \"department-select\",\r\n  props: {\r\n    groupName: {\r\n      // 区分组件数据源的数据字典配置名称\r\n      type: String\r\n    },\r\n    parentId: {\r\n      // 父组件传入的parentId\r\n      type: String\r\n    },\r\n    companyId: {\r\n      // 父组件传入的companyId\r\n      type: String\r\n    },\r\n    defaultExpandLevel: {\r\n      // 大于等于两家公司选项时，默认展开的树状层级，只有一家公司的时候默认展开第一级，否则不展开\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    value: {\r\n      type: [String, Array, Number]\r\n    },\r\n    disabledCompany: {\r\n      // 多选模式是否可以选择公司，仅多选模式有效\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    width: {\r\n      // 控件宽度，默认210px\r\n      type: String,\r\n      default: \"210px\"\r\n    },\r\n    limit: {\r\n      // 多选模式下最多允许显示的标签数\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    appendToBody: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    placeholder: {\r\n      type: String\r\n    },\r\n    needNotGroup: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    groupDisabled: { type: Boolean, default: false }, //是否禁用level=3的组不能选择，在售Listing部分的部门选择有用到\r\n    disabled: { type: Boolean, default: false },\r\n    disabledLevel: { type: Number }\r\n  },\r\n  data() {\r\n    return {\r\n      treeData: [],\r\n      myValue: undefined,\r\n      multiple: true\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    const { groupName } = this;\r\n    // 字典获取\r\n    CommonApi.getDictionaryValueBy(\"department_select_group\",groupName).then(res => {\r\n      if (res && res[\"code\"] === 0){\r\n        const itemValue = JSON.parse(res.data);\r\n        const { isSingle } = itemValue; // 取到角色标志和部门id集合\r\n        const companyIds = itemValue.company ? itemValue.company.split(\",\") : []; // 配置的company ids\r\n        const parents = itemValue.parentId ? itemValue.parentId.split(\",\") : []; // 配置的parent ids\r\n        this.multiple = isSingle !== \"true\";\r\n        CommonApi.getAllCompany().then(res1 => {\r\n          if (res1 && Array.isArray(res1.data)) {\r\n            const allCompany =\r\n              companyIds.length > 0? res1.data.filter(v => companyIds.includes(v.id)) : res1.data;\r\n            Department.getAll().then(res2 => {\r\n                if (res2 && Array.isArray(res2.data)) {\r\n                  const allDeparts =parents.length > 0? res2.data.filter(v =>parents.includes(v.parentId) ||parents.includes(v.id)): res2.data;\r\n                  const treeData = this.getTreeData(allCompany, allDeparts);\r\n                  if (groupName === \"amz-operation-center\") {\r\n                    this.treeData = treeData[0] ? treeData[0].children : [];\r\n                  } else if (groupName === \"reimburse-department\") {\r\n                    const treeArr = [];\r\n                    treeData.forEach(v => {\r\n                      treeArr.push(...(v.children || []));\r\n                    });\r\n                    this.treeData = treeArr;\r\n                  } else this.treeData = treeData;\r\n                }\r\n              }).finally(() => {});\r\n          }\r\n        });\r\n      }\r\n    });\r\n  },\r\n\r\n  watch: {\r\n    value(newValue) {\r\n      //父组件传入\r\n      const { multiple } = this;\r\n      //多选模式下必须保证myValue是数组或undefined\r\n      this.myValue = multiple\r\n        ? Array.isArray(newValue)\r\n          ? newValue\r\n          : []\r\n        : newValue;\r\n    },\r\n    myValue(newValue) {\r\n      //本组件双向绑定\r\n      this.$emit(\"input\", newValue);\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    realTreeData() {\r\n      // 真正取的treeData数组\r\n      const { companyId, treeData, multiple } = this;\r\n      const companyIds = companyId ? companyId.split(\",\") : []; // 配置的company ids\r\n      if (companyId === \"0\" && multiple === false) {\r\n        // 级联单选时，不传id不提供任何选项\r\n        return [];\r\n      }\r\n      if (companyIds.length === 1) {\r\n        return (\r\n          (treeData.find(v => companyIds.includes(v.id)) || {}).children || []\r\n        );\r\n      }\r\n      let bindData = companyIds.length > 0? treeData.filter(v => companyIds.includes(v.id)): treeData;\r\n      if (this.needNotGroup)\r\n        bindData.map(item => {\r\n          item.children &&\r\n            item.children.map(items => {\r\n              delete items.children;\r\n              return items;\r\n            });\r\n          return item;\r\n        });\r\n      return bindData;\r\n    },\r\n    defaultExpandLevelR() {\r\n      const { defaultExpandLevel} = this;\r\n      return defaultExpandLevel;\r\n    }\r\n  },\r\n  methods: {\r\n    getTreeData(allCompany, allDeparts) {\r\n\r\n      const { disabledCompany, multiple } = this;\r\n      const resultArr = [];\r\n      const getTreeArr = arr => {\r\n        const treeArr = [];\r\n        for (const item of arr) {\r\n          if (item.parentId === \"0\") {\r\n            treeArr.push(item);\r\n          }\r\n          for (const child of arr) {\r\n            child.isDisabled = this.groupDisabled ? child.level >= 3 : false;\r\n            if (this.disabledLevel && child.level === this.disabledLevel) {\r\n              child.isDisabled = true;\r\n            }\r\n            if (child.parentId === item.id) {\r\n              if (!item.children) {\r\n                item.children = [];\r\n                item.isDisabled = false;\r\n              }\r\n              item.children.push(child);\r\n            }\r\n          }\r\n        }\r\n        return treeArr;\r\n      };\r\n      for (const item of allCompany) {\r\n        const obj = {\r\n          departmentName: item.companyName,\r\n          id: item.id,\r\n          companyNameSimple: item.companyNameSimple,\r\n          children: [],\r\n          isDisabled: multiple ? disabledCompany : true\r\n        };\r\n        const children = allDeparts.filter(v => v.companyId === item.id);\r\n        obj.children = getTreeArr(children);\r\n        resultArr.push(obj);\r\n      }\r\n      return resultArr;\r\n    },\r\n\r\n    onOpen() {\r\n      this.$emit(\"open\");\r\n    },\r\n    onClose(value) {\r\n      this.$emit(\"close\", value);\r\n    },\r\n    inputChange(value) {\r\n      this.$emit(\"inputChange\", value);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" src=\"./index.less\" />\r\n"]}]}