package com.sy.erp.server.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
public class TransactionConfig {
    @Bean(name = "aimoProdMysqlDataSourceTransactionManager")
    public DataSourceTransactionManager aimoProdMysqlDataSourceTransactionManager(
            @Autowired @Qualifier("aimoProdMysql") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "aimoTestMysqlDataSourceTransactionManager")
    public DataSourceTransactionManager aimoTestMysqlDataSourceTransactionManager(
            @Autowired @Qualifier("aimoTestMysql") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "transactionManager")
    public DataSourceTransactionManager transactionManager(
            @Autowired @Qualifier("dataSource") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

}
