{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\funElement.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\funElement.js", "mtime": 1753778904265}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "API_BASE", "getList", "params", "url", "concat", "method", "getDetail", "id", "create", "data", "headers", "update", "delete", "_delete", "batchDelete", "ids", "getByFunType", "funType", "checkElement", "element"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/funElement.js"], "sourcesContent": ["import request from '@/libs/request'\nconst API_BASE = '/base/fun-element'\n\nexport default {\n  // 获取字段列表\n  getList(params) {\n    return request({\n      url: `${API_BASE}/list`,\n      method: 'get',\n      params\n    })\n  },\n\n  // 获取字段详情\n  getDetail(id) {\n    return request({\n      url: `${API_BASE}/${id}`,\n      method: 'get'\n    })\n  },\n\n  // 新增字段\n  create(data) {\n    return request({\n      url: API_BASE,\n      method: 'post',\n      data,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n  },\n\n  // 更新字段\n  update(id, data) {\n    return request({\n      url: `${API_BASE}/${id}`,\n      method: 'put',\n      data,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n  },\n\n  // 删除字段\n  delete(id) {\n    return request({\n      url: `${API_BASE}/${id}`,\n      method: 'delete'\n    })\n  },\n\n  // 批量删除字段\n  batchDelete(ids) {\n    return request({\n      url: `${API_BASE}/batch`,\n      method: 'delete',\n      data: ids,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n  },\n\n  // 根据功能类型获取字段列表\n  getByFunType(funType) {\n    return request({\n      url: `${API_BASE}/by-fun-type/${funType}`,\n      method: 'get'\n    })\n  },\n\n  // 检查字段英文是否存在\n  checkElement(element, funType, id) {\n    return request({\n      url: `${API_BASE}/check-element`,\n      method: 'get',\n      params: { element, funType, id }\n    })\n  }\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,IAAMC,QAAQ,GAAG,mBAAmB;AAEpC,eAAe;EACb;EACAC,OAAO,WAAAA,QAACC,MAAM,EAAE;IACd,OAAOH,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,UAAO;MACvBK,MAAM,EAAE,KAAK;MACbH,MAAM,EAANA;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,SAAS,WAAAA,UAACC,EAAE,EAAE;IACZ,OAAOR,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,OAAAI,MAAA,CAAIG,EAAE,CAAE;MACxBF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAG,MAAM,WAAAA,OAACC,IAAI,EAAE;IACX,OAAOV,OAAO,CAAC;MACbI,GAAG,EAAEH,QAAQ;MACbK,MAAM,EAAE,MAAM;MACdI,IAAI,EAAJA,IAAI;MACJC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,MAAM,WAAAA,OAACJ,EAAE,EAAEE,IAAI,EAAE;IACf,OAAOV,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,OAAAI,MAAA,CAAIG,EAAE,CAAE;MACxBF,MAAM,EAAE,KAAK;MACbI,IAAI,EAAJA,IAAI;MACJC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,MAAM,WAAAC,QAACN,EAAE,EAAE;IACT,OAAOR,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,OAAAI,MAAA,CAAIG,EAAE,CAAE;MACxBF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAS,WAAW,WAAAA,YAACC,GAAG,EAAE;IACf,OAAOhB,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,WAAQ;MACxBK,MAAM,EAAE,QAAQ;MAChBI,IAAI,EAAEM,GAAG;MACTL,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAM,YAAY,WAAAA,aAACC,OAAO,EAAE;IACpB,OAAOlB,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,mBAAAI,MAAA,CAAgBa,OAAO,CAAE;MACzCZ,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAa,YAAY,WAAAA,aAACC,OAAO,EAAEF,OAAO,EAAEV,EAAE,EAAE;IACjC,OAAOR,OAAO,CAAC;MACbI,GAAG,KAAAC,MAAA,CAAKJ,QAAQ,mBAAgB;MAChCK,MAAM,EAAE,KAAK;MACbH,MAAM,EAAE;QAAEiB,OAAO,EAAPA,OAAO;QAAEF,OAAO,EAAPA,OAAO;QAAEV,EAAE,EAAFA;MAAG;IACjC,CAAC,CAAC;EACJ;AACF,CAAC"}]}