{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\plugin\\error-store\\index.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\plugin\\error-store\\index.js", "mtime": 1752737748503}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwpleHBvcnQgZGVmYXVsdCB7CiAgaW5zdGFsbDogZnVuY3Rpb24gaW5zdGFsbChWdWUsIG9wdGlvbnMpIHsKICAgIGlmIChvcHRpb25zLmRldmVsb3BtZW50T2ZmICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSByZXR1cm47CiAgICBWdWUuY29uZmlnLmVycm9ySGFuZGxlciA9IGZ1bmN0aW9uIChlcnJvciwgdm0sIG1lcykgewogICAgICB2YXIgaW5mbyA9IHsKICAgICAgICB0eXBlOiAnc2NyaXB0JywKICAgICAgICBjb2RlOiAwLAogICAgICAgIG1lczogZXJyb3IubWVzc2FnZSwKICAgICAgICB1cmw6IHdpbmRvdy5sb2NhdGlvbi5ocmVmCiAgICAgIH07CiAgICAgIFZ1ZS5uZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7CiAgICAgICAgLy8gc3RvcmUuZGlzcGF0Y2goJ2FkZEVycm9yTG9nJywgaW5mbykKICAgICAgfSk7CiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["store", "install", "<PERSON><PERSON>", "options", "developmentOff", "process", "env", "NODE_ENV", "config", "<PERSON><PERSON><PERSON><PERSON>", "error", "vm", "mes", "info", "type", "code", "message", "url", "window", "location", "href", "nextTick", "console"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/plugin/error-store/index.js"], "sourcesContent": ["import store from '@/store'\r\nexport default {\r\n  install (Vue, options) {\r\n    if (options.developmentOff && process.env.NODE_ENV === 'development') return\r\n    Vue.config.errorHandler = (error, vm, mes) => {\r\n      let info = {\r\n        type: 'script',\r\n        code: 0,\r\n        mes: error.message,\r\n        url: window.location.href\r\n      }\r\n      Vue.nextTick(() => {\r\n        console.error(error)\r\n       // store.dispatch('addErrorLog', info)\r\n      })\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,eAAe;EACbC,OAAO,WAAAA,QAAEC,GAAG,EAAEC,OAAO,EAAE;IACrB,IAAIA,OAAO,CAACC,cAAc,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IACtEL,GAAG,CAACM,MAAM,CAACC,YAAY,GAAG,UAACC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAK;MAC5C,IAAIC,IAAI,GAAG;QACTC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,CAAC;QACPH,GAAG,EAAEF,KAAK,CAACM,OAAO;QAClBC,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC;MACDlB,GAAG,CAACmB,QAAQ,CAAC,YAAM;QACjBC,OAAO,CAACZ,KAAK,CAACA,KAAK,CAAC;QACrB;MACD,CAAC,CAAC;IACJ,CAAC;EACH;AACF,CAAC"}]}