package com.sy.erp.server.configuration;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum DataSourceEnum {
    /**
     * 爱墨正式环境数据库
     */
    AimoProdMysql("sy_cloud_prod_mysql"), // 10.168.3.227 sy_cloud
    /**
     * 爱墨测试环境数据库,当前系统主数据库
     */
    AimoTestMysql("sy_cloud_test_mysql"); // 10.168.1.40 sy_cloud
    public static final Map<String, DataSourceEnum> dataSourceMap = new HashMap<>();

    static {
        dataSourceMap.putAll(Arrays.stream(DataSourceEnum.values()).collect(Collectors.toMap(DataSourceEnum::getValue, Function.identity())));
    }

    private final String value;

    DataSourceEnum(String value) {
        this.value = value;
    }

    public static DataSourceEnum byValue(String dateType) {
        return Optional.ofNullable(dataSourceMap.get(dateType)).orElse(AimoTestMysql);
    }

}
