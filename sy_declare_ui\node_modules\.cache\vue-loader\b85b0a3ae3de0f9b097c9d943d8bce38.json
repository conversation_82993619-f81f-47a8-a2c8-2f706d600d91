{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue?vue&type=style&index=0&id=336cb1a0&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue", "mtime": 1752737748521}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouRmluYW5jZUJhc2ljc19ib3h7DQogIC5pdnUtdGFicy5pdnUtdGFicy1jYXJkew0KICAgIC5pdnUtdGFicy1iYXJ7DQogICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIH0NCiAgICAuaXZ1LXRhYnMtbmF2LXByZXZ7DQogICAgICAgIGxlZnQ6LTEycHg7DQogICAgfQ0KICAgIC5pdnUtdGFicy1uYXYtbmV4dHsNCiAgICAgICAgcmlnaHQ6IC04cHg7DQogICAgfQ0KICB9DQp9DQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base", "sourcesContent": ["<!--\r\n@create date 2020-09-04\r\n<AUTHOR>\r\n@desc 报关基础设置\r\n-->\r\n<template>\r\n    <div class=\"FinanceBasics_box\">\r\n        <Tabs type=\"card\" name=\"home\"  v-model=\"tabType\" @on-click=\"changeTabs\">\r\n            <TabPane label=\"报关类目\"  name=\"category\"><CustomClass v-if=\"customClassShow\"/></TabPane>\r\n            <TabPane label=\"清关连接\"  name=\"clearanceLink\"><ClearanceLink v-if=\"clearanceLinkShow\"/></TabPane>\r\n            <TabPane label=\"清关税号维护表\"  name=\"vatNo\"><VatNo v-if=\"vatNoShow\"/></TabPane>\r\n            <TabPane label=\"仓库地址\"  name=\"whAddress\" ><WhAddress v-if=\"whAddressSHow\"/></TabPane>\r\n        </Tabs>\r\n    </div>\r\n</template>\r\n<script>\r\nimport CustomClass from './customClass/index.vue'; //报关类目模块\r\nimport VatNo from './vatNo/index.vue'; //清关税号维护表\r\nimport WhAddress from  './whAddress/index.vue';//物流商渠道\r\nimport ClearanceLink from './clearanceLink/index.vue';//清关连接\r\nexport default {\r\n    name:'customBase',\r\n    components:{CustomClass,VatNo,WhAddress,ClearanceLink},\r\n    data(){\r\n        return{\r\n          tabType:'category',\r\n          customClassShow:true,\r\n          vatNoShow:false,\r\n          clearanceLinkShow:false,\r\n          whAddressSHow:false,\r\n        }\r\n    },\r\n    mounted(){\r\n    },\r\n    methods:{\r\n      changeTabs(name){\r\n        if (name === \"category\" && this.customClassShow === false) {\r\n          this.customClassShow = true;\r\n        }\r\n        if (name === \"clearanceLink\" && this.clearanceLinkShow === false) {\r\n          this.clearanceLinkShow = true;\r\n        }\r\n        if (name === \"vatNo\" && this.vatNoShow === false) {\r\n          this.vatNoShow = true;\r\n        }\r\n        if (name === \"whAddress\" && this.whAddressSHow === false) {\r\n          this.whAddressSHow = true;\r\n        }\r\n      },\r\n    }\r\n}\r\n</script>\r\n<style  lang=\"less\">\r\n.FinanceBasics_box{\r\n  .ivu-tabs.ivu-tabs-card{\r\n    .ivu-tabs-bar{\r\n      margin-bottom: 0;\r\n    }\r\n    .ivu-tabs-nav-prev{\r\n        left:-12px;\r\n    }\r\n    .ivu-tabs-nav-next{\r\n        right: -8px;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n"]}]}