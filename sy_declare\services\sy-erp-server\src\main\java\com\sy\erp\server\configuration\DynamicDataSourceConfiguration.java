package com.sy.erp.server.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;

import javax.sql.DataSource;

//@Configuration  // 禁用动态数据源配置，改用Nacos统一配置
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "false")  // 永远不启用
public class DynamicDataSourceConfiguration {


    @Bean(name = "aimoProdMysql")
    @ConfigurationProperties(prefix = "spring.datasource.aimo.prod.mysql")
    public DataSource aimoProdMysql() {
        return new HikariDataSource();
    }

    @Bean(name = "aimoTestMysql")
    @ConfigurationProperties(prefix = "spring.datasource.aimo.test.mysql")
    public DataSource aimoTestMysql() {
        return new HikariDataSource();
    }



    @Bean(name = "dataSource")
    @DependsOn("aimoProdMysql")
    public DataSource dataSource() {
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setAimoProdMysqlDataSource(aimoProdMysql());
        dataSource.setAimoTestMysqlDataSource(aimoTestMysql());
        return dataSource;
    }
}
