{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\clearanceLink.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\clearanceLink.js", "mtime": 1752737748404}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwppbXBvcnQgeyBOZXNwb3N0UmVxdWVzdCB9IGZyb20gJ0AvbGlicy9heGlvcy5qcyc7CmltcG9ydCBleHBvcnRSZXF1ZXN0IGZyb20gIkAvbGlicy9leHBvcnRSZXF1ZXN0IjsKdmFyIHdoQWRkcmVzc1BhdGggPSAiL2Jhc2UvY2xlYXJhbmNlTGluayI7Ci8qKg0KICog6I635Y+W5YiG6aG15pWw5o2uDQogKi8KdmFyIGxpc3RQYWdlID0gZnVuY3Rpb24gbGlzdFBhZ2UocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiB3aEFkZHJlc3NQYXRoICsgJy9saXN0UGFnZScsCiAgICBwYXJhbXM6IHBhcmFtcywKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CnZhciByZUNhbGNMaW5rID0gZnVuY3Rpb24gcmVDYWxjTGluayhwYXJhbXMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IHdoQWRkcmVzc1BhdGggKyAnL3JlQ2FsY0xpbmsnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9OwovKioNCiAqIOa3u+WKoOaVsOaNrg0KICovCnZhciBzYXZlQ2xlYXJhbmNlTGluayA9IGZ1bmN0aW9uIHNhdmVDbGVhcmFuY2VMaW5rKHBhcmFtcykgewogIHJldHVybiBOZXNwb3N0UmVxdWVzdCh3aEFkZHJlc3NQYXRoICsgJy9zYXZlQ2xlYXJhbmNlTGluaycsIHBhcmFtcyk7Cn07Ci8qKg0KICog6I635Y+W5pel5b+XDQogKi8KdmFyIGdldExvZ1JlZlR5cGUgPSBmdW5jdGlvbiBnZXRMb2dSZWZUeXBlKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogd2hBZGRyZXNzUGF0aCArICcvZ2V0TG9nUmVmVHlwZScsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CnZhciBkb3dubG9hZCA9IGZ1bmN0aW9uIGRvd25sb2FkKHBhcmFtcywgY2FsbGJhY2spIHsKICB2YXIgY29uZmlnID0gewogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgZmlsZU5hbWU6IHBhcmFtc1snZmlsZU5hbWUnXQogIH07CiAgcmV0dXJuIGV4cG9ydFJlcXVlc3Qod2hBZGRyZXNzUGF0aCArICcvZG93bmxvYWQnLCBjb25maWcsIGNhbGxiYWNrKTsKfTsKdmFyIGRvd25sb2FkVGVtcGxhdGUgPSBmdW5jdGlvbiBkb3dubG9hZFRlbXBsYXRlKHBhcmFtcywgY2FsbGJhY2spIHsKICB2YXIgY29uZmlnID0gewogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgZmlsZU5hbWU6IHBhcmFtc1snZmlsZU5hbWUnXQogIH07CiAgcmV0dXJuIGV4cG9ydFJlcXVlc3Qod2hBZGRyZXNzUGF0aCArICcvZG93bmxvYWRUZW1wbGF0ZScsIGNvbmZpZywgY2FsbGJhY2spOwp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbGlzdFBhZ2U6IGxpc3RQYWdlLAogIHJlQ2FsY0xpbms6IHJlQ2FsY0xpbmssCiAgc2F2ZUNsZWFyYW5jZUxpbms6IHNhdmVDbGVhcmFuY2VMaW5rLAogIGdldExvZ1JlZlR5cGU6IGdldExvZ1JlZlR5cGUsCiAgZG93bmxvYWQ6IGRvd25sb2FkLAogIGRvd25sb2FkVGVtcGxhdGU6IGRvd25sb2FkVGVtcGxhdGUKfTs="}, {"version": 3, "names": ["request", "NespostRequest", "exportRequest", "wh<PERSON><PERSON><PERSON><PERSON><PERSON>", "listPage", "params", "url", "method", "reCalcLink", "saveClearanceLink", "getLogRefType", "download", "callback", "config", "fileName", "downloadTemplate"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/custom/clearanceLink.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport {NespostRequest} from '@/libs/axios.js';\r\nimport exportRequest from \"@/libs/exportRequest\";\r\n\r\nconst whAddressPath = \"/base/clearanceLink\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: whAddressPath + '/listPage',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\nconst reCalcLink =(params) => {\r\n  return request({\r\n    url: whAddressPath + '/reCalcLink',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst saveClearanceLink = (params) => {\r\n  return NespostRequest(whAddressPath + '/saveClearanceLink', params)\r\n}\r\n/**\r\n * 获取日志\r\n */\r\nconst getLogRefType = () => {\r\n  return request({\r\n    url: whAddressPath + '/getLogRefType',\r\n    method: 'get'\r\n  })\r\n}\r\nconst download = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(whAddressPath + '/download',config,callback);\r\n}\r\nconst downloadTemplate = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(whAddressPath + '/downloadTemplate',config,callback);\r\n}\r\nexport default {\r\n  listPage,\r\n  reCalcLink,\r\n  saveClearanceLink,\r\n  getLogRefType,\r\n  download,\r\n  downloadTemplate\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAAQC,cAAc,QAAO,iBAAiB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,IAAMC,aAAa,GAAG,qBAAqB;AAC3C;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,aAAa,GAAG,WAAW;IAChCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMC,UAAU,GAAE,SAAZA,UAAUA,CAAGH,MAAM,EAAK;EAC5B,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,aAAa,GAAG,aAAa;IAClCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAME,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIJ,MAAM,EAAK;EACpC,OAAOJ,cAAc,CAACE,aAAa,GAAG,oBAAoB,EAAEE,MAAM,CAAC;AACrE,CAAC;AACD;AACA;AACA;AACA,IAAMK,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,OAAOV,OAAO,CAAC;IACbM,GAAG,EAAEH,aAAa,GAAG,gBAAgB;IACrCI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAIN,MAAM,EAACO,QAAQ,EAAG;EAClC,IAAMC,MAAM,GAAG;IACbR,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbO,QAAQ,EAACT,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,aAAa,GAAG,WAAW,EAACU,MAAM,EAACD,QAAQ,CAAC;AACnE,CAAC;AACD,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIV,MAAM,EAACO,QAAQ,EAAG;EAC1C,IAAMC,MAAM,GAAG;IACbR,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbO,QAAQ,EAACT,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,aAAa,GAAG,mBAAmB,EAACU,MAAM,EAACD,QAAQ,CAAC;AAC3E,CAAC;AACD,eAAe;EACbR,QAAQ,EAARA,QAAQ;EACRI,UAAU,EAAVA,UAAU;EACVC,iBAAiB,EAAjBA,iBAAiB;EACjBC,aAAa,EAAbA,aAAa;EACbC,QAAQ,EAARA,QAAQ;EACRI,gBAAgB,EAAhBA;AACF,CAAC"}]}