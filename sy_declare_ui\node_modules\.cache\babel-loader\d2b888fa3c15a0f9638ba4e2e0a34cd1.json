{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\router\\routers.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\router\\routers.js", "mtime": 1753761804304}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Main", "path", "name", "meta", "title", "hideInMenu", "component", "redirect", "notCache", "children", "icon", "hideInBread"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/router/routers.js"], "sourcesContent": ["import Main from '@/components/main'\r\n/**\r\n * iview-admin中meta除了原生参数外可配置的参数:\r\n * meta: {\r\n *  title: { String|Number|Function }\r\n *         显示在侧边栏、面包屑和标签栏的文字\r\n *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;\r\n *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由\r\n *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置\r\n *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项\r\n *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致\r\n *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由\r\n *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'\r\n *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数\r\n * }\r\n */\r\n\r\nexport default [{\r\n        path: '/login',\r\n        name: 'login',\r\n        meta: {\r\n            title: 'Login - 登录',\r\n            hideInMenu: true\r\n        },\r\n        component: () =>\r\n            import ('@/view/login/login.vue')\r\n    },\r\n    {\r\n        path: '/',\r\n        name: '_home',\r\n        redirect: '/home',\r\n        component: Main,\r\n        meta: {\r\n            hideInMenu: true,\r\n            notCache: true\r\n        },\r\n        children: [{\r\n            path: '/home',\r\n            name: 'home',\r\n            meta: {\r\n                hideInMenu: true,\r\n                title: '首页',\r\n                notCache: true,\r\n                icon: 'md-home'\r\n            },\r\n        }]\r\n    },\r\n    {\r\n        path: '/account',\r\n        name: 'account',\r\n        component: Main,\r\n        meta: {\r\n            notCache: true,\r\n            hideInBread: true,\r\n            hideInMenu: true\r\n        },\r\n        children: [{\r\n            path: 'setting',\r\n            name: 'setting',\r\n            meta: {\r\n                icon: 'md-notifications',\r\n                title: '个人设置'\r\n            },\r\n            component: () =>\r\n                import ('@/view/module/account/setting.vue')\r\n        }]\r\n    },\r\n    {\r\n        path: '/base',\r\n        name: 'base',\r\n        component: Main,\r\n        meta: {\r\n            notCache: true,\r\n            hideInBread: true,\r\n            hideInMenu: true\r\n        },\r\n        children: [{\r\n            path: 'workflow/add/:id?',\r\n            name: 'workflow-add',\r\n            meta: {\r\n                title: '流程设计器',\r\n                hideInMenu: true\r\n            },\r\n            component: () =>\r\n                import ('@/view/module/base/workflow/add.vue')\r\n        }]\r\n    },\r\n    {\r\n        path: '/error_logger',\r\n        name: 'error_logger',\r\n        meta: {\r\n            hideInBread: true,\r\n            hideInMenu: true\r\n        },\r\n        component: Main,\r\n        children: [{\r\n            path: 'error_logger_page',\r\n            name: 'error_logger_page',\r\n            meta: {\r\n                icon: 'ios-bug',\r\n                title: '错误收集'\r\n            },\r\n            component: () =>\r\n                import ('@/view/error-page/error-logger.vue')\r\n        }]\r\n    },\r\n    {\r\n        path: '/login/success',\r\n        name: 'loginSuccess',\r\n        meta: {\r\n            hideInMenu: true\r\n        },\r\n        component: () =>\r\n            import ('@/view/login/login-success.vue')\r\n    },\r\n\r\n    {\r\n        path: '/401',\r\n        name: 'error_401',\r\n        meta: {\r\n            hideInMenu: true\r\n        },\r\n        component: () =>\r\n            import ('@/view/error-page/401.vue')\r\n    },\r\n    {\r\n        path: '/500',\r\n        name: 'error_500',\r\n        meta: {\r\n            hideInMenu: true\r\n        },\r\n        component: () =>\r\n            import ('@/view/error-page/500.vue')\r\n    }\r\n]\r\n"], "mappings": ";;;AAAA,OAAOA,IAAI,MAAM,mBAAmB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,CAAC;EACRC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE;IACFC,KAAK,EAAE,YAAY;IACnBC,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE,SAAAA,UAAA;IAAA,OACP,MAAM,CAAE,wBAAwB,CAAC;EAAA;AACzC,CAAC,EACD;EACIL,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbK,QAAQ,EAAE,OAAO;EACjBD,SAAS,EAAEN,IAAI;EACfG,IAAI,EAAE;IACFE,UAAU,EAAE,IAAI;IAChBG,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE,CAAC;IACPR,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MACFE,UAAU,EAAE,IAAI;MAChBD,KAAK,EAAE,IAAI;MACXI,QAAQ,EAAE,IAAI;MACdE,IAAI,EAAE;IACV;EACJ,CAAC;AACL,CAAC,EACD;EACIT,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfI,SAAS,EAAEN,IAAI;EACfG,IAAI,EAAE;IACFK,QAAQ,EAAE,IAAI;IACdG,WAAW,EAAE,IAAI;IACjBN,UAAU,EAAE;EAChB,CAAC;EACDI,QAAQ,EAAE,CAAC;IACPR,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MACFO,IAAI,EAAE,kBAAkB;MACxBN,KAAK,EAAE;IACX,CAAC;IACDE,SAAS,EAAE,SAAAA,UAAA;MAAA,OACP,MAAM,CAAE,mCAAmC,CAAC;IAAA;EACpD,CAAC;AACL,CAAC,EACD;EACIL,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZI,SAAS,EAAEN,IAAI;EACfG,IAAI,EAAE;IACFK,QAAQ,EAAE,IAAI;IACdG,WAAW,EAAE,IAAI;IACjBN,UAAU,EAAE;EAChB,CAAC;EACDI,QAAQ,EAAE,CAAC;IACPR,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MACFC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE;IAChB,CAAC;IACDC,SAAS,EAAE,SAAAA,UAAA;MAAA,OACP,MAAM,CAAE,qCAAqC,CAAC;IAAA;EACtD,CAAC;AACL,CAAC,EACD;EACIL,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE;IACFQ,WAAW,EAAE,IAAI;IACjBN,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAEN,IAAI;EACfS,QAAQ,EAAE,CAAC;IACPR,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE;MACFO,IAAI,EAAE,SAAS;MACfN,KAAK,EAAE;IACX,CAAC;IACDE,SAAS,EAAE,SAAAA,UAAA;MAAA,OACP,MAAM,CAAE,oCAAoC,CAAC;IAAA;EACrD,CAAC;AACL,CAAC,EACD;EACIL,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE;IACFE,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE,SAAAA,UAAA;IAAA,OACP,MAAM,CAAE,gCAAgC,CAAC;EAAA;AACjD,CAAC,EAED;EACIL,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;IACFE,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE,SAAAA,UAAA;IAAA,OACP,MAAM,CAAE,2BAA2B,CAAC;EAAA;AAC5C,CAAC,EACD;EACIL,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE;IACFE,UAAU,EAAE;EAChB,CAAC;EACDC,SAAS,EAAE,SAAAA,UAAA;IAAA,OACP,MAAM,CAAE,2BAA2B,CAAC;EAAA;AAC5C,CAAC,CACJ"}]}