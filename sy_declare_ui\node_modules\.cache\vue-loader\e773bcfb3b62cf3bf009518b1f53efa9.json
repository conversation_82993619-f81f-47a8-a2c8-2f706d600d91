{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\index.vue", "mtime": 1752737748521}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ3VzdG9tQ2xhc3MgZnJvbSAnLi9jdXN0b21DbGFzcy9pbmRleC52dWUnOyAvL+aKpeWFs+exu+ebruaooeWdlw0KaW1wb3J0IFZhdE5vIGZyb20gJy4vdmF0Tm8vaW5kZXgudnVlJzsgLy/muIXlhbPnqI7lj7fnu7TmiqTooagNCmltcG9ydCBXaEFkZHJlc3MgZnJvbSAgJy4vd2hBZGRyZXNzL2luZGV4LnZ1ZSc7Ly/nianmtYHllYbmuKDpgZMNCmltcG9ydCBDbGVhcmFuY2VMaW5rIGZyb20gJy4vY2xlYXJhbmNlTGluay9pbmRleC52dWUnOy8v5riF5YWz6L+e5o6lDQpleHBvcnQgZGVmYXVsdCB7DQogICAgbmFtZTonY3VzdG9tQmFzZScsDQogICAgY29tcG9uZW50czp7Q3VzdG9tQ2xhc3MsVmF0Tm8sV2hBZGRyZXNzLENsZWFyYW5jZUxpbmt9LA0KICAgIGRhdGEoKXsNCiAgICAgICAgcmV0dXJuew0KICAgICAgICAgIHRhYlR5cGU6J2NhdGVnb3J5JywNCiAgICAgICAgICBjdXN0b21DbGFzc1Nob3c6dHJ1ZSwNCiAgICAgICAgICB2YXROb1Nob3c6ZmFsc2UsDQogICAgICAgICAgY2xlYXJhbmNlTGlua1Nob3c6ZmFsc2UsDQogICAgICAgICAgd2hBZGRyZXNzU0hvdzpmYWxzZSwNCiAgICAgICAgfQ0KICAgIH0sDQogICAgbW91bnRlZCgpew0KICAgIH0sDQogICAgbWV0aG9kczp7DQogICAgICBjaGFuZ2VUYWJzKG5hbWUpew0KICAgICAgICBpZiAobmFtZSA9PT0gImNhdGVnb3J5IiAmJiB0aGlzLmN1c3RvbUNsYXNzU2hvdyA9PT0gZmFsc2UpIHsNCiAgICAgICAgICB0aGlzLmN1c3RvbUNsYXNzU2hvdyA9IHRydWU7DQogICAgICAgIH0NCiAgICAgICAgaWYgKG5hbWUgPT09ICJjbGVhcmFuY2VMaW5rIiAmJiB0aGlzLmNsZWFyYW5jZUxpbmtTaG93ID09PSBmYWxzZSkgew0KICAgICAgICAgIHRoaXMuY2xlYXJhbmNlTGlua1Nob3cgPSB0cnVlOw0KICAgICAgICB9DQogICAgICAgIGlmIChuYW1lID09PSAidmF0Tm8iICYmIHRoaXMudmF0Tm9TaG93ID09PSBmYWxzZSkgew0KICAgICAgICAgIHRoaXMudmF0Tm9TaG93ID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAobmFtZSA9PT0gIndoQWRkcmVzcyIgJiYgdGhpcy53aEFkZHJlc3NTSG93ID09PSBmYWxzZSkgew0KICAgICAgICAgIHRoaXMud2hBZGRyZXNzU0hvdyA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base", "sourcesContent": ["<!--\r\n@create date 2020-09-04\r\n<AUTHOR>\r\n@desc 报关基础设置\r\n-->\r\n<template>\r\n    <div class=\"FinanceBasics_box\">\r\n        <Tabs type=\"card\" name=\"home\"  v-model=\"tabType\" @on-click=\"changeTabs\">\r\n            <TabPane label=\"报关类目\"  name=\"category\"><CustomClass v-if=\"customClassShow\"/></TabPane>\r\n            <TabPane label=\"清关连接\"  name=\"clearanceLink\"><ClearanceLink v-if=\"clearanceLinkShow\"/></TabPane>\r\n            <TabPane label=\"清关税号维护表\"  name=\"vatNo\"><VatNo v-if=\"vatNoShow\"/></TabPane>\r\n            <TabPane label=\"仓库地址\"  name=\"whAddress\" ><WhAddress v-if=\"whAddressSHow\"/></TabPane>\r\n        </Tabs>\r\n    </div>\r\n</template>\r\n<script>\r\nimport CustomClass from './customClass/index.vue'; //报关类目模块\r\nimport VatNo from './vatNo/index.vue'; //清关税号维护表\r\nimport WhAddress from  './whAddress/index.vue';//物流商渠道\r\nimport ClearanceLink from './clearanceLink/index.vue';//清关连接\r\nexport default {\r\n    name:'customBase',\r\n    components:{CustomClass,VatNo,WhAddress,ClearanceLink},\r\n    data(){\r\n        return{\r\n          tabType:'category',\r\n          customClassShow:true,\r\n          vatNoShow:false,\r\n          clearanceLinkShow:false,\r\n          whAddressSHow:false,\r\n        }\r\n    },\r\n    mounted(){\r\n    },\r\n    methods:{\r\n      changeTabs(name){\r\n        if (name === \"category\" && this.customClassShow === false) {\r\n          this.customClassShow = true;\r\n        }\r\n        if (name === \"clearanceLink\" && this.clearanceLinkShow === false) {\r\n          this.clearanceLinkShow = true;\r\n        }\r\n        if (name === \"vatNo\" && this.vatNoShow === false) {\r\n          this.vatNoShow = true;\r\n        }\r\n        if (name === \"whAddress\" && this.whAddressSHow === false) {\r\n          this.whAddressSHow = true;\r\n        }\r\n      },\r\n    }\r\n}\r\n</script>\r\n<style  lang=\"less\">\r\n.FinanceBasics_box{\r\n  .ivu-tabs.ivu-tabs-card{\r\n    .ivu-tabs-bar{\r\n      margin-bottom: 0;\r\n    }\r\n    .ivu-tabs-nav-prev{\r\n        left:-12px;\r\n    }\r\n    .ivu-tabs-nav-next{\r\n        right: -8px;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n"]}]}