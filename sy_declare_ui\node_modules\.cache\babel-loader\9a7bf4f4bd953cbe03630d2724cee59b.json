{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\rankReport.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\store\\module\\rankReport.js", "mtime": 1752737748505}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIHN0YXRlOiB7CiAgICBhY3RpdmVOYW1lOiAnNCcKICB9LAogIG11dGF0aW9uczogewogICAgc2V0QWN0aXZlTmFtZTogZnVuY3Rpb24gc2V0QWN0aXZlTmFtZShzdGF0ZSwgZGF0YSkgewogICAgICBzdGF0ZS5hY3RpdmVOYW1lID0gZGF0YTsKICAgIH0KICB9LAogIGFjdGlvbnM6IHsKICAgIGNoYW5nZUFjdGl2ZU5hbWU6IGZ1bmN0aW9uIGNoYW5nZUFjdGl2ZU5hbWUoX3JlZiwgYWN0aXZlTmFtZSkgewogICAgICB2YXIgY29tbWl0ID0gX3JlZi5jb21taXQ7CiAgICAgIGNvbW1pdCgnc2V0QWN0aXZlTmFtZScsIGFjdGl2ZU5hbWUpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["state", "activeName", "mutations", "setActiveName", "data", "actions", "changeActiveName", "_ref", "commit"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/store/module/rankReport.js"], "sourcesContent": ["export default {\r\n  state: {\r\n    activeName: '4'\r\n  },\r\n\r\n  mutations: {\r\n    setActiveName (state, data) {\r\n      state.activeName = data;\r\n    }\r\n  },\r\n\r\n  actions: {\r\n    changeActiveName ({ commit }, activeName) {\r\n      commit('setActiveName', activeName)\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE;IACLC,UAAU,EAAE;EACd,CAAC;EAEDC,SAAS,EAAE;IACTC,aAAa,WAAAA,cAAEH,KAAK,EAAEI,IAAI,EAAE;MAC1BJ,KAAK,CAACC,UAAU,GAAGG,IAAI;IACzB;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,gBAAgB,WAAAA,iBAAAC,IAAA,EAAcN,UAAU,EAAE;MAAA,IAAtBO,MAAM,GAAAD,IAAA,CAANC,MAAM;MACxBA,MAAM,CAAC,eAAe,EAAEP,UAAU,CAAC;IACrC;EACF;AACF,CAAC"}]}