{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue", "mtime": 1752737748508}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/view/login", "sourcesContent": ["<style lang=\"less\">\r\n@import \"./login.less\";\r\n</style>\r\n\r\n<template>\r\n  <div class=\"login\">\r\n    <div class=\"login-con\">\r\n      <div class=\"login-layout-logo\">\r\n        <img class=\"login-logo\" :src=\"loginLogo\" key=\"login-logo\"  alt=\"珠海穗元服饰有限公司\"/>\r\n      </div>\r\n      <div class=\"login-area\">\r\n        <div class=\"backgroundText\">\r\n          <span>穗元服饰，不是一个人的小情怀</span>\r\n          <br />\r\n          <span>而是一群人的光荣与梦想</span>\r\n          <br />\r\n          <span>一个行业的机遇和使命</span>\r\n        </div>\r\n        <div class=\"login-area-center\">\r\n          <div id=\"wx_icon\"></div>\r\n          <div class=\"right-login\">\r\n            <div class=\"login-title\">穗元服饰账号登录</div>\r\n            <div v-show=\"!checkStatus\" class=\"form-con\">\r\n              <Form ref=\"loginForm\" :model=\"form\" :rules=\"rules\" @keydown.enter.native=\"handleSubmit\" style=\"position: relative\">\r\n                <FormItem prop=\"username\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">账号</div>\r\n                    <input type=\"text\" v-model=\"form.username\" />\r\n                  </div>\r\n                </FormItem>\r\n                <FormItem prop=\"password\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">密码</div>\r\n                    <input :type=\"passwordType\" class=\"password\" v-model=\"form.password\"/>\r\n                    <div class=\"eye\">\r\n                      <img :src=\"eyeImg\" @click=\"changeEyeStatus\"  alt=\"查看密码\"/>\r\n                    </div>\r\n                  </div>\r\n                  <Checkbox class=\"autoLogin\" v-model=\"form.auto\">自动登录</Checkbox>\r\n                </FormItem>\r\n                <FormItem>\r\n                  <Button @click=\"handleSubmit\" :size=\"buttonSize\" :loading=\"loading\" :long=\"true\" style=\"background:#1C6BBA;color:white\">登录</Button>\r\n                </FormItem>\r\n              </Form>\r\n            </div>\r\n            <div class=\"popTip\" v-show=\"checkStatus\">\r\n              <div style=\"margin-bottom: 5px\">\r\n                <a @click=\"closeCheck\">\r\n                  <Icon size=\"12\" type=\"md-arrow-back\" />返回\r\n                </a>\r\n              </div>\r\n              <div>\r\n                <slide-verify ref=\"slideBlock\" v-show=\"needCheck\" class=\"slide-box\" style=\"width: 280px\" :r=\"8\" :l=\"32\" :w=\"280\" :h=\"120\" :imgs=\"loginImg\"\r\n                  @success=\"handleCheckSuccess\" @fail=\"handleCheckFail\" slider-text=\"向右滑动完成验证\"></slide-verify>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"footer-area\">\r\n        <div class=\"login-footer-copyright\">\r\n          Copyright ©️ SuiYun.cn. All rights reserved\r\n        </div>\r\n        <div class=\"login-footer-copyright\">\r\n          珠海穗元服饰有限公司-中国服饰行业领跑者 <br/>联系方式：400-660-2205\r\n          email：<EMAIL>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions } from \"vuex\";\r\nimport loginLogo from \"@/assets/images/login-logo.png\";\r\nimport { setToken } from \"@/libs/util\";\r\nimport { getRequest } from \"@/libs/axios.js\";\r\nimport check1 from \"@/assets/images/loginImg/check1.jpg\";\r\nimport check2 from \"@/assets/images/loginImg/check2.jpg\";\r\nimport check3 from \"@/assets/images/loginImg/check3.jpg\";\r\nimport check4 from \"@/assets/images/loginImg/check4.jpg\";\r\nimport check5 from \"@/assets/images/loginImg/check5.jpg\";\r\nimport check6 from \"@/assets/images/loginImg/check6.jpg\";\r\n\r\nexport default {\r\n  name: \"LoginForm\",\r\n  props: {\r\n    usernameRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"账号不能为空\", trigger: \"blur\" }];\r\n      }\r\n    },\r\n    passwordRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"密码不能为空\", trigger: \"blur\" }];\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      loginImg: [check1, check2, check3, check4, check5, check6],\r\n      loginLogo,\r\n      buttonSize: \"large\",\r\n      passwordType: \"password\",\r\n      eyeStatus: true,\r\n      eyeImg: require(\"@/assets/icons/eye.png\"),\r\n      //新滑动验证\r\n      checkStatus: false,\r\n      needCheck: false,\r\n      errTimes: 0, //输入密码错误的次数\r\n      checkResult: false, //滑动验证的结果\r\n      //新滑动验证结束\r\n      form: {\r\n        username: \"\",\r\n        password: \"\",\r\n        auto: false\r\n      },\r\n      config: {}\r\n    };\r\n  },\r\n  computed: {\r\n    rules() {\r\n      return {\r\n        username: this.usernameRules,\r\n        password: this.passwordRules\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    eyeStatus(val) {\r\n      if (val) {\r\n        this.eyeImg = require(\"@/assets/icons/eye.png\");\r\n        this.passwordType = \"password\";\r\n      } else {\r\n        this.eyeImg = require(\"@/assets/icons/eye-active.png\");\r\n        this.passwordType = \"Text\";\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\"]),\r\n    //返回输入登录信息\r\n    closeCheck() {\r\n      this.checkStatus = false;\r\n    },\r\n    //验证通过\r\n    handleCheckSuccess() {\r\n      this.checkResult = true;\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          let username = this.form.username;\r\n          let password = this.form.password;\r\n          let auto = this.form.auto;\r\n          this.handleLogin({ username, password, auto })\r\n            .then(res => {\r\n              if (res && res['code'] === 0) {\r\n                this.$router.push({ name: this.$config.homeName });\r\n                localStorage.setItem(\"needCheck\", \"false\");\r\n                this.needCheck = false;\r\n                this.checkStatus = false;\r\n                this.errTimes = 0;\r\n              }}).catch(err => {\r\n                if (err) {\r\n                this.errTimes++;\r\n                this.checkStatus = false;\r\n                this.$refs.slideBlock.reset();\r\n                this.checkResult = false;\r\n                if (this.errTimes > 2) {\r\n                  localStorage.setItem(\"needCheck\", \"true\");\r\n                  this.needCheck = true;\r\n                }\r\n              }}).finally(() => {this.loading = false;});\r\n        }\r\n      });\r\n    },\r\n    //验证失败\r\n    handleCheckFail() {\r\n      this.checkResult = false;\r\n    },\r\n    //密码是否可见\r\n    changeEyeStatus() {\r\n      this.eyeStatus = !this.eyeStatus;\r\n    },\r\n    //点击登录\r\n    handleSubmit() {\r\n      if (this.needCheck === true) {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.checkStatus = true;\r\n          }\r\n        });\r\n      } else {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.loading = true;\r\n            let username = this.form.username;\r\n            let password = this.form.password;\r\n            let auto = this.form.auto;\r\n            this.handleLogin({ username, password, auto })\r\n              .then(res => {\r\n\r\n                if (res && res['code'] === 0) {\r\n                  this.$router.push({ name: this.$config.homeName });\r\n                  localStorage.setItem(\"needCheck\", \"false\");\r\n                  this.needCheck = false;\r\n                  this.errTimes = 0;\r\n                }}).catch(err => {\r\n                  if (err) {\r\n                    this.errTimes++;\r\n                    this.checkStatus = false;\r\n                    this.$refs.slideBlock.reset();\r\n                    this.checkResult = false;\r\n                    if (this.errTimes > 2) {\r\n                      localStorage.setItem(\"needCheck\", \"true\");\r\n                      this.needCheck = true;\r\n                    }\r\n                }})\r\n              .finally(() => {this.loading = false;});\r\n          }});\r\n      }\r\n    },\r\n    handleAuthLogin() {\r\n      let token = this.$route.query.token;\r\n      let UserId = this.$route.query.UserId;\r\n      if (typeof token === \"undefined\") {\r\n      } else {\r\n        getRequest(\"/base/login/checkThirdIp\", { UserId: UserId })\r\n          .then(res => {\r\n            if (res[\"code\"] === \"0\") {\r\n              setToken(token);\r\n              this.$router.push({ name: this.$config.homeName });\r\n            }\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n      }\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleAuthLogin();\r\n    this.needCheck = localStorage.getItem(\"needCheck\")\r\n      ? localStorage.getItem(\"needCheck\") === \"true\" : false;\r\n  }\r\n};\r\nwindow.onload = function() {\r\n  const redirect_uri =\r\n    process.env.NODE_ENV === \"production\"\r\n      ? encodeURIComponent(\"https://amz.i-suiyuan.com/api-v1.0/base/oauth/qiyewechat/callback\")\r\n      : undefined;\r\n  window.WwLogin({\r\n    id: \"wx_icon\",\r\n    appid: \"ww41fa91e90d30fdd8\",\r\n    agentid: \"1000002\",\r\n    redirect_uri: redirect_uri,\r\n    state: \"2241241241\",\r\n    href: location.origin + \"/QR_code.css\"\r\n  });\r\n};\r\n</script>\r\n<style></style>\r\n"]}]}