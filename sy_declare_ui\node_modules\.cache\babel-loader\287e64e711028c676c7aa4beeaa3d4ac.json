{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\logs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\logs\\index.vue", "mtime": 1752737748511}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovVXNlcnMvYWRtaW5pL0Rlc2t0b3AvZGV2L3N5X2RlY2xhcmVfdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0IGdhdGV3YXlMb2cgZnJvbSAnQC9hcGkvYmFzZS9nYXRld2F5L2dhdGV3YXlMb2cnOwppbXBvcnQgeyByZWFkVXNlckFnZW50IH0gZnJvbSAnQC9saWJzL3V0aWwnOwppbXBvcnQgeyBhdXRvVGFibGVIZWlnaHQgfSBmcm9tICdAL2xpYnMvdG9vbHMuanMnOwppbXBvcnQgQXBpIGZyb20gIkAvYXBpL2Jhc2UvZ2F0ZXdheS9hcGkiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2dhdGV3YXlMb2dzJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYXV0b1RhYmxlSGVpZ2h0OiBhdXRvVGFibGVIZWlnaHQsCiAgICAgIGRyYXdlcjogZmFsc2UsCiAgICAgIGN1cnJlbnRSb3c6IHt9LAogICAgICBwYWdlU2l6ZU9wdHM6IFsyMCwgNTAsIDEwMF0sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBzZWFyY2hGb3JtOiB7CiAgICAgICAgcGF0aDogJycsCiAgICAgICAgaXA6ICcnLAogICAgICAgIHNlcnZpY2VJZDogJycKICAgICAgfSwKICAgICAgcGFnZUluZm86IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlOiAxLAogICAgICAgIGxpbWl0OiAyMAogICAgICB9LAogICAgICBjb2x1bW5zOiBbewogICAgICAgIHRpdGxlOiAn5pel5b+X5qih5Z2XJywKICAgICAgICB3aWR0aDogMTgwLAogICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICBrZXk6ICdsb2dNb2R1bGUnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+aTjeS9nOexu+WeiycsCiAgICAgICAgd2lkdGg6IDEwMCwKICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAga2V5OiAnbG9nVHlwZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6K+35rGC5Zyw5Z2AJywKICAgICAgICBrZXk6ICdwYXRoJywKICAgICAgICB3aWR0aDogMzIwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+ivt+axguWPguaVsCcsCiAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgIGtleTogJ3BhcmFtcycsCiAgICAgICAgc2xvdDogJ3BhcmFtcycsCiAgICAgICAgd2lkdGg6IDIyMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmjqXlj6PlkI3np7AnLAogICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICBrZXk6ICdhcGlOYW1lJywKICAgICAgICB3aWR0aDogMjIwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+ivt+axguaWueW8jycsCiAgICAgICAga2V5OiAnbWV0aG9kJywKICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgd2lkdGg6IDEyMCwKICAgICAgICBmaWx0ZXJzOiBbewogICAgICAgICAgbGFiZWw6ICdQT1NUJywKICAgICAgICAgIHZhbHVlOiAwCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICdHRVQnLAogICAgICAgICAgdmFsdWU6IDEKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ0RFTEVURScsCiAgICAgICAgICB2YWx1ZTogMgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAnT1BUSU9OUycsCiAgICAgICAgICB2YWx1ZTogMwogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAnUEFUQ0gnLAogICAgICAgICAgdmFsdWU6IDQKICAgICAgICB9XSwKICAgICAgICBmaWx0ZXJNdWx0aXBsZTogZmFsc2UsCiAgICAgICAgZmlsdGVyTWV0aG9kOiBmdW5jdGlvbiBmaWx0ZXJNZXRob2QodmFsdWUsIHJvdykgewogICAgICAgICAgaWYgKHZhbHVlID09PSAwKSB7CiAgICAgICAgICAgIHJldHVybiByb3cubWV0aG9kID09PSAnUE9TVCc7CiAgICAgICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAxKSB7CiAgICAgICAgICAgIHJldHVybiByb3cubWV0aG9kID09PSAnR0VUJzsKICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT09IDIpIHsKICAgICAgICAgICAgcmV0dXJuIHJvdy5tZXRob2QgPT09ICdERUxFVEUnOwogICAgICAgICAgfSBlbHNlIGlmICh2YWx1ZSA9PT0gMykgewogICAgICAgICAgICByZXR1cm4gcm93Lm1ldGhvZCA9PT0gJ09QVElPTlMnOwogICAgICAgICAgfSBlbHNlIGlmICh2YWx1ZSA9PT0gNCkgewogICAgICAgICAgICByZXR1cm4gcm93Lm1ldGhvZCA9PT0gJ1BBVENIJzsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ0lQJywKICAgICAgICBrZXk6ICdpcCcsCiAgICAgICAgd2lkdGg6IDE1MAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfljLrln58nLAogICAgICAgIGtleTogJ3JlZ2lvbicsCiAgICAgICAgbWluV2lkdGg6IDEwMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnu4jnq68nLAogICAgICAgIHdpZHRoOiA4MCwKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoLCBwYXJhbXMpIHsKICAgICAgICAgIHJldHVybiBoKCdkaXYnLCByZWFkVXNlckFnZW50KHBhcmFtcy5yb3cudXNlckFnZW50KS50ZXJtaW5hbCk7CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmtY/op4jlmagnLAogICAgICAgIHdpZHRoOiAxMDAsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgcGFyYW1zKSB7CiAgICAgICAgICByZXR1cm4gaCgnZGl2JywgcmVhZFVzZXJBZ2VudChwYXJhbXMucm93LnVzZXJBZ2VudCkuYnJvd3Nlcik7CiAgICAgICAgfQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmnI3liqHlkI0nLAogICAgICAgIGtleTogJ3NlcnZpY2VJZCcsCiAgICAgICAgd2lkdGg6IDE2MAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjmiLflkI0nLAogICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICB3aWR0aDogMTIwLAogICAgICAgIGtleTogJ3VzZXJOYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICflk43lupTnirbmgIEnLAogICAgICAgIGtleTogJ2h0dHBTdGF0dXMnLAogICAgICAgIHNsb3Q6ICdodHRwU3RhdHVzJywKICAgICAgICB3aWR0aDogMTAwCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+iAl+aXticsCiAgICAgICAga2V5OiAndXNlVGltZScsCiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgcGFyYW1zKSB7CiAgICAgICAgICByZXR1cm4gaCgnZGl2JywgKHBhcmFtcy5yb3dbJ3VzZVRpbWUnXSA/IHBhcmFtcy5yb3dbJ3VzZVRpbWUnXSA6IDApICsgJyBtcycpOwogICAgICAgIH0sCiAgICAgICAgd2lkdGg6IDEwMAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfor7fmsYLml7bpl7QnLAogICAgICAgIGtleTogJ3JlcXVlc3RUaW1lJywKICAgICAgICB3aWR0aDogMTYwCiAgICAgIH1dLAogICAgICBkYXRhOiBbXQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNlYXJjaDogZnVuY3Rpb24gaGFuZGxlU2VhcmNoKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnYXRld2F5TG9nLmxpc3RQYWdlKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5wYWdlSW5mbyksIHRoaXMuc2VhcmNoRm9ybSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLmRhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIF90aGlzLnBhZ2VJbmZvLnRvdGFsID0gcGFyc2VJbnQocmVzLmRhdGEudG90YWwpOwogICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlc2V0Rm9ybTogZnVuY3Rpb24gaGFuZGxlUmVzZXRGb3JtKCkgewogICAgICB0aGlzLnBhZ2VJbmZvID0gewogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHBhZ2U6IDEsCiAgICAgICAgbGltaXQ6IDIwCiAgICAgIH07CiAgICAgIHRoaXMuJHJlZnNbJ3NlYXJjaEZvcm1SZWYnXS5yZXNldEZpZWxkcygpOwogICAgfSwKICAgIGhhbmRsZVBhZ2U6IGZ1bmN0aW9uIGhhbmRsZVBhZ2UoY3VycmVudCkgewogICAgICB0aGlzLnBhZ2VJbmZvLnBhZ2UgPSBjdXJyZW50OwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfSwKICAgIGhhbmRsZVBhZ2VTaXplOiBmdW5jdGlvbiBoYW5kbGVQYWdlU2l6ZShzaXplKSB7CiAgICAgIHRoaXMucGFnZUluZm8ubGltaXQgPSBzaXplOwogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgfQp9Ow=="}, {"version": 3, "names": ["gatewayLog", "readUserAgent", "autoTableHeight", "Api", "name", "data", "drawer", "currentRow", "pageSizeOpts", "loading", "searchForm", "path", "ip", "serviceId", "pageInfo", "total", "page", "limit", "columns", "title", "width", "align", "key", "slot", "filters", "label", "value", "filterMultiple", "filterMethod", "row", "method", "min<PERSON><PERSON><PERSON>", "render", "h", "params", "userAgent", "terminal", "browser", "methods", "handleSearch", "_this", "listPage", "_objectSpread", "then", "res", "records", "parseInt", "finally", "handleResetForm", "$refs", "resetFields", "handlePage", "current", "handlePageSize", "size", "mounted"], "sources": ["src/view/module/base/gateway/logs/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" inline>\r\n        <FormItem prop=\"path\">\r\n          <Input type=\"text\" v-model=\"searchForm.path\" placeholder=\"请输入请求路径\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"ip\">\r\n          <Input type=\"text\" v-model=\"searchForm.ip\" placeholder=\"请输入IP\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"serviceId\">\r\n          <Input type=\"text\" v-model=\"searchForm.serviceId\" placeholder=\"请输入服务名\"/>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm()\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <Table :border=\"true\" :max-height=\"autoTableHeight($refs.autoTableRef)\" ref=\"autoTableRef\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" :page-size-opts=\"pageSizeOpts\">\r\n        <template v-slot:httpStatus=\"{ row }\">\r\n          <Badge v-if=\"row['httpStatus']==='200'\" status=\"success\"/>\r\n          <Badge v-else status=\"error\"/>\r\n          <span>{{row['httpStatus']}}</span>\r\n        </template>\r\n        <template v-slot:params=\"{row,index}\">\r\n          <Poptip :word-wrap=\"true\" width=\"200\" trigger=\"hover\" :content=\"row['params']\">\r\n            <div  v-copytext=\"row['params']\" style=\"width:100px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;\">{{row['params']}}</div>\r\n          </Poptip>\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" size=\"small\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import gatewayLog from '@/api/base/gateway/gatewayLog'\r\n  import {readUserAgent} from '@/libs/util'\r\n  import { autoTableHeight } from '@/libs/tools.js'\r\n  import Api from \"@/api/base/gateway/api\";\r\n  export default {\r\n    name: 'gatewayLogs',\r\n    data () {\r\n      return {\r\n        autoTableHeight,\r\n        drawer: false,\r\n        currentRow: {},\r\n        pageSizeOpts:[20,50,100],\r\n        loading: false,\r\n        searchForm:{\r\n          path: '',\r\n          ip: '',\r\n          serviceId: ''},\r\n        pageInfo: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 20,\r\n        },\r\n        columns: [\r\n          {\r\n            title: '日志模块',\r\n            width: 180,\r\n            align: 'center',\r\n            key: 'logModule'\r\n          },\r\n          {\r\n            title: '操作类型',\r\n            width: 100,\r\n            align: 'center',\r\n            key: 'logType'\r\n          },\r\n          {\r\n            title: '请求地址',\r\n            key: 'path',\r\n            width: 320\r\n          },\r\n          {\r\n            title: '请求参数',\r\n            align: 'center',\r\n            key: 'params',\r\n            slot:'params',\r\n            width: 220\r\n          },\r\n          {\r\n            title: '接口名称',\r\n            align: 'center',\r\n            key: 'apiName',\r\n            width: 220\r\n          },\r\n          {\r\n            title: '请求方式',\r\n            key: 'method',\r\n            align: 'center',\r\n            width: 120,\r\n            filters: [\r\n              {\r\n                label: 'POST',\r\n                value: 0\r\n              },\r\n              {\r\n                label: 'GET',\r\n                value: 1\r\n              },\r\n              {\r\n                label: 'DELETE',\r\n                value: 2\r\n              },\r\n              {\r\n                label: 'OPTIONS',\r\n                value: 3\r\n              },\r\n              {\r\n                label: 'PATCH',\r\n                value: 4\r\n              }\r\n            ],\r\n            filterMultiple: false,\r\n            filterMethod (value, row) {\r\n              if (value === 0) {\r\n                return row.method === 'POST'\r\n              } else if (value === 1) {\r\n                return row.method === 'GET'\r\n              } else if (value === 2) {\r\n                return row.method === 'DELETE'\r\n              } else if (value === 3) {\r\n                return row.method === 'OPTIONS'\r\n              } else if (value === 4) {\r\n                return row.method === 'PATCH'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            title: 'IP',\r\n            key: 'ip',\r\n            width: 150\r\n          },\r\n          {\r\n            title: '区域',\r\n            key: 'region',\r\n            minWidth: 100\r\n          },\r\n          {\r\n            title: '终端',\r\n            width: 80,\r\n            render: (h, params) => {\r\n              return h('div', readUserAgent(params.row.userAgent).terminal)\r\n            }\r\n          },\r\n          {\r\n            title: '浏览器',\r\n            width: 100,\r\n            render: (h, params) => {\r\n              return h('div', readUserAgent(params.row.userAgent).browser)\r\n            }\r\n          },\r\n          {\r\n            title: '服务名',\r\n            key: 'serviceId',\r\n            width: 160\r\n          },\r\n          {\r\n            title: '用户名',\r\n            align: 'center',\r\n            width: 120,\r\n            key: 'userName'\r\n          },\r\n          {\r\n            title: '响应状态',\r\n            key: 'httpStatus',\r\n            slot: 'httpStatus',\r\n            width: 100\r\n          },\r\n          {\r\n            title: '耗时',\r\n            key: 'useTime',\r\n            render: (h, params) => {\r\n              return h('div', (params.row['useTime'] ? params.row['useTime'] : 0) + ' ms')\r\n            },\r\n\r\n            width: 100\r\n          },\r\n          {\r\n            title: '请求时间',\r\n            key: 'requestTime',\r\n            width: 160\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleSearch () {\r\n        this.loading = true\r\n        gatewayLog.listPage({...this.pageInfo,...this.searchForm}).then(res => {\r\n          this.data = res.data.records\r\n          this.pageInfo.total = parseInt(res.data.total)\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handleResetForm () {\r\n        this.pageInfo = {total: 0, page: 1, limit: 20,}\r\n        this.$refs['searchFormRef'].resetFields()\r\n      },\r\n      handlePage (current) {\r\n        this.pageInfo.page = current\r\n        this.handleSearch()\r\n      },\r\n      handlePageSize (size) {\r\n        this.pageInfo.limit = size\r\n        this.handleSearch()\r\n      }\r\n    },\r\n    mounted: function () {\r\n      this.handleSearch()\r\n    }\r\n  }\r\n</script>\r\n"], "mappings": ";;AAsCA,OAAAA,UAAA;AACA,SAAAC,aAAA;AACA,SAAAC,eAAA;AACA,OAAAC,GAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAH,eAAA,EAAAA,eAAA;MACAI,MAAA;MACAC,UAAA;MACAC,YAAA;MACAC,OAAA;MACAC,UAAA;QACAC,IAAA;QACAC,EAAA;QACAC,SAAA;MAAA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,GAAA;MACA,GACA;QACAH,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,GAAA;MACA,GACA;QACAH,KAAA;QACAG,GAAA;QACAF,KAAA;MACA,GACA;QACAD,KAAA;QACAE,KAAA;QACAC,GAAA;QACAC,IAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAE,KAAA;QACAC,GAAA;QACAF,KAAA;MACA,GACA;QACAD,KAAA;QACAG,GAAA;QACAD,KAAA;QACAD,KAAA;QACAI,OAAA,GACA;UACAC,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,GACA;UACAD,KAAA;UACAC,KAAA;QACA,EACA;QACAC,cAAA;QACAC,YAAA,WAAAA,aAAAF,KAAA,EAAAG,GAAA;UACA,IAAAH,KAAA;YACA,OAAAG,GAAA,CAAAC,MAAA;UACA,WAAAJ,KAAA;YACA,OAAAG,GAAA,CAAAC,MAAA;UACA,WAAAJ,KAAA;YACA,OAAAG,GAAA,CAAAC,MAAA;UACA,WAAAJ,KAAA;YACA,OAAAG,GAAA,CAAAC,MAAA;UACA,WAAAJ,KAAA;YACA,OAAAG,GAAA,CAAAC,MAAA;UACA;QACA;MACA,GACA;QACAX,KAAA;QACAG,GAAA;QACAF,KAAA;MACA,GACA;QACAD,KAAA;QACAG,GAAA;QACAS,QAAA;MACA,GACA;QACAZ,KAAA;QACAC,KAAA;QACAY,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA,QAAAhC,aAAA,CAAAiC,MAAA,CAAAL,GAAA,CAAAM,SAAA,EAAAC,QAAA;QACA;MACA,GACA;QACAjB,KAAA;QACAC,KAAA;QACAY,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA,QAAAhC,aAAA,CAAAiC,MAAA,CAAAL,GAAA,CAAAM,SAAA,EAAAE,OAAA;QACA;MACA,GACA;QACAlB,KAAA;QACAG,GAAA;QACAF,KAAA;MACA,GACA;QACAD,KAAA;QACAE,KAAA;QACAD,KAAA;QACAE,GAAA;MACA,GACA;QACAH,KAAA;QACAG,GAAA;QACAC,IAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAG,GAAA;QACAU,MAAA,WAAAA,OAAAC,CAAA,EAAAC,MAAA;UACA,OAAAD,CAAA,SAAAC,MAAA,CAAAL,GAAA,cAAAK,MAAA,CAAAL,GAAA;QACA;QAEAT,KAAA;MACA,GACA;QACAD,KAAA;QACAG,GAAA;QACAF,KAAA;MACA,EACA;MACAf,IAAA;IACA;EACA;EACAiC,OAAA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAA/B,OAAA;MACAT,UAAA,CAAAyC,QAAA,CAAAC,aAAA,CAAAA,aAAA,UAAA5B,QAAA,QAAAJ,UAAA,GAAAiC,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAAnC,IAAA,GAAAuC,GAAA,CAAAvC,IAAA,CAAAwC,OAAA;QACAL,KAAA,CAAA1B,QAAA,CAAAC,KAAA,GAAA+B,QAAA,CAAAF,GAAA,CAAAvC,IAAA,CAAAU,KAAA;MACA,GAAAgC,OAAA;QACAP,KAAA,CAAA/B,OAAA;MACA;IACA;IACAuC,eAAA,WAAAA,gBAAA;MACA,KAAAlC,QAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,KAAA;MAAA;MACA,KAAAgC,KAAA,kBAAAC,WAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAtC,QAAA,CAAAE,IAAA,GAAAoC,OAAA;MACA,KAAAb,YAAA;IACA;IACAc,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAxC,QAAA,CAAAG,KAAA,GAAAqC,IAAA;MACA,KAAAf,YAAA;IACA;EACA;EACAgB,OAAA,WAAAA,QAAA;IACA,KAAAhB,YAAA;EACA;AACA"}]}