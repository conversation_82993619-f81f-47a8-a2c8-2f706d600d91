{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\vatNo\\index.vue", "mtime": 1752737748521}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["CommonApi", "VatNo", "UploadImg", "Shop", "ShopSelect", "getToken", "getUrl", "ClearanceLink", "components", "data", "loading", "saving", "modal", "spinShow", "disabled", "title", "formInfo", "vatNo", "shopIds", "country", "pageInfo", "total", "page", "limit", "loginInfo", "Accept", "mode", "Authorization", "importURl", "vatNoForm", "eori", "shopId", "company", "address", "countryList", "shopList", "columns", "key", "min<PERSON><PERSON><PERSON>", "align", "slot", "width", "mounted", "handleSearch", "getCountryList", "getAllShop", "methods", "_this", "getAll", "then", "res", "_this2", "params", "_objectSpread", "getStr", "value", "Array", "isArray", "join", "undefined", "listPage", "records", "Number", "finally", "handleReset", "$refs", "resetFields", "delVatNo", "row", "_this3", "$Modal", "confirm", "content", "onOk", "remove", "id", "$Message", "success", "editVatNo", "resetForm", "Object", "assign", "addVatNo", "saveVatNo", "_this4", "validate", "valid", "handleImportSuccess", "clearFiles", "warning", "handleImportFormatError", "file", "error", "name", "okText", "handleImportError", "err", "message", "handleMaxSize", "templateExport", "_this5", "downloadTemplate", "vatNoExport", "_this6", "Date", "getTime", "download", "_this7", "ListDictionaryValueBy", "map", "item", "JSON", "parse", "handlePage", "handlePageSize", "size", "cancelForm"], "sources": ["src/view/module/custom/base/vatNo/index.vue"], "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 清关税号维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"formInfo\" :model=\"formInfo\" inline :label-width=\"60\">\r\n      <FormItem prop=\"number1\" label=\"税号\">\r\n        <Input v-model=\"formInfo.vatNo\" placeholder=\"请输入税号搜索\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"shopIds\" label=\"店铺\">\r\n        <ShopSelect v-model=\"formInfo.shopIds\" placeholder=\"店铺\" width=\"205px\" valueField=\"id\"/>\r\n      </FormItem>\r\n      <FormItem label=\"目的国\" prop=\"country\">\r\n        <Select v-model=\"formInfo.country\" filterable clearable placeholder=\"请选择目的国\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button style=\"margin-left:10px;\" @click=\"addVatNo\" :loading=\"saving\">新增</Button>\r\n      <Button @click=\"templateExport\" style=\"margin-left:10px;\" :loading=\"saving\">导入模板</Button>\r\n      <Button @click=\"vatNoExport\" style=\"margin-left:10px;\" :loading=\"saving\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\">\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:shopName=\"{row}\">\r\n        <span v-for=\"item in shopList\" v-if=\"item['id'] === row.shopId\">{{ item['name'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editVatNo(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"delVatNo(row)\" style=\"margin:0 2px\">删除</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"vatNoForm\" :model=\"vatNoForm\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem label=\"店铺\" prop=\"shopId\" :rules=\"{required: true, message: '不能为空', trigger: 'change'}\">\r\n          <Select v-model=\"vatNoForm.shopId\" filterable clearable placeholder=\"网店\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in shopList\" :value=\"item['id']\" :key=\"index\">{{ item['name'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"目的国\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"vatNoForm.country\" filterable clearable placeholder=\"请选择目的国\" class=\"widthClass\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"税号\" prop=\"vatNo\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Input v-model.trim=\"vatNoForm.vatNo\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"税号公司\" prop=\"company\">\r\n          <Input v-model.trim=\"vatNoForm.company\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"地址\" prop=\"address\">\r\n          <Input v-model.trim=\"vatNoForm.address\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"EORI\" prop=\"eori\">\r\n          <Input v-model.trim=\"vatNoForm.eori\" :readonly=\"disabled\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveVatNo\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport VatNo from \"@/api/custom/vatNo\";\r\nimport UploadImg from \"@/view/module/custom/company/common/uploadImg.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport ShopSelect from \"@/components/shopSelect/index.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nimport ClearanceLink from \"@/api/custom/clearanceLink\";\r\nexport default {\r\n  components: {UploadImg,ShopSelect},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      title: '',\r\n      formInfo: {\r\n        vatNo: '',\r\n        shopIds: '',\r\n        country: ''\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/vatNo/importFile\",\r\n      vatNoForm: {eori:null,shopId:null,country:null,company:null,vatNo:null,address:null},\r\n      countryList: [],\r\n      shopList:[],\r\n      data: [],\r\n      columns: [\r\n        {title: '网店名称', key: 'shopId', minWidth: 100, align: 'center',slot:'shopName'},\r\n        {title: '目的国', key: 'country', minWidth: 100, align: 'center',slot:'country'},\r\n        {title: '税号', key: 'vatNo', minWidth: 100, align: 'center'},\r\n        {title: '税号公司', key: 'company', minWidth: 150, align: 'center'},\r\n        {title: '地址', key: 'address', minWidth: 300, align: 'center'},\r\n        {title: 'EORI', key: 'eori', minWidth: 100, align: 'center'},\r\n        {title: '操作', key: 'action', width: 150, align: 'center', slot: 'action'}],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getAllShop();\r\n  },\r\n  methods: {\r\n    getAllShop() {\r\n      Shop.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shopList = res.data;\r\n        }\r\n      })\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo, ...this.pageInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      VatNo.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['formInfo'].resetFields();\r\n    },\r\n    delVatNo(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          VatNo.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    editVatNo(row) {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.vatNoForm = Object.assign({},row);\r\n    },\r\n    addVatNo() {\r\n      this.title = \"添加清关税号\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n    saveVatNo() {\r\n      this.$refs['vatNoForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          VatNo.saveVatNo(this.vatNoForm).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    templateExport(){\r\n      this.loading = true;\r\n      VatNo.downloadTemplate({\"fileName\":\"清关税号导入模板.xls\"}, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    vatNoExport() {\r\n      this.loading = true;\r\n      let params = {...this.formInfo}\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n      params['shopIds'] = getStr(params['shopIds']);\r\n      params['fileName'] = \"清关税号\" + new Date().getTime() + \".xls\";\r\n      this.loading = true;\r\n      VatNo.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['vatNoForm'].resetFields();\r\n      this.vatNoForm = {};\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAyFA,OAAAA,SAAA;AACA,OAAAC,KAAA;AACA,OAAAC,SAAA;AACA,OAAAC,IAAA;AACA,OAAAC,UAAA;AACA,SAAAC,QAAA,EAAAC,MAAA;AACA,OAAAC,aAAA;AACA;EACAC,UAAA;IAAAN,SAAA,EAAAA,SAAA;IAAAE,UAAA,EAAAA;EAAA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,KAAA;MACAC,QAAA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;QACAC,KAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,SAAA;QACAC,MAAA;QACAC,IAAA;QACAC,aAAA,cAAAtB,QAAA;MACA;MACAuB,SAAA,EAAAtB,MAAA;MACAuB,SAAA;QAAAC,IAAA;QAAAC,MAAA;QAAAZ,OAAA;QAAAa,OAAA;QAAAf,KAAA;QAAAgB,OAAA;MAAA;MACAC,WAAA;MACAC,QAAA;MACA1B,IAAA;MACA2B,OAAA,GACA;QAAArB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAzB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAzB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAxB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAxB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAxB,KAAA;QAAAsB,GAAA;QAAAC,QAAA;QAAAC,KAAA;MAAA,GACA;QAAAxB,KAAA;QAAAsB,GAAA;QAAAI,KAAA;QAAAF,KAAA;QAAAC,IAAA;MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,cAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACAD,UAAA,WAAAA,WAAA;MAAA,IAAAE,KAAA;MACA5C,IAAA,CAAA6C,MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACAH,KAAA,CAAAZ,QAAA,GAAAe,GAAA,CAAAzC,IAAA;QACA;MACA;IACA;IACAkC,YAAA,WAAAA,aAAA;MAAA,IAAAQ,MAAA;MACA,KAAAzC,OAAA;MACA,IAAA0C,MAAA,GAAAC,aAAA,CAAAA,aAAA,UAAArC,QAAA,QAAAI,QAAA;MACA,IAAAkC,MAAA,YAAAA,OAAAC,KAAA;QAAA,OAAAA,KAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAF,KAAA,IAAAA,KAAA,CAAAG,IAAA,QAAAC,SAAA;MAAA;MACAP,MAAA,cAAAE,MAAA,CAAAF,MAAA;MACAnD,KAAA,CAAA2D,QAAA,CAAAR,MAAA,EAAAH,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAC,MAAA,CAAA1C,IAAA,GAAAyC,GAAA,CAAAzC,IAAA,CAAAoD,OAAA;UACAV,MAAA,CAAA/B,QAAA,CAAAC,KAAA,GAAAyC,MAAA,CAAAZ,GAAA,CAAAzC,IAAA,CAAAY,KAAA;QACA;MACA,GAAA0C,OAAA;QACAZ,MAAA,CAAAzC,OAAA;MACA;IACA;IACAsD,WAAA,WAAAA,YAAA;MACA,KAAAC,KAAA,aAAAC,WAAA;IACA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACAxD,KAAA;QACAyD,OAAA;QACAC,IAAA,WAAAA,KAAA;UACAxE,KAAA,CAAAyE,MAAA;YAAAC,EAAA,EAAAP,GAAA,CAAAO;UAAA,GAAA1B,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAmB,MAAA,CAAAO,QAAA,CAAAC,OAAA;cACAR,MAAA,CAAA1B,YAAA;YACA;UACA;QACA;MACA;IACA;IACAmC,SAAA,WAAAA,UAAAV,GAAA;MACA,KAAArD,KAAA;MACA,KAAAH,KAAA;MACA,KAAAE,QAAA;MACA,KAAAiE,SAAA;MACA,KAAAlD,SAAA,GAAAmD,MAAA,CAAAC,MAAA,KAAAb,GAAA;IACA;IACAc,QAAA,WAAAA,SAAA;MACA,KAAAnE,KAAA;MACA,KAAAH,KAAA;MACA,KAAAE,QAAA;MACA,KAAAiE,SAAA;IACA;IACAI,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAnB,KAAA,cAAAoB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAAzE,MAAA;UACAV,KAAA,CAAAkF,SAAA,CAAAC,MAAA,CAAAvD,SAAA,EAAAoB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAkC,MAAA,CAAAzE,MAAA;cACAyE,MAAA,CAAAR,QAAA,CAAAC,OAAA;cACAO,MAAA,CAAAL,SAAA;cACAK,MAAA,CAAAxE,KAAA;cACAwE,MAAA,CAAAzC,YAAA;YACA;UACA,GAAAoB,OAAA;YACAqB,MAAA,CAAAzE,MAAA;UACA;QACA;MACA;IACA;IACA4E,mBAAA,WAAAA,oBAAArC,GAAA;MACA,KAAAe,KAAA,kBAAAuB,UAAA;MACA,IAAAtC,GAAA;QACA,KAAA0B,QAAA,CAAAC,OAAA;QACA,KAAAlC,YAAA;MACA;QACA,KAAAiC,QAAA,CAAAa,OAAA,CAAAvC,GAAA;MACA;IACA;IACAwC,uBAAA,WAAAA,wBAAAC,IAAA;MACA;MACA,KAAArB,MAAA,CAAAsB,KAAA;QACA7E,KAAA;QACAyD,OAAA,UAAAmB,IAAA,CAAAE,IAAA;QACAC,MAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA,EAAAL,IAAA;MACA,KAAAf,QAAA,CAAAa,OAAA,CAAAE,IAAA,CAAAM,OAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAtB,QAAA,CAAAa,OAAA;IACA;IACAU,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA1F,OAAA;MACAT,KAAA,CAAAoG,gBAAA;QAAA;MAAA;QACAD,MAAA,CAAA1F,OAAA;MACA;IACA;IACA4F,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA7F,OAAA;MACA,IAAA0C,MAAA,GAAAC,aAAA,UAAArC,QAAA;MACA,IAAAsC,MAAA,YAAAA,OAAAC,KAAA;QAAA,OAAAA,KAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAF,KAAA,IAAAA,KAAA,CAAAG,IAAA,QAAAC,SAAA;MAAA;MACAP,MAAA,cAAAE,MAAA,CAAAF,MAAA;MACAA,MAAA,4BAAAoD,IAAA,GAAAC,OAAA;MACA,KAAA/F,OAAA;MACAT,KAAA,CAAAyG,QAAA,CAAAtD,MAAA;QACAmD,MAAA,CAAA7F,OAAA;MACA;IACA;IACA;IACAkC,cAAA,WAAAA,eAAA;MAAA,IAAA+D,MAAA;MACA3G,SAAA,CAAA4G,qBAAA,iBAAA3D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,IAAAA,GAAA;UACA,IAAAzC,IAAA,GAAAyC,GAAA;UACA,IAAAzC,IAAA;YACAkG,MAAA,CAAAzE,WAAA,GAAAzB,IAAA,CAAAoG,GAAA,WAAAC,IAAA;cAAA,OAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA,CAAAvD,KAAA;YAAA;UACA;QACA;MACA;IACA;IACA0D,UAAA,WAAAA,WAAA3F,IAAA;MACA,KAAAF,QAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAqB,YAAA;IACA;IACAuE,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAA/F,QAAA,CAAAE,IAAA;MACA,KAAAF,QAAA,CAAAG,KAAA,GAAA4F,IAAA;MACA,KAAAxE,YAAA;IACA;IACAyE,UAAA,WAAAA,WAAA;MACA,KAAAxG,KAAA;MACA,KAAAmE,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MACA,KAAAd,KAAA,cAAAC,WAAA;MACA,KAAArC,SAAA;IACA;EACA;AACA"}]}