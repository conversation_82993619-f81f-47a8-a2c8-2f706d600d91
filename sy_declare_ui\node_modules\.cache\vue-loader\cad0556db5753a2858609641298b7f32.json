{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\index.vue", "mtime": 1752737748514}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/view/module/base/role", "sourcesContent": ["<template>\r\n  <div class=\"roleManage\">\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline\r\n            @keydown.enter.native=\"(e) => {e.preventDefault();handleSearch(1);}\">\r\n        <FormItem prop=\"roleName\">\r\n          <Input type=\"text\" v-model=\"pageInfo.roleName\" placeholder=\"角色名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"roleCode\">\r\n          <Input type=\"text\" v-model=\"pageInfo.roleCode\" placeholder=\"角色编码\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  :transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal('', 'add')\" :disabled=\"!hasAuthority('roleAdd')\">添加</Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':(v.key ===1?'error':'warning')\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a style=\"margin-right: 8px\" @click=\"handleModal(row, 'view')\">查看</a>\r\n          <a @click=\"handleModal(row, 'edit')\" v-if=\"hasAuthority('roleEdit')\">编辑</a>&nbsp;\r\n          <a @click=\"handleRemove(row)\" v-if=\"hasAuthority('roleDel')\">删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" size=\"small\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change=\"handlePageSize\"></Page>\r\n    </Card>\r\n    <!-- 点击添加之后的弹窗 -->\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" width=\"40\" @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Tabs @on-click=\"handleTabClick\" :value=\"current\">\r\n          <TabPane label=\"角色信息\" name=\"form1\">\r\n            <Form v-show=\"current === 'form1'\" ref=\"form1\" :disabled=\"modalType === 'view'\" :model=\"formItem\"\r\n                  :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"角色标识\" prop=\"roleCode\">\r\n                <Input v-model=\"formItem.roleCode\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"角色名称\" prop=\"roleName\">\r\n                <Input v-model=\"formItem.roleName\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"状态\">\r\n                <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n                  <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n                </RadioGroup>\r\n              </FormItem>\r\n              <FormItem label=\"描述\">\r\n                <Input v-model=\"formItem.roleDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n          <TabPane :disabled=\"!formItem.id\" label=\"分配权限\" name=\"form2\">\r\n            <Form v-show=\"current === 'form2'\" ref=\"form2\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"菜单名称\" prop=\"menuName\">\r\n                <Input v-model=\"formItem.menuName\" placeholder=\"请输入\" style=\"width: 250px\"\r\n                       @keydown.enter.native=\"(e) => {e.preventDefault();handleSearchMenu()}\"/>\r\n                <Button type=\"primary\" @click=\"handleSearchMenu\" style=\"margin: 0 20px\" :loading=\"loadTree\">查询\r\n                </Button>\r\n                <Button @click=\"handleResetMenu\">重置</Button>\r\n              </FormItem>\r\n              <FormItem label=\"功能菜单\" prop=\"grantMenus\">\r\n                <tree-table ref=\"tree\" style=\"max-height: 480px; overflow: auto\" expand-key=\"menuName\"\r\n                            :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"modalType !== 'view'\"\r\n                            :columns=\"menuColumns\" :data=\"selectMenus\" >\r\n                  <template v-slot:operation=\"scope\">\r\n                    <CheckboxGroup key=\"box1\" v-model=\"formItem.grantActions\">\r\n                      <Checkbox :disabled=\"modalType === 'view'\" v-for=\"item in scope.row.actionList\"\r\n                                :key=\"item.authorityId\" :label=\"item.authorityId\" >\r\n                        <span :title=\"item.actionDesc\">{{ item.actionName }}</span>\r\n                      </Checkbox>\r\n                    </CheckboxGroup>\r\n                  </template>\r\n                </tree-table>\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n          <TabPane :disabled=\"!formItem.id\" label=\"角色成员\" name=\"form3\">\r\n            <Form v-show=\"current === 'form3'\" ref=\"form3\" :disabled=\"modalType === 'view'\" :model=\"formItem\"\r\n                  :rules=\"formItemRules\" @keydown.enter.native=\"(e) => { e.preventDefault(); }\">\r\n              <PersonSelectEx ref=\"personSelectRef\" @clearSelected=\"formItem.userIds = []\" groupName=\"role_manage_edit\" v-model=\"formItem.userIds\"/>\r\n            </Form>\r\n          </TabPane>\r\n        </Tabs>\r\n        <div class=\"drawer-footer\" style=\"border-top: none\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" v-if=\"modalType !== 'view'\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Role from \"@/api/base/role\";\r\nimport Common from '@/api/basic/common'\r\nimport Authority from \"@/api/system/authority_1\";\r\nimport {listConvertTree} from \"@/libs/util\";\r\nimport PersonSelectEx from \"./PersonSelectEx.vue\"; //引入人员选择组件\r\nimport {autoTableHeight} from \"@/libs/tools.js\";\r\n\r\nexport default {\r\n  name: \"systemRole\",\r\n  components: {\r\n    PersonSelectEx\r\n  },\r\n  data() {\r\n    const validateCode = (rule, value, callback) => {\r\n      let reg = /^[_a-zA-Z0-9]+$/;\r\n      if (value === \"\") {\r\n        callback(new Error(\"角色标识不能为空\"));\r\n      } else if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"只允许字母、数字、下划线\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.statusOps,\r\n      loading: false,\r\n      modalVisible: false,\r\n      modalTitle: \"\",\r\n      modalType: \"\",\r\n      loadTree: false,\r\n      modalRole: \"\",\r\n      saving: false,\r\n      current: \"form1\",\r\n      forms: [\r\n        \"form1\",\r\n        \"form2\",\r\n        \"form3\"\r\n      ],\r\n      selectMenus: [],\r\n      selectUsers: [],\r\n      selectShops: [],\r\n      selectShops1: [[]],\r\n      selectPlatforms: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n      },\r\n      formItemRules: {\r\n        roleCode: [{required: true, validator: validateCode, trigger: \"blur\"}],\r\n        roleName: [\r\n          {required: true, message: \"角色名称不能为空\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n        status: 0,\r\n        roleDesc: \"\",\r\n        grantMenus: [],\r\n        grantActions: [],\r\n        menuName: \"\",\r\n        userIds: [],\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          width: 60,\r\n          align: \"center\",\r\n        },\r\n        {\r\n          title: \"角色名称\",\r\n          key: \"roleName\",\r\n          width: 300,\r\n        },\r\n        {\r\n          title: \"角色标识\",\r\n          key: \"roleCode\",\r\n          width: 300,\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          slot: \"status\",\r\n          key: \"status\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"最后修改时间\",\r\n          key: \"updateTime\",\r\n          width: 200,\r\n        },\r\n        {\r\n          title: \"描述\",\r\n          key: \"roleDesc\",\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          width: 200,\r\n        },\r\n      ],\r\n      menuColumns: [\r\n        {\r\n          title: \"菜单\",\r\n          key: \"menuName\",\r\n          minWidth: \"250px\",\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          type: \"template\",\r\n          template: \"operation\",\r\n          minWidth: \"200px\",\r\n        },\r\n      ],\r\n      data: [],\r\n    };\r\n  },\r\n  methods: {\r\n    handleSearchMenu() {\r\n      this.handleLoadRoleGranted(this.formItem.id);\r\n    },\r\n    handleResetMenu() {\r\n      this.formItem.menuName = '';\r\n      this.handleLoadRoleGranted(this.formItem.id);\r\n    },\r\n    handleCheckAll(id) {\r\n      if (this.indeterminate) {\r\n        this.checkAll = true;\r\n      } else {\r\n        this.checkAll = !this.checkAll;\r\n      }\r\n      this.indeterminate = false;\r\n\r\n      let allData = [];\r\n      this.selectShops.map((item) => {\r\n        if (id === item.parentId) {\r\n          allData.push(item.id);\r\n        }\r\n      });\r\n      if (this.checkAll) {\r\n        this.checkAllGroup = allData;\r\n        this.checkAll = true;\r\n      } else {\r\n        this.checkAllGroup = [];\r\n        this.checkAll = false;\r\n      }\r\n    },\r\n    handleModal(data, type) {\r\n      if (!type && !this.modalType) {\r\n        //没有操作类型\r\n        return;\r\n      }\r\n      if (data) {\r\n        if (type === \"view\") {\r\n          this.modalType = \"view\";\r\n          this.modalRole = data.roleName;\r\n        }\r\n        if (type === \"edit\") {\r\n          this.modalType = \"edit\";\r\n          this.modalRole = data.roleName;\r\n        }\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else if (type === \"add\") {\r\n        this.modalType = \"add\";\r\n        this.modalRole = \"\";\r\n      }\r\n      /* 添加角色 */\r\n      if (this.current === this.forms[0]) {\r\n        if (this.modalType === \"view\") {\r\n          this.modalTitle = \"查看角色 - \" + this.modalRole;\r\n        }\r\n        if (this.modalType === \"edit\") {\r\n          this.modalTitle = \"编辑角色 - \" + this.modalRole;\r\n        }\r\n        if (this.modalType === \"add\") {\r\n          this.modalTitle = \"添加角色\";\r\n          this.$refs['form1'].resetFields();\r\n        }\r\n      }\r\n      /* 分配权限 */\r\n      if (this.current === this.forms[1] && (this.modalType === 'edit' || this.modalType === 'view')) {\r\n        this.modalTitle = \"分配权限 - \" + this.modalRole;\r\n        this.handleLoadRoleGranted(this.formItem.id);\r\n      }\r\n      /* 角色成员 */\r\n      if (this.current === this.forms[2] && (this.modalType === 'edit' || this.modalType === 'view')) {\r\n        this.modalTitle = \"角色成员 - \" + this.modalRole;\r\n        this.handleLoadRoleUsers(this.formItem.id);\r\n      }\r\n      this.modalVisible = true;\r\n    },\r\n    handleResetForm(form) {\r\n      this.$refs[form].resetFields();\r\n      if (form === 'searchForm') {\r\n        this.handleSearch(1);\r\n      }\r\n    },\r\n    handleTabClick(name) {\r\n      this.current = name;\r\n      this.handleModal();\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n        status: 0,\r\n        menuName: \"\",\r\n        roleDesc: \"\"\r\n      };\r\n      //重置验证\r\n      this.modalVisible = false;\r\n      this.forms.map((form) => {\r\n        this.handleResetForm(form);\r\n      });\r\n      this.current = this.forms[0];\r\n      this.formItem.userIds = [];\r\n      this.formItem.grantMenus = [];\r\n      this.formItem.grantActions = [];\r\n      this.saving = false;\r\n      const {personSelectRef} = this.$refs;\r\n      personSelectRef.onVisibleChange(false);\r\n    },\r\n    handleSubmit() {\r\n      if (this.current === this.forms[0]) {\r\n        this.$refs[this.current].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            if (this.formItem.id) {\r\n              Role.edit(this.formItem)\r\n                  .then((res) => {\r\n                    if (res['code'] === 0) {\r\n                      this.$Message.success(\"保存成功\");\r\n                      this.handleReset();\r\n                    }\r\n                    this.handleSearch();\r\n                  })\r\n                  .finally(() => {\r\n                    this.saving = false;\r\n                  });\r\n            } else {\r\n              Role.add(this.formItem)\r\n                  .then((res) => {\r\n                    if (res['code'] === 0) {\r\n                      this.$Message.success(\"保存成功\");\r\n                      this.handleReset();\r\n                    }\r\n                    this.handleSearch();\r\n                  })\r\n                  .finally(() => {\r\n                    this.saving = false;\r\n                  });\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (this.current === this.forms[1]) {\r\n        this.$refs[this.current].validate((valid) => {\r\n          if (valid) {\r\n            const authorityIds = this.getCheckedAuthorities();\r\n            for (const xItem of this.selectMenus) {\r\n              const childrenIds = xItem.children ? xItem.children.map((v) => v.authorityId) : [];\r\n              let flag = false;\r\n              for (const yItem of childrenIds) {\r\n                if (authorityIds.includes(yItem)) {\r\n                  flag = true;\r\n                  break;\r\n                }\r\n              }\r\n              if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                authorityIds.push(xItem.authorityId);\r\n              }\r\n            }\r\n            this.saving = true;\r\n            Authority.grantAuthorityForRole({\r\n              roleId: this.formItem.id,\r\n              authorityIds: authorityIds,\r\n            }).then((res) => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"授权成功\");\r\n                this.modalVisible = false;\r\n              }\r\n            }).finally(() => {\r\n              this.saving = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n\r\n      if (this.current === this.forms[2]) {\r\n        this.saving = true;\r\n        Role.addRoleUsers({\r\n          roleId: this.formItem.id,\r\n          userIds: this.formItem.userIds,\r\n        }).then((res) => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"保存成功\");\r\n            this.modalVisible = false;\r\n            this.handleReset();\r\n          }\r\n        }).finally(() => {\r\n          this.saving = false;\r\n        });\r\n      }\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      Role.listPage(this.pageInfo).then((res) => {\r\n        this.data = res.data.records;\r\n        this.pageInfo.total = parseInt(res.data.total);\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleRemove(data) {\r\n      this.$Modal.confirm({\r\n        title: \"确定删除吗？\",\r\n        onOk: () => {\r\n          Role.remove(data.id).then((res) => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(\"删除成功\");\r\n            }\r\n            this.handleSearch();\r\n          });\r\n        },\r\n      });\r\n    },\r\n    getCheckedAuthorities() {\r\n      const menus = this.$refs[\"tree\"].getCheckedProp(\"authorityId\");\r\n      return menus.concat(this.formItem.grantActions);\r\n    },\r\n    handleLoadRoleGranted() {\r\n      this.loadTree = true;\r\n      if (!this.formItem.id) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      const p1 = Authority.getAuthorityMenu(this.formItem.menuName);\r\n      const p2 = Authority.getAuthorityForRole(this.formItem.id);\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0 && res1.data) {\r\n          let opt = {primaryKey: \"id\", parentKey: \"parentId\", startPid: \"0\",};\r\n          if (res2.code === 0 && res2.data && res2.data.length > 0) {\r\n            let menus = [];\r\n            let actions = [];\r\n            res2.data.map((item) => {\r\n              // 菜单权限\r\n              if (item.authority.indexOf(\"MENU_\") !== -1 && !menus.includes(item.authorityId)) {\r\n                menus.push(item.authorityId);\r\n              }\r\n              // 操作权限\r\n              if (item.authority.indexOf(\"ACTION_\") !== -1 && !actions.includes(item.authorityId)) {\r\n                actions.push(item.authorityId);\r\n              }\r\n            });\r\n            that.formItem.grantMenus = menus;\r\n            that.formItem.grantActions = actions;\r\n          }\r\n          res1.data.map((item) => {\r\n            // 菜单选中\r\n            if (that.formItem.grantMenus.includes(item.authorityId)) {\r\n              item._isChecked = true;\r\n            }\r\n          });\r\n          if(that.formItem.menuName){\r\n            opt.startPid = Math.min.apply(Math, res1.data.map(function(item) {return item[opt.parentKey]}))+\"\";\r\n          }\r\n\r\n          that.selectMenus = listConvertTree(res1.data, opt);\r\n        }\r\n      }).finally(() => this.loadTree = false);\r\n    },\r\n    // id为角色ID\r\n    handleLoadRoleUsers(id) {\r\n      if (!id) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      Role.getRoleUsers(id).then((res) => {\r\n        if (res['code'] === 0) {\r\n          let userIds = [];\r\n          res.data.forEach((item) => userIds.push(item.id));\r\n          that.formItem.userIds = userIds;\r\n        }\r\n      })\r\n    }\r\n  },\r\n  mounted: function () {\r\n    this.handleSearch();\r\n  },\r\n};\r\n</script>\r\n"]}]}