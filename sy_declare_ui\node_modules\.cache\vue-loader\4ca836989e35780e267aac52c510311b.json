{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\InsidePurchase\\insidePurchase.vue?vue&type=style&index=0&id=33b97643&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\InsidePurchase\\insidePurchase.vue", "mtime": 1753761803419}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud2Vla2x5VGFibGUgew0KICAuaXZ1LXRhYmxlLXRib2R5IHsNCiAgICAuaXZ1LXRhYmxlLWV4cGFuZGVkLWNlbGwgew0KICAgICAgcGFkZGluZzogMDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["insidePurchase.vue"], "names": [], "mappings": ";AA8XA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "insidePurchase.vue", "sourceRoot": "src/view/module/custom/InsidePurchase", "sourcesContent": ["<!--\r\n@create date 2024-01-09\r\n<AUTHOR>\r\n@desc 内部采购订单\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchFormRef\" :model=\"searchForm\" inline @keydown.native.enter.prevent=\"handleSearch\">\r\n      <FormItem prop=\"date\">\r\n        <DatePicker type=\"daterange\"\r\n                    v-model=\"searchForm.date\"\r\n                    placement=\"bottom-start\"\r\n                    @on-change=\"dateChange\"\r\n                    placeholder=\"发货开始日期-发货结束日期\"\r\n                    style=\"width: 200px\">\r\n        </DatePicker>\r\n      </FormItem>\r\n      <FormItem prop=\"sheetNos\">\r\n        <div style=\"display: flex;\">\r\n          <Multiple placeholder=\"请输入发货单号(回车分隔)\" @changeValue=\"(values)=>{ searchForm.sheetNos = values || []; }\"\r\n                    width=\"600px\" :maxLength=\"100\" ref=\"multipleRef\" style=\"display: inline-flex;\"></Multiple>\r\n          <Dropdown trigger=\"custom\" :visible=\"popVisible\" style=\"margin-left: 3px\" :transfer=\"true\"\r\n                    transfer-class-name=\"orderBillDrop\">\r\n            <Button type=\"dashed\" @click=\"()=>{popVisible=true;}\">输入</Button>\r\n            <DropdownMenu slot=\"list\" class=\"poptipContentInxyz1\">\r\n              <Input v-model=\"popTipContent\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\"\r\n                     placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n              <div style=\"text-align: right; padding-top: 3px\">\r\n                <Button type=\"info\" size=\"small\" @click=\"closeDropdown()\">确定</Button>\r\n              </div>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n        </div>\r\n      </FormItem>\r\n      <FormItem prop=\"consignorIds\" >\r\n        <Select v-model=\"searchForm.consignorIds\"\r\n                label-in-value :clearable=\"true\" :multiple=\"true\"\r\n                placeholder=\"请输入境内发货人\"  class=\"widthClass\" style=\"width:200px;height: 35px;\"  >\r\n          <Option v-for=\"item in consignorList\" :value=\"item.id\" :key=\"item.id\">{{ item.consignorName }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"contractAgreementNos\">\r\n        <div style=\"display: flex;\">\r\n          <Multiple placeholder=\"请输入发货单号(回车分隔)\" @changeValue=\"(values)=>{ searchForm.contractAgreementNos = values || []; }\"\r\n                    width=\"600px\" :maxLength=\"100\" ref=\"multipleAgreementNoRef\" style=\"display: inline-flex;\"></Multiple>\r\n          <Dropdown trigger=\"custom\" :visible=\"popAgreementNoVisible\" style=\"margin-left: 3px\" :transfer=\"true\"\r\n                    transfer-class-name=\"orderBillDrop\">\r\n            <Button type=\"dashed\" @click=\"()=>{popAgreementNoVisible=true;}\">输入</Button>\r\n            <DropdownMenu slot=\"list\" class=\"poptipContentInxyz1\">\r\n              <Input v-model=\"popTipContentAgreementNo\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\"\r\n                     placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n              <div style=\"text-align: right; padding-top: 3px\">\r\n                <Button type=\"info\" size=\"small\" @click=\"closeDropdownAgreementNo()\">确定</Button>\r\n              </div>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n        </div>\r\n      </FormItem>\r\n      <FormItem prop=\"status\">\r\n        <Select v-model=\"searchForm.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"true\" :transfer=\"true\">\r\n          <Option :value=\"0\" :key=\"0\">未生成</Option>\r\n          <Option :value=\"1\" :key=\"1\">已生成</Option>\r\n          <Option :value=\"-1\" :key=\"-1\">生成失败</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\r\n        <Button style=\"margin-left:10px\" @click=\"handlerReset\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <Button type=\"primary\" @click=\"handleCreateShow()\" :loading=\"saving\">生成采购文件</Button>\r\n      <Button style=\"margin-left:10px\" @click=\"downloadFile\" :loading=\"saving\">\r\n        批量下载\r\n      </Button>\r\n      <Button style=\"margin-left:10px\" @click=\"deleteRow\" :loading=\"saving\" :disabled=\"selectedRows.length === 0\">\r\n        批量删除\r\n      </Button>\r\n    </div>\r\n    <Table :columns=\"columns\" :data=\"data\" :border=\"true\" :loading=\"loading\" class=\"weeklyTable\"\r\n           @on-selection-change=\"onSelectChange\" :transfer=\"true\" >\r\n      <template v-slot:sheetNos=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"left\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap;\">\r\n            {{ row.sheetNos }}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"width:100%\" v-copytext=\"row.sheetNos\">\r\n            {{ row.sheetNos.length > 50 ? (row.sheetNos.substring(0, 47) + '...') : row.sheetNos }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:remark=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"right-end\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n            {{ row.remark }}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"width:100%\" v-copytext=\"row.remark\">\r\n            {{ row.remark.length > 30 ? (row.remark.substring(0, 27) + '...') : row.remark }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:status=\"{ row }\">\r\n        <Badge v-if=\"!row.status || row.status === 0 \" color=\"gold\" text=\"未生成\"/>\r\n        <Badge v-if=\"row.status === 1\" color=\"green\" text=\"已生成\"/>\r\n        <Badge v-if=\"row.status === -1\" color=\"red\" text=\"生成失败\"/>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal v-model=\"fileModelVisible\" title=\"生成采购文件\" @on-cancel=\"()=>{fileModelVisible = false;this.$refs['poFormRef'].resetFields();}\" :width=\"600\" :loading=\"loading\">\r\n      <Form ref=\"poFormRef\" :model=\"fileForm\" :label-width=\"100\">\r\n        <FormItem label=\"合同协议号\" prop=\"contractAgreementNos\">\r\n          <Input v-model=\"fileForm.contractAgreementNos\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入合同协议号,多个以逗号或者换行符\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"drawer-footer\">\r\n        <Button type=\"default\" @click=\"()=>{fileModelVisible = false;this.$refs['poFormRef'].resetFields();}\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleCreate('create')\" :loading=\"loading\">保存</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport { isEmpty } from '@/libs/tools';\r\nimport InsidePurchase from \"@/api/custom/insidePurchase\";\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport Consignor from \"@/api/custom/consignor\";\r\n\r\nexport default {\r\n  name: 'InsidePurchaseFile',\r\n  components: { Multiple },\r\n  data () {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      selectedRows: [],\r\n      searchForm: {\r\n        date: [],\r\n        sheetNos: [],\r\n        consignorIds:null,\r\n        contractAgreementNos:[],\r\n        startDate: '',\r\n        endDate: '',\r\n        status:null\r\n      },\r\n      popTipContent:null,\r\n      popTipContentAgreementNo:null,\r\n      fileModelVisible:false,\r\n      fileForm:{contractAgreementNos: ''},\r\n      columns: [{\r\n        type: 'selection',\r\n        width: 60,\r\n        align: 'center',\r\n        fixed: 'left'\r\n      }, {\r\n        type: 'index', title: '#', width: 50,\r\n      }, {\r\n        title: '发货日期',\r\n        key: 'picDate',\r\n        width: 110,\r\n        align: 'center',\r\n        render: (_, { row }) => (\r\n            <span>\r\n                {row.picDate ? row.picDate.split(' ')[0] : ''}\r\n              </span>\r\n        )\r\n      }, {\r\n        title: '发货单号',\r\n        key: 'sheetNo',\r\n        minWidth: 130,\r\n        align: 'center'\r\n      }, {\r\n        title: '关联发货单号',\r\n        key: 'sheetNos',\r\n        minWidth: 400,\r\n        align: 'center',\r\n        slot: 'sheetNos'\r\n      }, {\r\n        title: '采购公司',\r\n        key: 'consignorName',\r\n        minWidth: 200,\r\n        align: 'center'\r\n      }, {\r\n        title: '合同协议号',\r\n        key: 'contractAgreementNo',\r\n        minWidth: 150,\r\n        align: 'center'\r\n      }, {\r\n        title: '生成状态',\r\n        key: 'status',\r\n        minWidth: 100,\r\n        align: 'center',\r\n        slot: 'status'\r\n      }, {\r\n        title: '备注',\r\n        key: 'remark',\r\n        minWidth: 200,\r\n        align: 'center',\r\n        slot: 'remark'\r\n      }],\r\n      pageInfo: {\r\n        page: 1,\r\n        limit: 10,\r\n        total: 0\r\n      },\r\n      data: [],\r\n      consignorList:[],\r\n      multiple: true,\r\n      popVisible: false,\r\n      popAgreementNoVisible:false,\r\n      personVisible: false\r\n    };\r\n  },\r\n  mounted () {\r\n    this.getAllConsignor();\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    getAllConsignor() {\r\n      Consignor.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.consignorList = res.data;\r\n        }\r\n      })\r\n    },\r\n    //表格选中行\r\n    onSelectChange (selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n    dateChange (date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    getParams(){\r\n      const getStr = value =>\r\n          value && Array.isArray(value) ? value.join(\",\") : undefined;\r\n\r\n      let params = {\r\n        ...this.pageInfo,...this.searchForm\r\n      };\r\n      delete params.sheetNos;delete params.consignorIds;delete params.date;delete params.contractAgreementNos;\r\n      params['sheetNos'] = getStr(this.searchForm['sheetNos']);\r\n      params['contractAgreementNos'] = getStr(this.searchForm['contractAgreementNos']);\r\n      params['consignorIds'] = getStr(this.searchForm['consignorIds']);\r\n      return params;\r\n    },\r\n    handleSearch () {\r\n      this.loading = true;\r\n      InsidePurchase.listPage(this.getParams()).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.data = res.data.records;\r\n              this.pageInfo.total = Number(res.data.total);\r\n            }\r\n          }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlerReset () {\r\n      const { multipleRef,multipleAgreementNoRef } = this.$refs;\r\n      if (multipleRef && multipleRef.setValueArray) {\r\n        multipleRef.setValueArray([]);\r\n      }\r\n      if (multipleAgreementNoRef && multipleAgreementNoRef.setValueArray) {\r\n        multipleAgreementNoRef.setValueArray([]);\r\n      }\r\n      this.pageInfo.page = 1;\r\n      this.dateChange();\r\n      this.$refs['searchFormRef'].resetFields();\r\n    },\r\n    deleteRow () {\r\n      if (this.selectedRows.length === 0) {\r\n        return;\r\n      }\r\n      const ids = this.selectedRows.map(v => v.id).join(',');\r\n      if (ids === null || ids === '') {\r\n        this.$Message.success('请选择需要删除的记录！');\r\n        return;\r\n      }\r\n      let that = this;\r\n      this.$Modal.confirm({\r\n        title: '确认删除已选数据吗？',\r\n        content: '温馨提示：数据删除后需要重新生成，请谨慎操作！',\r\n        onOk: () => {\r\n          InsidePurchase.remove({\"ids\":ids}).then(res => {\r\n                if (res['code'] === 0) {\r\n                  that.handleSearch();\r\n                  that.selectedRows = [];\r\n                }\r\n              }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    downloadFile () {\r\n      this.loading = true;\r\n      InsidePurchase.listPage(this.getParams()).then(res => {\r\n          if (res['code'] === 0) {\r\n            let total = Number(res.data.total);\r\n            if(total<=0){\r\n              this.$Message.success('没有下载的记录');\r\n              return;\r\n            }\r\n            this.$Message.info('开始下载');\r\n            for (let i = 0; i < res.data.records.length; i++) {\r\n              let row = res.data.records[i];\r\n              setTimeout(()=>{\r\n                InsidePurchase.download({\"id\":row['id'],\"fileName\":row['contractAgreementNo']+\"采购合同.xls\"},()=>{})\r\n              }, i*300)\r\n            }\r\n          }\r\n        }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleCreateShow(){\r\n      this.fileModelVisible=true;\r\n    },\r\n    handleCreate(){\r\n      let arr = [];\r\n      this.fileForm.contractAgreementNos.split(',').map((each)=>{each.split('\\n').map((item)=>{if(item){arr.push(item);}})});\r\n      if(arr.length>50){\r\n        this.$Message.success('合同协议号数超过50,单次最大支持50个');\r\n        return;\r\n      }\r\n      this.loading=true;\r\n      InsidePurchase.createInsidePurchase({\"contractAgreementNos\":arr.join(\",\")}).then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success('生成成功！');\r\n            this.fileModelVisible = false;\r\n          } else {\r\n            this.$Message.success(res['message']);\r\n          }\r\n        }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlePage (page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize (size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    closeDropdown () { //关闭输入文本框\r\n      const { popTipContent } = this;\r\n      const { multipleRef } = this.$refs;\r\n      this.popVisible = false;\r\n      if (!popTipContent) return;\r\n      const content = popTipContent ? popTipContent.trim().replace(/，/g, ',') : '';\r\n      this.searchForm.sheetNos = content.split('\\n').filter(v => !!v);\r\n      this.searchForm.sheetNos = [...new Set(this.searchForm.sheetNos)];\r\n      if (multipleRef && multipleRef.setValueArray) {\r\n        multipleRef.setValueArray(this.searchForm.sheetNos);\r\n      }\r\n      this.popTipContent = undefined;\r\n    },\r\n    closeDropdownAgreementNo(){\r\n      const { popTipContentAgreementNo } = this;\r\n      const { multipleAgreementNoRef } = this.$refs;\r\n      this.popAgreementNoVisible = false;\r\n      if (!popTipContentAgreementNo) return;\r\n      const content = popTipContentAgreementNo ? popTipContentAgreementNo.trim().replace(/，/g, ',') : '';\r\n      this.searchForm.contractAgreementNos = content.split('\\n').filter(v => !!v);\r\n      this.searchForm.contractAgreementNos = [...new Set(this.searchForm.contractAgreementNos)];\r\n      if (multipleAgreementNoRef && multipleAgreementNoRef.setValueArray) {\r\n        multipleAgreementNoRef.setValueArray(this.searchForm.contractAgreementNos);\r\n      }\r\n      this.popTipContentAgreementNo = undefined;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.weeklyTable {\r\n  .ivu-table-tbody {\r\n    .ivu-table-expanded-cell {\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}