# Nacos workflow.properties 配置文件

## 配置说明
此文件需要在Nacos配置中心创建，配置ID为 `workflow.properties`，分组为 `DEFAULT_GROUP`。

## 完整配置内容

```properties
# ========================================
# 数据源基本配置
# ========================================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=********************************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.type=com.zaxxer.hikari.HikariDataSource

# 注意：配置中使用 spring.datasource.url，代码中会自动转换为 HikariCP 需要的 jdbcUrl

# ========================================
# HikariCP 连接池配置
# ========================================
# 连接池最大连接数
spring.datasource.hikari.maximum-pool-size=20
# 连接池最小空闲连接数
spring.datasource.hikari.minimum-idle=5
# 连接超时时间（毫秒）
spring.datasource.hikari.connection-timeout=30000
# 空闲连接超时时间（毫秒）
spring.datasource.hikari.idle-timeout=600000
# 连接最大生命周期（毫秒）
spring.datasource.hikari.max-lifetime=1800000
# 连接泄漏检测阈值（毫秒）
spring.datasource.hikari.leak-detection-threshold=60000
# 连接池名称
spring.datasource.hikari.pool-name=ErpNacosPool
# 连接测试查询
spring.datasource.hikari.connection-test-query=SELECT 1
# 自动提交
spring.datasource.hikari.auto-commit=true
# 只读模式
spring.datasource.hikari.read-only=false
# 连接初始化SQL
# spring.datasource.hikari.connection-init-sql=SET NAMES utf8mb4

# ========================================
# 其他可选配置
# ========================================
# 数据库连接验证超时时间
spring.datasource.hikari.validation-timeout=5000
# 是否在获取连接时验证连接
spring.datasource.hikari.validate-on-borrow=true
# 是否在归还连接时验证连接
spring.datasource.hikari.validate-on-return=false
# 是否在连接空闲时验证连接
spring.datasource.hikari.validate-while-idle=true
```

## 配置参数说明

### 基本配置
- **driver-class-name**: MySQL 8.0+ 驱动类
- **url**: 数据库连接URL，包含必要的参数
  - `useSSL=false`: 禁用SSL（开发环境）
  - `useUnicode=true`: 启用Unicode支持
  - `characterEncoding=utf-8`: 字符编码
  - `allowMultiQueries=true`: 允许多语句查询
  - `rewriteBatchedStatements=true`: 批量操作优化
  - `serverTimezone=Asia/Shanghai`: 时区设置
  - `allowPublicKeyRetrieval=true`: 允许公钥检索
- **username**: 数据库用户名
- **password**: 数据库密码
- **type**: 数据源类型，使用HikariCP

### 连接池配置
- **maximum-pool-size**: 连接池最大连接数，建议根据应用负载调整
- **minimum-idle**: 最小空闲连接数，保证基本可用性
- **connection-timeout**: 连接超时时间，30秒
- **idle-timeout**: 空闲连接超时，10分钟
- **max-lifetime**: 连接最大生命周期，30分钟
- **leak-detection-threshold**: 连接泄漏检测，60秒

## 环境配置建议

### 开发环境
```properties
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.leak-detection-threshold=60000
```

### 测试环境
```properties
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.minimum-idle=3
spring.datasource.hikari.leak-detection-threshold=30000
```

### 生产环境
```properties
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.leak-detection-threshold=0
```

## 配置验证

### 1. 在Nacos控制台验证
- 登录Nacos控制台
- 进入配置管理 -> 配置列表
- 确认 `workflow.properties` 配置存在且内容正确

### 2. 应用启动验证
启动应用后查看日志：
```
🔄 开始创建Nacos数据源配置
📊 所有配置（包括连接池配置）都将从Nacos的workflow.properties中获取
✅ Nacos数据源配置创建成功
```

### 3. 连接池状态检查
可以通过JMX或应用监控查看HikariCP连接池状态。

## 注意事项

1. **密码安全**: 生产环境建议使用Nacos的配置加密功能
2. **配置刷新**: 修改配置后可以动态刷新，无需重启应用
3. **备份**: 定期备份Nacos配置数据
4. **监控**: 监控数据库连接池的使用情况

---
**配置版本**: v1.0
**更新时间**: 2025-08-05
**适用环境**: 开发/测试/生产
