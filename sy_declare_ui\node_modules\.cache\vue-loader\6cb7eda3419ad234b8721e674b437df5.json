{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue?vue&type=template&id=26384284&scoped=true&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\workflow\\index.vue", "mtime": 1754293498182}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}