package com.sy.erp.server.configuration;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 简化数据源配置 - 使用本地配置
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "false", matchIfMissing = true)
public class SimpleDataSourceConfiguration {

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String datasourceUsername;

    @Value("${spring.datasource.password}")
    private String datasourcePassword;

    @Value("${spring.datasource.driver-class-name}")
    private String datasourceDriverClassName;

    /**
     * 主数据源 - 使用本地配置
     */
    @Bean(name = "dataSource")
    @Primary
    public DataSource dataSource() {
        try {
            log.info("🔄 开始创建主数据源（本地配置）");

            log.info("📊 数据源配置:");
            log.info("🔗 数据库URL: {}", datasourceUrl);
            log.info("👤 用户名: {}", datasourceUsername);

            HikariDataSource dataSource = new HikariDataSource();
            dataSource.setJdbcUrl(datasourceUrl);
            dataSource.setUsername(datasourceUsername);
            dataSource.setPassword(datasourcePassword);
            dataSource.setDriverClassName(datasourceDriverClassName);

            // 连接池配置
            dataSource.setMaximumPoolSize(20);
            dataSource.setMinimumIdle(5);
            dataSource.setConnectionTimeout(30000);
            dataSource.setIdleTimeout(600000);
            dataSource.setMaxLifetime(1800000);
            dataSource.setLeakDetectionThreshold(60000);

            // 连接池名称
            dataSource.setPoolName("ErpMainPool");

            log.info("✅ 主数据源创建成功");
            return dataSource;

        } catch (Exception e) {
            log.error("❌ 主数据源创建失败: {}", e.getMessage());
            throw new RuntimeException("主数据源初始化失败", e);
        }
    }

    /**
     * 为了兼容动态数据源，创建相同的数据源实例
     */
    @Bean(name = "aimoProdMysql")
    public DataSource aimoProdMysql() {
        log.info("🔄 创建aimoProdMysql数据源（使用主数据源配置）");
        return dataSource();
    }

    @Bean(name = "aimoTestMysql")
    public DataSource aimoTestMysql() {
        log.info("🔄 创建aimoTestMysql数据源（使用主数据源配置）");
        return dataSource();
    }


}
