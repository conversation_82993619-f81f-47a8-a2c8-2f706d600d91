{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearanceInfo\\ClearanceModel1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearanceInfo\\ClearanceModel1.vue", "mtime": 1752737748522}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ClearanceModel1.vue"], "names": [], "mappings": ";AA2GA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ClearanceModel1.vue", "sourceRoot": "src/view/module/custom/clearance/clearanceInfo", "sourcesContent": ["<!--\r\n@create date 2021-08-06\r\n@desc 清关公共模块\r\n-->\r\n<template>\r\n  <div>\r\n    <Form ref=\"formInfo\" :model=\"formInfo\" inline>\r\n      <FormItem prop=\"fbaNo\" label=\"FBA单号\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['thdOrder']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n        <Button style=\"margin: 0 10px\" v-copytext=\"formInfo['thdOrder']\">复制</Button>\r\n      </FormItem>\r\n      <FormItem prop=\"boxQty\" label=\"箱数\" :label-width=\"40\">\r\n        <Input v-model=\"formInfo['boxQty']\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"whCode\" label=\"地址速记码\" :label-width=\"75\">\r\n        <Input v-model=\"formInfo['whCode']\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"isUrl\" label=\"是否需要链接\" :label-width=\"85\">\r\n        <RadioGroup v-model=\"formInfo['isUrl']\" @on-change=\"changeRadio1\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"isPhoto\" label=\"是否需要图片\" :label-width=\"85\">\r\n        <RadioGroup v-model=\"formInfo['isPhoto']\" @on-change=\"changeRadio2\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"thdRef\" label=\"货件追踪编号\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['thdRef']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"address\" label=\"收货地址\" :label-width=\"70\">\r\n        <Input v-model=\"formInfo['address']\"  style=\"width:430px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"currency\" label=\"币种\" :label-width=\"60\">\r\n        <Input v-model=\"currencyList.filter(item=>item['id'] === formInfo['currency']).map(item=>item['code'])[0]\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"taxNoAddress\" label=\"税号地址\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['taxNoAddress']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"taxCompany\" label=\"税号公司\" :label-width=\"70\" v-show=\"formInfo.isCompany === 1\">\r\n        <Input v-model=\"formInfo['taxCompany']\"  style=\"width:430px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"isCompany\" label=\"是否需要税号公司\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo['isCompany']\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" :disabled=\"true\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem v-show=\"!this.id\" prop=\"isSpuName\" label=\"是否需要型号\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo['isSpuName']\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem  label=\"是否导出单品重量\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo.isWeight\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"numProportion\" label=\"数量比例%\" :label-width=\"80\">\r\n        <InputNumber v-model=\"formInfo['qtyPercent']\"\r\n                     :readonly=\"!!this.id\"  :disabled=\"!!this.id\"\r\n                     @on-change=\"changeQtyPercent\"\r\n                     @on-blur=\"changeQtyPercent\"\r\n                     :formatter=\"value => `${value}%`\"\r\n                     :parser=\"value => value.replace('%', '')\"  style=\"width:100px\">\r\n        </InputNumber>\r\n      </FormItem>\r\n      <FormItem  label=\"是否使用产品价\" :label-width=\"110\" v-if=\"!this.id\">\r\n        <RadioGroup v-model=\"formInfo['isSalePrice']\" @on-change=\"changePriceType\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"productDiscount\" label=\"产品价折扣%\" :label-width=\"100\" v-if=\"!this.id\">\r\n        <InputNumber v-model=\"formInfo['salePriceDisCount']\"\r\n                     :min=\"0\"\r\n                     :max=\"100\"\r\n                     @on-change=\"changePriceDisCount\"\r\n                     @on-blur=\"changePriceDisCount\"\r\n                     :formatter=\"value => `${value}%`\"\r\n                     :parser=\"value => value.replace('%', '')\"  style=\"width:100px\">\r\n        </InputNumber>\r\n      </FormItem>\r\n    </Form>\r\n    <Table :columns=\"columns1\" :show-summary=\"true\" :summary-method=\"handleSummary\" :data=\"detailData\" max-height=\"500\" :border=\"true\" :key=\"keyData\">\r\n      <template v-slot:urlTemp=\"{row,index}\">\r\n        <Poptip :word-wrap=\"true\" width=\"200\" trigger=\"hover\" :content=\"row['goodsUrl']\">\r\n          <div  v-copytext=\"row['goodsUrl']\" style=\"width:100px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;\">{{row['goodsUrl']}}</div>\r\n        </Poptip>\r\n      </template>\r\n      <template v-slot:imgTemp=\"{row,index}\">\r\n        <img v-if=\"!!row['photoUrl']\" v-change-imj=\"row['photoUrl']\" style=\"max-width: 100px;max-height: 60px;\" alt=\"商品图片\" src=\"\">\r\n      </template>\r\n      <template v-slot:spuName=\"{row,index}\">\r\n        <span>{{formInfo['isSpuName'] === 0 ? '' : row['spuName']}}</span>\r\n      </template>\r\n      <template v-slot:currencyName=\"{row,index}\">\r\n        <span v-for=\"item in currencyList\" v-if=\"item['id'] === row.currency\">{{ item['code'] }}</span>\r\n      </template>\r\n    </Table>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Currency from \"@/api/basf/currency\";\r\n\r\nexport default {\r\n  data(){\r\n    return{\r\n      loading:false,\r\n      clearanceRank:null,\r\n      detailData:[],\r\n      keyData:new Date().getTime(),\r\n      id:null,//导出id；\r\n      formInfo:{\r\n        thdOrder:'',\r\n        boxQty:null,\r\n        whCode:null,\r\n        isUrl:0,\r\n        isPhoto:0,\r\n        thdRef:null,\r\n        address:null,\r\n        currency:null,\r\n        taxNoAddress:null,\r\n        taxCompany:null,\r\n        isCompany:0,\r\n        isWeight:0,\r\n        isSpuName:0,\r\n        qtyPercent:100,\r\n        isSalePrice:0,\r\n        salePriceDisCount:null,\r\n      },\r\n      columns1:[\r\n        {title: '箱号',  key: 'boxNo',  width: 220,  align: 'center',},\r\n        {title: '尺寸(CM)',  key: 'size',  width:100,  align: 'center',},\r\n        {title: '总毛重(Kg)',  key: 'grossWeight',  width:100,  align: 'center',},\r\n        {title: '特殊属性',  key: 'attribute',  width:100,  align: 'center',},\r\n        {title: '订单箱子详情',  key: 'boxDetail',  align: 'center',children:\r\n            [{  title: '海关编码',  key: 'hsCode',  align: 'center',  width:110,},\r\n            {  title: '中文品名',  key: 'customName',  align: 'center',  width:110,},\r\n            {  title: '英文品名',  key: 'customNameEn',  align: 'center',  width:180,},\r\n            {  title: '型号',  key: 'spuName',  align: 'center',  width:150, slot:'spuName',},\r\n            {  title: '品牌',  key: 'brand',  align: 'center',  width:100,},\r\n            {  title: '材质',  key: 'material',  align: 'center',  width:130, tooltip:true},\r\n            {  title: '用途',  key: 'purpose',  align: 'center',  width:130, tooltip:true},\r\n            {  title: '数量',  key: 'qtyTemp',  align: 'center',  width:90,},\r\n            {  title: '单个重量(Kg)',  key: 'unitWeight',  align: 'center',  width:90,},\r\n            {  title: '币种',  key: 'currency',  align: 'center',  width:90,slot:'currencyName'},\r\n            {  title: '单价',  key: 'unitPrice',  align: 'center',  width:90,}]}],\r\n    }\r\n  },\r\n  props: {\r\n    clearanceVisible: {\r\n      type: Boolean,\r\n    },\r\n    currencyList:{type:Array},\r\n    onCancel: { type: Function },\r\n  },\r\n  methods:{\r\n    setDefault(data){\r\n      this.formInfo = data;\r\n      this.formInfo['isWeight']=0;\r\n      this.id = data['id'];\r\n      this.changeRadio1(data['isUrl']);\r\n      this.changeRadio2(data['isPhoto']);\r\n      this.detailData = data['detailList'];\r\n      this.detailData.forEach(item=>{\r\n        item.qtyTemp = Math.round(item.qty*(this.formInfo['qtyPercent'] / 100))\r\n      })\r\n      this.changePriceType(this.formInfo['isSalePrice']);\r\n    },\r\n    getParams(){\r\n      let param = {...this.formInfo};\r\n      param['detailList'] = this.detailData;\r\n      return param;\r\n    },\r\n    keepSaveSuccess(id){\r\n      this.id = id;\r\n    },\r\n    changeQtyPercent(){\r\n      let that = this;\r\n      this.detailData.forEach(item=>{\r\n        console.log(item.qty*(that.formInfo['qtyPercent'] / 100));\r\n        item.qtyTemp = Math.round(item.qty*(that.formInfo['qtyPercent'] / 100))\r\n      })\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changePriceType(v){\r\n      this.formInfo.salePriceDisCount = v === 0 ? null : 100;\r\n      if(v === 0){\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] =item['price'];\r\n        })\r\n      }else{\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] =item['salePrice'];\r\n        })\r\n      }\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changePriceDisCount(){\r\n      if(this.formInfo.isSalePrice === 0){\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] = item.price\r\n        })\r\n      }else{\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] = this.formatFloat(item['salePrice']*(this.formInfo.salePriceDisCount / 100),2)\r\n        })\r\n      }\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changeRadio(flag,obj){\r\n      let currentIndex = (this.columns1[4].children|| []).findIndex((item) => item.key === obj.key);\r\n      if(flag === 1){\r\n        if(currentIndex === -1){\r\n          this.columns1[4].children.push(obj);\r\n        }\r\n      }else{\r\n        if(currentIndex !== -1){\r\n          this.columns1[4].children.splice(currentIndex,1);\r\n        }\r\n      }\r\n    },\r\n    changeRadio1(v){\r\n      this.changeRadio(v,{title: '商品链接', key: 'goodsUrl', width:150, slot:'urlTemp', align: 'center',});\r\n    },\r\n    changeRadio2(v){\r\n      this.changeRadio(v,{title: '商品图片', key: 'photoUrl', slot:'imgTemp', align: 'center', width:80,});\r\n    },\r\n    handleSummary ({ columns}) {\r\n      const sums = {};\r\n      columns.forEach((column, index) => {\r\n        const key = column.key;\r\n        if (index === 0) {\r\n          sums[key] = {key, value: '合计'};\r\n          return;\r\n        }\r\n        if(key === 'grossWeight'){\r\n          sums[key] = {key, value:this.formInfo['grossWeight']};\r\n          return;\r\n        }\r\n        if(key === 'qty'){\r\n          sums[key] = {key, value:this.formInfo['qty']};\r\n        }else{\r\n          sums[key] = {key, value:'--'};\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    // 浮点计算\r\n    formatFloat(f,digit){\r\n      let m = Math.pow(10, digit);\r\n      return Math.round(f * m) / m;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n"]}]}