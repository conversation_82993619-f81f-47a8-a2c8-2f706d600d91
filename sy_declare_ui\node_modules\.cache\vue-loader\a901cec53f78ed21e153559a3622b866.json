{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue?vue&type=style&index=0&id=35ab9eed&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue", "mtime": 1752737748520}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud29yZHNCb3h7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi1ib3R0b206IDVweDsNCn0NCi5kaXZXaWR0aHsNCiAgd2lkdGg6MzMwcHg7DQp9DQoud29yZExlZnR7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgd2lkdGg6IDEwMHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgd2hpdGUtc3BhY2U6bm93cmFwOw0KfQ0KLldvcmRzUmlnaHR7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgd2lkdGg6IDIyMHB4Ow0KICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogIHdvcmQtYnJlYWs6IG5vcm1hbDsNCn0NCg=="}, {"version": 3, "sources": ["indexView.vue"], "names": [], "mappings": ";AAoHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "indexView.vue", "sourceRoot": "src/view/module/custom/base/customClass", "sourcesContent": ["<template>\r\n  <Modal :width=\"1040\" :value=\"modelViewVisible\" :mask-closable=\"false\" :title=\"'查看报关类目'\" @on-cancel=\"onCancel\">\r\n    <Spin :fix=\"true\" v-if=\"spin\">加载中...</Spin>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">上级目录:</span><span class=\"WordsRight\">{{customModel.parentClassName}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">类目名称:</span><span class=\"WordsRight\">{{customModel.className}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">产品型号:</span><span class=\"WordsRight\">{{customModel.categoryName}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">中文报关名:</span><span  class=\"WordsRight\">{{customModel.customNameCn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">英文报关名:</span><span class=\"WordsRight\">{{customModel.customNameEn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关海关编码:</span><span class=\"WordsRight\">{{customModel.hsCode}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">材质:</span><span class=\"WordsRight\">{{customModel.material}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">用途:</span><span class=\"WordsRight\">{{customModel.purpose}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关单位:</span><span class=\"WordsRight\">{{customModel.unit}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div style=\"width:400px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">申报要素</p>\r\n          <Table :border=\"true\" :columns=\"declareColumn\" :data=\"declareData\"></Table>\r\n        </Card>\r\n      </div>\r\n      <div style=\"width:600px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">清关资料</p>\r\n          <Table :border=\"true\" :columns=\"clearanceColumn\" :data=\"clearanceData\"></Table>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" @click=\"onCancel\">关闭</Button>&nbsp;\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport {autoTableHeight} from \"@/libs/tools\";\r\nimport Category from \"@/api/custom/customClass\";\r\nexport default {\r\n  name: 'CategoryView',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading:false,\r\n      spin:false,\r\n      id:null,\r\n      customModel:{},\r\n      declareColumn:[{title: '类型',key: 'decKey', minWidth: 120, align: 'center'},\r\n        {title: '内容', key: 'content',minWidth: 120, align: 'center'}],\r\n      clearanceColumn:[{title: '国家',key: 'countryName', minWidth: 120, align: 'center'},\r\n        {title: '清关编码', key: 'hsCode',minWidth: 120, align: 'center'},\r\n        {title: '清关价格', key: 'price',minWidth: 120, align: 'center'},\r\n        {title: '清关币种', key: 'currencyName',minWidth: 120, align: 'center'}],\r\n      declareData:[],\r\n      clearanceData:[],\r\n      data:[]\r\n    }\r\n  },\r\n  props: {\r\n    onCancel: { type: Function },\r\n    modelViewVisible: {\r\n      type: Boolean,\r\n    },\r\n    allData:{type:Array},\r\n    currencyList:{type:Array},\r\n    countryList:{type:Array},\r\n  },\r\n  methods: {\r\n    setDefault(id) {\r\n      this.id = id;\r\n      this.loadData();\r\n    },\r\n    loadData(){\r\n      this.customModel={};\r\n      this.spin = true;\r\n      Category.getBy({\"id\": this.id,\"calc\":true}).then(res=>{\r\n        if (res['code'] === 0) {\r\n          this.customModel = res.data;\r\n          this.allData.forEach(item => {\r\n            if (item['id'] === this.customModel.parentId) {\r\n              this.customModel.parentClassName = item['className'];\r\n            }\r\n          })\r\n          if(!this.customModel.categoryName){\r\n            this.customModel.categoryName=null;\r\n          }\r\n          this.declareData = res.data['declarationElementList'];\r\n          this.clearanceData = res.data['clearanceElementList'];\r\n          if(this.clearanceData && this.countryList.length>0){\r\n            let countryObj = {};\r\n            this.countryList.forEach(item=>countryObj[item['two_code']] = item['name_cn'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['countryName']= countryObj[item['country']]\r\n            })\r\n            let currencyObj = {};\r\n            this.currencyList.forEach(item=>currencyObj[item['id']] = item['name'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['currencyName']= currencyObj[item['currency']]\r\n            })\r\n          }\r\n        }\r\n      }).finally(()=>{this.spin = false;})\r\n    },\r\n    ChangeImg(){\r\n      this.spin = true;\r\n      this.loadData();\r\n    },\r\n  },\r\n  mounted: function () {\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.wordsBox{\r\n  display: flex;\r\n  margin-bottom: 5px;\r\n}\r\n.divWidth{\r\n  width:330px;\r\n}\r\n.wordLeft{\r\n  display: inline-block;\r\n  width: 100px;\r\n  font-weight: bold;\r\n  white-space:nowrap;\r\n}\r\n.WordsRight{\r\n  position: relative;\r\n  width: 220px;\r\n  word-wrap: break-word;\r\n  word-break: normal;\r\n}\r\n</style>\r\n\r\n"]}]}