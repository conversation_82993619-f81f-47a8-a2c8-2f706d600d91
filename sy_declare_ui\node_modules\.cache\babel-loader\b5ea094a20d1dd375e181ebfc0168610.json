{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\department.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\department.js", "mtime": 1752737748398}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwp2YXIgZGVwYXJ0bWVudFBhdGggPSAiL2Jhc2UvZGVwYXJ0bWVudCI7Ci8qKg0KICog6I635Y+W6I+c5Y2V5YiX6KGoDQogKi8KZXhwb3J0IHZhciBnZXRCeUNvbXBhbnlJZCA9IGZ1bmN0aW9uIGdldEJ5Q29tcGFueUlkKGNvbXBhbnlJZCkgewogIHZhciBwYXJhbXMgPSB7CiAgICBjb21wYW55SWQ6IGNvbXBhbnlJZAogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBkZXBhcnRtZW50UGF0aCArICcvZ2V0QnlDb21wYW55SWQnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CmV4cG9ydCB2YXIgZ2V0QWxsID0gZnVuY3Rpb24gZ2V0QWxsKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogZGVwYXJ0bWVudFBhdGggKyAnL2dldEFsbCcsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CgovKioNCiAqIOa3u+WKoOmDqOmXqA0KICogQHBhcmFtIGRhdGEg5YyF5ZCr77yaY29tcGFueUlkLHBhcmVudElkKGRlZmF1bHQ6MCxyZXF1aXJlZD1mYWxzZSksbWFuYWdlcklkKGRlZmF1bHQ6bnVsbCxyZXF1aXJlZD1mYWxzZSksZGVwYXJ0bWVudE5hbWUsc3RhdHVzDQogKi8KZXhwb3J0IHZhciBhZGQgPSBmdW5jdGlvbiBhZGQoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogZGVwYXJ0bWVudFBhdGggKyAnL2FkZERlcGFydG1lbnQnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CgovKioNCiAqIOabtOaWsOmDqOmXqA0KICogQHBhcmFtIGRhdGEg5YyF5ZCr77yaaWQsY29tcGFueUlkLHBhcmVudElkKGRlZmF1bHQ6MCxyZXF1aXJlZD1mYWxzZSksbWFuYWdlcklkKGRlZmF1bHQ6bnVsbCxyZXF1aXJlZD1mYWxzZSksZGVwYXJ0bWVudE5hbWUsc3RhdHVzDQogKi8KZXhwb3J0IHZhciBlZGl0ID0gZnVuY3Rpb24gZWRpdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBkZXBhcnRtZW50UGF0aCArICcvZWRpdERlcGFydG1lbnQnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07Ci8qKg0KICog5Yig6Zmk6I+c5Y2VDQogKiBAcGFyYW0gaWQNCiAqLwpleHBvcnQgdmFyIHJlbW92ZSA9IGZ1bmN0aW9uIHJlbW92ZShpZCkgewogIHZhciBkYXRhID0gewogICAgaWQ6IGlkCiAgfTsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGRlcGFydG1lbnRQYXRoICsgJy9yZW1vdmVEZXBhcnRtZW50JywKICAgIGRhdGE6IGRhdGEsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9OwpleHBvcnQgZGVmYXVsdCB7CiAgZ2V0QWxsOiBnZXRBbGwsCiAgZ2V0QnlDb21wYW55SWQ6IGdldEJ5Q29tcGFueUlkLAogIGFkZDogYWRkLAogIGVkaXQ6IGVkaXQsCiAgcmVtb3ZlOiByZW1vdmUKfTs="}, {"version": 3, "names": ["request", "departmentPath", "getByCompanyId", "companyId", "params", "url", "method", "getAll", "add", "data", "edit", "remove", "id"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/department.js"], "sourcesContent": ["import request from '@/libs/request'\r\nconst departmentPath = \"/base/department\";\r\n/**\r\n * 获取菜单列表\r\n */\r\nexport const getByCompanyId = (companyId) => {\r\n  const params = {\r\n    companyId: companyId,\r\n  }\r\n  return request({\r\n    url: departmentPath+'/getByCompanyId',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport const getAll = () => {\r\n  return request({\r\n    url: departmentPath+'/getAll',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 添加部门\r\n * @param data 包含：companyId,parentId(default:0,required=false),managerId(default:null,required=false),departmentName,status\r\n */\r\nexport const add = (data) => {\r\n  return request({\r\n    url: departmentPath+'/addDepartment',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 更新部门\r\n * @param data 包含：id,companyId,parentId(default:0,required=false),managerId(default:null,required=false),departmentName,status\r\n */\r\nexport const edit = (data) => {\r\n  return request({\r\n    url: departmentPath+'/editDepartment',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 删除菜单\r\n * @param id\r\n */\r\nexport const remove = (id) => {\r\n  const data = {\r\n    id: id\r\n  }\r\n  return request({\r\n    url: departmentPath+'/removeDepartment',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\nexport default {\r\n    getAll,\r\n    getByCompanyId,\r\n    add,\r\n    edit,\r\n    remove\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,IAAMC,cAAc,GAAG,kBAAkB;AACzC;AACA;AACA;AACA,OAAO,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAS,EAAK;EAC3C,IAAMC,MAAM,GAAG;IACbD,SAAS,EAAEA;EACb,CAAC;EACD,OAAOH,OAAO,CAAC;IACbK,GAAG,EAAEJ,cAAc,GAAC,iBAAiB;IACrCG,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;EAC1B,OAAOP,OAAO,CAAC;IACbK,GAAG,EAAEJ,cAAc,GAAC,SAAS;IAC7BK,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAME,GAAG,GAAG,SAANA,GAAGA,CAAIC,IAAI,EAAK;EAC3B,OAAOT,OAAO,CAAC;IACbK,GAAG,EAAEJ,cAAc,GAAC,gBAAgB;IACpCQ,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAMI,IAAI,GAAG,SAAPA,IAAIA,CAAID,IAAI,EAAK;EAC5B,OAAOT,OAAO,CAAC;IACbK,GAAG,EAAEJ,cAAc,GAAC,iBAAiB;IACrCQ,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,IAAMK,MAAM,GAAG,SAATA,MAAMA,CAAIC,EAAE,EAAK;EAC5B,IAAMH,IAAI,GAAG;IACXG,EAAE,EAAEA;EACN,CAAC;EACD,OAAOZ,OAAO,CAAC;IACbK,GAAG,EAAEJ,cAAc,GAAC,mBAAmB;IACvCQ,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,eAAe;EACXC,MAAM,EAANA,MAAM;EACNL,cAAc,EAAdA,cAAc;EACdM,GAAG,EAAHA,GAAG;EACHE,IAAI,EAAJA,IAAI;EACJC,MAAM,EAANA;AACJ,CAAC"}]}