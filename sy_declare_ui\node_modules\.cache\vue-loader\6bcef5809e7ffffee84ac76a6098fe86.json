{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\customClass\\indexView.vue", "mtime": 1752737748520}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["indexView.vue"], "names": [], "mappings": ";AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "indexView.vue", "sourceRoot": "src/view/module/custom/base/customClass", "sourcesContent": ["<template>\r\n  <Modal :width=\"1040\" :value=\"modelViewVisible\" :mask-closable=\"false\" :title=\"'查看报关类目'\" @on-cancel=\"onCancel\">\r\n    <Spin :fix=\"true\" v-if=\"spin\">加载中...</Spin>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">上级目录:</span><span class=\"WordsRight\">{{customModel.parentClassName}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">类目名称:</span><span class=\"WordsRight\">{{customModel.className}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">产品型号:</span><span class=\"WordsRight\">{{customModel.categoryName}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">中文报关名:</span><span  class=\"WordsRight\">{{customModel.customNameCn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">英文报关名:</span><span class=\"WordsRight\">{{customModel.customNameEn}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关海关编码:</span><span class=\"WordsRight\">{{customModel.hsCode}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">材质:</span><span class=\"WordsRight\">{{customModel.material}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">用途:</span><span class=\"WordsRight\">{{customModel.purpose}}</span></div>\r\n      <div class=\"wordsBox divWidth\"><span class=\"wordLeft\">报关单位:</span><span class=\"WordsRight\">{{customModel.unit}}</span></div>\r\n    </div>\r\n    <div class=\"wordsBox\">\r\n      <div style=\"width:400px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">申报要素</p>\r\n          <Table :border=\"true\" :columns=\"declareColumn\" :data=\"declareData\"></Table>\r\n        </Card>\r\n      </div>\r\n      <div style=\"width:600px;\">\r\n        <Card class=\"infoBox1\">\r\n          <p slot=\"title\">清关资料</p>\r\n          <Table :border=\"true\" :columns=\"clearanceColumn\" :data=\"clearanceData\"></Table>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" @click=\"onCancel\">关闭</Button>&nbsp;\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport {autoTableHeight} from \"@/libs/tools\";\r\nimport Category from \"@/api/custom/customClass\";\r\nexport default {\r\n  name: 'CategoryView',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading:false,\r\n      spin:false,\r\n      id:null,\r\n      customModel:{},\r\n      declareColumn:[{title: '类型',key: 'decKey', minWidth: 120, align: 'center'},\r\n        {title: '内容', key: 'content',minWidth: 120, align: 'center'}],\r\n      clearanceColumn:[{title: '国家',key: 'countryName', minWidth: 120, align: 'center'},\r\n        {title: '清关编码', key: 'hsCode',minWidth: 120, align: 'center'},\r\n        {title: '清关价格', key: 'price',minWidth: 120, align: 'center'},\r\n        {title: '清关币种', key: 'currencyName',minWidth: 120, align: 'center'}],\r\n      declareData:[],\r\n      clearanceData:[],\r\n      data:[]\r\n    }\r\n  },\r\n  props: {\r\n    onCancel: { type: Function },\r\n    modelViewVisible: {\r\n      type: Boolean,\r\n    },\r\n    allData:{type:Array},\r\n    currencyList:{type:Array},\r\n    countryList:{type:Array},\r\n  },\r\n  methods: {\r\n    setDefault(id) {\r\n      this.id = id;\r\n      this.loadData();\r\n    },\r\n    loadData(){\r\n      this.customModel={};\r\n      this.spin = true;\r\n      Category.getBy({\"id\": this.id,\"calc\":true}).then(res=>{\r\n        if (res['code'] === 0) {\r\n          this.customModel = res.data;\r\n          this.allData.forEach(item => {\r\n            if (item['id'] === this.customModel.parentId) {\r\n              this.customModel.parentClassName = item['className'];\r\n            }\r\n          })\r\n          if(!this.customModel.categoryName){\r\n            this.customModel.categoryName=null;\r\n          }\r\n          this.declareData = res.data['declarationElementList'];\r\n          this.clearanceData = res.data['clearanceElementList'];\r\n          if(this.clearanceData && this.countryList.length>0){\r\n            let countryObj = {};\r\n            this.countryList.forEach(item=>countryObj[item['two_code']] = item['name_cn'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['countryName']= countryObj[item['country']]\r\n            })\r\n            let currencyObj = {};\r\n            this.currencyList.forEach(item=>currencyObj[item['id']] = item['name'])\r\n            this.clearanceData.forEach(item=>{\r\n              item['currencyName']= currencyObj[item['currency']]\r\n            })\r\n          }\r\n        }\r\n      }).finally(()=>{this.spin = false;})\r\n    },\r\n    ChangeImg(){\r\n      this.spin = true;\r\n      this.loadData();\r\n    },\r\n  },\r\n  mounted: function () {\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.wordsBox{\r\n  display: flex;\r\n  margin-bottom: 5px;\r\n}\r\n.divWidth{\r\n  width:330px;\r\n}\r\n.wordLeft{\r\n  display: inline-block;\r\n  width: 100px;\r\n  font-weight: bold;\r\n  white-space:nowrap;\r\n}\r\n.WordsRight{\r\n  position: relative;\r\n  width: 220px;\r\n  word-wrap: break-word;\r\n  word-break: normal;\r\n}\r\n</style>\r\n\r\n"]}]}