{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearanceInfo\\ClearanceModel1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearanceInfo\\ClearanceModel1.vue", "mtime": 1752737748522}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "data", "loading", "clearanceRank", "detailData", "keyData", "Date", "getTime", "id", "formInfo", "thdOrder", "boxQty", "whCode", "isUrl", "isPhoto", "thdRef", "address", "currency", "taxNoAddress", "taxCompany", "isCompany", "isWeight", "isSpuName", "qtyPercent", "isSalePrice", "salePriceDisCount", "columns1", "title", "key", "width", "align", "children", "slot", "tooltip", "props", "clearanceVisible", "type", "Boolean", "currencyList", "Array", "onCancel", "Function", "methods", "<PERSON><PERSON><PERSON><PERSON>", "_this", "changeRadio1", "changeRadio2", "for<PERSON>ach", "item", "qtyTemp", "Math", "round", "qty", "changePriceType", "getParams", "param", "_objectSpread", "keepSaveSuccess", "changeQtyPercent", "that", "console", "log", "v", "changePriceDisCount", "_this2", "price", "formatFloat", "changeRadio", "flag", "obj", "currentIndex", "findIndex", "push", "splice", "handleSummary", "_ref", "_this3", "columns", "sums", "column", "index", "value", "f", "digit", "m", "pow"], "sources": ["src/view/module/custom/clearance/clearanceInfo/ClearanceModel1.vue"], "sourcesContent": ["<!--\r\n@create date 2021-08-06\r\n@desc 清关公共模块\r\n-->\r\n<template>\r\n  <div>\r\n    <Form ref=\"formInfo\" :model=\"formInfo\" inline>\r\n      <FormItem prop=\"fbaNo\" label=\"FBA单号\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['thdOrder']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n        <Button style=\"margin: 0 10px\" v-copytext=\"formInfo['thdOrder']\">复制</Button>\r\n      </FormItem>\r\n      <FormItem prop=\"boxQty\" label=\"箱数\" :label-width=\"40\">\r\n        <Input v-model=\"formInfo['boxQty']\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"whCode\" label=\"地址速记码\" :label-width=\"75\">\r\n        <Input v-model=\"formInfo['whCode']\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"isUrl\" label=\"是否需要链接\" :label-width=\"85\">\r\n        <RadioGroup v-model=\"formInfo['isUrl']\" @on-change=\"changeRadio1\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"isPhoto\" label=\"是否需要图片\" :label-width=\"85\">\r\n        <RadioGroup v-model=\"formInfo['isPhoto']\" @on-change=\"changeRadio2\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"thdRef\" label=\"货件追踪编号\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['thdRef']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"address\" label=\"收货地址\" :label-width=\"70\">\r\n        <Input v-model=\"formInfo['address']\"  style=\"width:430px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"currency\" label=\"币种\" :label-width=\"60\">\r\n        <Input v-model=\"currencyList.filter(item=>item['id'] === formInfo['currency']).map(item=>item['code'])[0]\"  style=\"width: 100px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"taxNoAddress\" label=\"税号地址\" :label-width=\"90\">\r\n        <Input v-model=\"formInfo['taxNoAddress']\"  style=\"width:400px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"taxCompany\" label=\"税号公司\" :label-width=\"70\" v-show=\"formInfo.isCompany === 1\">\r\n        <Input v-model=\"formInfo['taxCompany']\"  style=\"width:430px\" :readonly=\"true\" :disabled=\"true\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"isCompany\" label=\"是否需要税号公司\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo['isCompany']\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" :disabled=\"true\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem v-show=\"!this.id\" prop=\"isSpuName\" label=\"是否需要型号\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo['isSpuName']\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem  label=\"是否导出单品重量\" :label-width=\"110\">\r\n        <RadioGroup v-model=\"formInfo.isWeight\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"numProportion\" label=\"数量比例%\" :label-width=\"80\">\r\n        <InputNumber v-model=\"formInfo['qtyPercent']\"\r\n                     :readonly=\"!!this.id\"  :disabled=\"!!this.id\"\r\n                     @on-change=\"changeQtyPercent\"\r\n                     @on-blur=\"changeQtyPercent\"\r\n                     :formatter=\"value => `${value}%`\"\r\n                     :parser=\"value => value.replace('%', '')\"  style=\"width:100px\">\r\n        </InputNumber>\r\n      </FormItem>\r\n      <FormItem  label=\"是否使用产品价\" :label-width=\"110\" v-if=\"!this.id\">\r\n        <RadioGroup v-model=\"formInfo['isSalePrice']\" @on-change=\"changePriceType\">\r\n          <Radio v-for=\"(item) in [{value:0,name:'否'},{value:1,name:'是'}]\"\r\n                 :label=\"item.value\" v-bind:key=\"item.value\">{{item.name}}</Radio>\r\n        </RadioGroup>\r\n      </FormItem>\r\n      <FormItem prop=\"productDiscount\" label=\"产品价折扣%\" :label-width=\"100\" v-if=\"!this.id\">\r\n        <InputNumber v-model=\"formInfo['salePriceDisCount']\"\r\n                     :min=\"0\"\r\n                     :max=\"100\"\r\n                     @on-change=\"changePriceDisCount\"\r\n                     @on-blur=\"changePriceDisCount\"\r\n                     :formatter=\"value => `${value}%`\"\r\n                     :parser=\"value => value.replace('%', '')\"  style=\"width:100px\">\r\n        </InputNumber>\r\n      </FormItem>\r\n    </Form>\r\n    <Table :columns=\"columns1\" :show-summary=\"true\" :summary-method=\"handleSummary\" :data=\"detailData\" max-height=\"500\" :border=\"true\" :key=\"keyData\">\r\n      <template v-slot:urlTemp=\"{row,index}\">\r\n        <Poptip :word-wrap=\"true\" width=\"200\" trigger=\"hover\" :content=\"row['goodsUrl']\">\r\n          <div  v-copytext=\"row['goodsUrl']\" style=\"width:100px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;\">{{row['goodsUrl']}}</div>\r\n        </Poptip>\r\n      </template>\r\n      <template v-slot:imgTemp=\"{row,index}\">\r\n        <img v-if=\"!!row['photoUrl']\" v-change-imj=\"row['photoUrl']\" style=\"max-width: 100px;max-height: 60px;\" alt=\"商品图片\" src=\"\">\r\n      </template>\r\n      <template v-slot:spuName=\"{row,index}\">\r\n        <span>{{formInfo['isSpuName'] === 0 ? '' : row['spuName']}}</span>\r\n      </template>\r\n      <template v-slot:currencyName=\"{row,index}\">\r\n        <span v-for=\"item in currencyList\" v-if=\"item['id'] === row.currency\">{{ item['code'] }}</span>\r\n      </template>\r\n    </Table>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Currency from \"@/api/basf/currency\";\r\n\r\nexport default {\r\n  data(){\r\n    return{\r\n      loading:false,\r\n      clearanceRank:null,\r\n      detailData:[],\r\n      keyData:new Date().getTime(),\r\n      id:null,//导出id；\r\n      formInfo:{\r\n        thdOrder:'',\r\n        boxQty:null,\r\n        whCode:null,\r\n        isUrl:0,\r\n        isPhoto:0,\r\n        thdRef:null,\r\n        address:null,\r\n        currency:null,\r\n        taxNoAddress:null,\r\n        taxCompany:null,\r\n        isCompany:0,\r\n        isWeight:0,\r\n        isSpuName:0,\r\n        qtyPercent:100,\r\n        isSalePrice:0,\r\n        salePriceDisCount:null,\r\n      },\r\n      columns1:[\r\n        {title: '箱号',  key: 'boxNo',  width: 220,  align: 'center',},\r\n        {title: '尺寸(CM)',  key: 'size',  width:100,  align: 'center',},\r\n        {title: '总毛重(Kg)',  key: 'grossWeight',  width:100,  align: 'center',},\r\n        {title: '特殊属性',  key: 'attribute',  width:100,  align: 'center',},\r\n        {title: '订单箱子详情',  key: 'boxDetail',  align: 'center',children:\r\n            [{  title: '海关编码',  key: 'hsCode',  align: 'center',  width:110,},\r\n            {  title: '中文品名',  key: 'customName',  align: 'center',  width:110,},\r\n            {  title: '英文品名',  key: 'customNameEn',  align: 'center',  width:180,},\r\n            {  title: '型号',  key: 'spuName',  align: 'center',  width:150, slot:'spuName',},\r\n            {  title: '品牌',  key: 'brand',  align: 'center',  width:100,},\r\n            {  title: '材质',  key: 'material',  align: 'center',  width:130, tooltip:true},\r\n            {  title: '用途',  key: 'purpose',  align: 'center',  width:130, tooltip:true},\r\n            {  title: '数量',  key: 'qtyTemp',  align: 'center',  width:90,},\r\n            {  title: '单个重量(Kg)',  key: 'unitWeight',  align: 'center',  width:90,},\r\n            {  title: '币种',  key: 'currency',  align: 'center',  width:90,slot:'currencyName'},\r\n            {  title: '单价',  key: 'unitPrice',  align: 'center',  width:90,}]}],\r\n    }\r\n  },\r\n  props: {\r\n    clearanceVisible: {\r\n      type: Boolean,\r\n    },\r\n    currencyList:{type:Array},\r\n    onCancel: { type: Function },\r\n  },\r\n  methods:{\r\n    setDefault(data){\r\n      this.formInfo = data;\r\n      this.formInfo['isWeight']=0;\r\n      this.id = data['id'];\r\n      this.changeRadio1(data['isUrl']);\r\n      this.changeRadio2(data['isPhoto']);\r\n      this.detailData = data['detailList'];\r\n      this.detailData.forEach(item=>{\r\n        item.qtyTemp = Math.round(item.qty*(this.formInfo['qtyPercent'] / 100))\r\n      })\r\n      this.changePriceType(this.formInfo['isSalePrice']);\r\n    },\r\n    getParams(){\r\n      let param = {...this.formInfo};\r\n      param['detailList'] = this.detailData;\r\n      return param;\r\n    },\r\n    keepSaveSuccess(id){\r\n      this.id = id;\r\n    },\r\n    changeQtyPercent(){\r\n      let that = this;\r\n      this.detailData.forEach(item=>{\r\n        console.log(item.qty*(that.formInfo['qtyPercent'] / 100));\r\n        item.qtyTemp = Math.round(item.qty*(that.formInfo['qtyPercent'] / 100))\r\n      })\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changePriceType(v){\r\n      this.formInfo.salePriceDisCount = v === 0 ? null : 100;\r\n      if(v === 0){\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] =item['price'];\r\n        })\r\n      }else{\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] =item['salePrice'];\r\n        })\r\n      }\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changePriceDisCount(){\r\n      if(this.formInfo.isSalePrice === 0){\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] = item.price\r\n        })\r\n      }else{\r\n        this.detailData.forEach(item=>{\r\n          item['unitPrice'] = this.formatFloat(item['salePrice']*(this.formInfo.salePriceDisCount / 100),2)\r\n        })\r\n      }\r\n      this.keyData = new Date().getTime();\r\n    },\r\n    changeRadio(flag,obj){\r\n      let currentIndex = (this.columns1[4].children|| []).findIndex((item) => item.key === obj.key);\r\n      if(flag === 1){\r\n        if(currentIndex === -1){\r\n          this.columns1[4].children.push(obj);\r\n        }\r\n      }else{\r\n        if(currentIndex !== -1){\r\n          this.columns1[4].children.splice(currentIndex,1);\r\n        }\r\n      }\r\n    },\r\n    changeRadio1(v){\r\n      this.changeRadio(v,{title: '商品链接', key: 'goodsUrl', width:150, slot:'urlTemp', align: 'center',});\r\n    },\r\n    changeRadio2(v){\r\n      this.changeRadio(v,{title: '商品图片', key: 'photoUrl', slot:'imgTemp', align: 'center', width:80,});\r\n    },\r\n    handleSummary ({ columns}) {\r\n      const sums = {};\r\n      columns.forEach((column, index) => {\r\n        const key = column.key;\r\n        if (index === 0) {\r\n          sums[key] = {key, value: '合计'};\r\n          return;\r\n        }\r\n        if(key === 'grossWeight'){\r\n          sums[key] = {key, value:this.formInfo['grossWeight']};\r\n          return;\r\n        }\r\n        if(key === 'qty'){\r\n          sums[key] = {key, value:this.formInfo['qty']};\r\n        }else{\r\n          sums[key] = {key, value:'--'};\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    // 浮点计算\r\n    formatFloat(f,digit){\r\n      let m = Math.pow(10, digit);\r\n      return Math.round(f * m) / m;\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n"], "mappings": ";;;;;;AA2GA,OAAAA,QAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,MAAAC,IAAA,GAAAC,OAAA;MACAC,EAAA;MAAA;MACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,KAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,UAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;QACAC,WAAA;QACAC,iBAAA;MACA;MACAC,QAAA,GACA;QAAAC,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,GAAA;QAAAE,KAAA;QAAAC,QAAA,EACA;UAAAJ,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;UAAAG,IAAA;QAAA,GACA;UAAAL,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;UAAAI,OAAA;QAAA,GACA;UAAAN,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;UAAAI,OAAA;QAAA,GACA;UAAAN,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;UAAAG,IAAA;QAAA,GACA;UAAAL,KAAA;UAAAC,GAAA;UAAAE,KAAA;UAAAD,KAAA;QAAA;MAAA;IACA;EACA;EACAK,KAAA;IACAC,gBAAA;MACAC,IAAA,EAAAC;IACA;IACAC,YAAA;MAAAF,IAAA,EAAAG;IAAA;IACAC,QAAA;MAAAJ,IAAA,EAAAK;IAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA1C,IAAA;MAAA,IAAA2C,KAAA;MACA,KAAAnC,QAAA,GAAAR,IAAA;MACA,KAAAQ,QAAA;MACA,KAAAD,EAAA,GAAAP,IAAA;MACA,KAAA4C,YAAA,CAAA5C,IAAA;MACA,KAAA6C,YAAA,CAAA7C,IAAA;MACA,KAAAG,UAAA,GAAAH,IAAA;MACA,KAAAG,UAAA,CAAA2C,OAAA,WAAAC,IAAA;QACAA,IAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,IAAA,CAAAI,GAAA,IAAAR,KAAA,CAAAnC,QAAA;MACA;MACA,KAAA4C,eAAA,MAAA5C,QAAA;IACA;IACA6C,SAAA,WAAAA,UAAA;MACA,IAAAC,KAAA,GAAAC,aAAA,UAAA/C,QAAA;MACA8C,KAAA,sBAAAnD,UAAA;MACA,OAAAmD,KAAA;IACA;IACAE,eAAA,WAAAA,gBAAAjD,EAAA;MACA,KAAAA,EAAA,GAAAA,EAAA;IACA;IACAkD,gBAAA,WAAAA,iBAAA;MACA,IAAAC,IAAA;MACA,KAAAvD,UAAA,CAAA2C,OAAA,WAAAC,IAAA;QACAY,OAAA,CAAAC,GAAA,CAAAb,IAAA,CAAAI,GAAA,IAAAO,IAAA,CAAAlD,QAAA;QACAuC,IAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,IAAA,CAAAI,GAAA,IAAAO,IAAA,CAAAlD,QAAA;MACA;MACA,KAAAJ,OAAA,OAAAC,IAAA,GAAAC,OAAA;IACA;IACA8C,eAAA,WAAAA,gBAAAS,CAAA;MACA,KAAArD,QAAA,CAAAgB,iBAAA,GAAAqC,CAAA;MACA,IAAAA,CAAA;QACA,KAAA1D,UAAA,CAAA2C,OAAA,WAAAC,IAAA;UACAA,IAAA,gBAAAA,IAAA;QACA;MACA;QACA,KAAA5C,UAAA,CAAA2C,OAAA,WAAAC,IAAA;UACAA,IAAA,gBAAAA,IAAA;QACA;MACA;MACA,KAAA3C,OAAA,OAAAC,IAAA,GAAAC,OAAA;IACA;IACAwD,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,SAAAvD,QAAA,CAAAe,WAAA;QACA,KAAApB,UAAA,CAAA2C,OAAA,WAAAC,IAAA;UACAA,IAAA,gBAAAA,IAAA,CAAAiB,KAAA;QACA;MACA;QACA,KAAA7D,UAAA,CAAA2C,OAAA,WAAAC,IAAA;UACAA,IAAA,gBAAAgB,MAAA,CAAAE,WAAA,CAAAlB,IAAA,iBAAAgB,MAAA,CAAAvD,QAAA,CAAAgB,iBAAA;QACA;MACA;MACA,KAAApB,OAAA,OAAAC,IAAA,GAAAC,OAAA;IACA;IACA4D,WAAA,WAAAA,YAAAC,IAAA,EAAAC,GAAA;MACA,IAAAC,YAAA,SAAA5C,QAAA,IAAAK,QAAA,QAAAwC,SAAA,WAAAvB,IAAA;QAAA,OAAAA,IAAA,CAAApB,GAAA,KAAAyC,GAAA,CAAAzC,GAAA;MAAA;MACA,IAAAwC,IAAA;QACA,IAAAE,YAAA;UACA,KAAA5C,QAAA,IAAAK,QAAA,CAAAyC,IAAA,CAAAH,GAAA;QACA;MACA;QACA,IAAAC,YAAA;UACA,KAAA5C,QAAA,IAAAK,QAAA,CAAA0C,MAAA,CAAAH,YAAA;QACA;MACA;IACA;IACAzB,YAAA,WAAAA,aAAAiB,CAAA;MACA,KAAAK,WAAA,CAAAL,CAAA;QAAAnC,KAAA;QAAAC,GAAA;QAAAC,KAAA;QAAAG,IAAA;QAAAF,KAAA;MAAA;IACA;IACAgB,YAAA,WAAAA,aAAAgB,CAAA;MACA,KAAAK,WAAA,CAAAL,CAAA;QAAAnC,KAAA;QAAAC,GAAA;QAAAI,IAAA;QAAAF,KAAA;QAAAD,KAAA;MAAA;IACA;IACA6C,aAAA,WAAAA,cAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,OAAA,GAAAF,IAAA,CAAAE,OAAA;MACA,IAAAC,IAAA;MACAD,OAAA,CAAA9B,OAAA,WAAAgC,MAAA,EAAAC,KAAA;QACA,IAAApD,GAAA,GAAAmD,MAAA,CAAAnD,GAAA;QACA,IAAAoD,KAAA;UACAF,IAAA,CAAAlD,GAAA;YAAAA,GAAA,EAAAA,GAAA;YAAAqD,KAAA;UAAA;UACA;QACA;QACA,IAAArD,GAAA;UACAkD,IAAA,CAAAlD,GAAA;YAAAA,GAAA,EAAAA,GAAA;YAAAqD,KAAA,EAAAL,MAAA,CAAAnE,QAAA;UAAA;UACA;QACA;QACA,IAAAmB,GAAA;UACAkD,IAAA,CAAAlD,GAAA;YAAAA,GAAA,EAAAA,GAAA;YAAAqD,KAAA,EAAAL,MAAA,CAAAnE,QAAA;UAAA;QACA;UACAqE,IAAA,CAAAlD,GAAA;YAAAA,GAAA,EAAAA,GAAA;YAAAqD,KAAA;UAAA;QACA;MACA;MACA,OAAAH,IAAA;IACA;IACA;IACAZ,WAAA,WAAAA,YAAAgB,CAAA,EAAAC,KAAA;MACA,IAAAC,CAAA,GAAAlC,IAAA,CAAAmC,GAAA,KAAAF,KAAA;MACA,OAAAjC,IAAA,CAAAC,KAAA,CAAA+B,CAAA,GAAAE,CAAA,IAAAA,CAAA;IACA;EACA;AACA"}]}