{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\login\\login.vue", "mtime": 1752737748508}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapActions", "loginLogo", "setToken", "getRequest", "check1", "check2", "check3", "check4", "check5", "check6", "name", "props", "usernameRules", "type", "Array", "default", "_default", "required", "message", "trigger", "passwordRules", "data", "loading", "loginImg", "buttonSize", "passwordType", "eyeStatus", "eyeImg", "require", "checkStatus", "<PERSON><PERSON><PERSON><PERSON>", "errTimes", "checkResult", "form", "username", "password", "auto", "config", "computed", "rules", "watch", "val", "methods", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON>", "handleCheckSuccess", "_this", "$refs", "loginForm", "validate", "valid", "handleLogin", "then", "res", "$router", "push", "$config", "homeName", "localStorage", "setItem", "catch", "err", "slideBlock", "reset", "finally", "handleCheckFail", "changeEyeStatus", "handleSubmit", "_this2", "handleAuthLogin", "_this3", "token", "$route", "query", "UserId", "mounted", "getItem", "window", "onload", "redirect_uri", "process", "env", "NODE_ENV", "encodeURIComponent", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "appid", "agentid", "state", "href", "location", "origin"], "sources": ["src/view/login/login.vue"], "sourcesContent": ["<style lang=\"less\">\r\n@import \"./login.less\";\r\n</style>\r\n\r\n<template>\r\n  <div class=\"login\">\r\n    <div class=\"login-con\">\r\n      <div class=\"login-layout-logo\">\r\n        <img class=\"login-logo\" :src=\"loginLogo\" key=\"login-logo\"  alt=\"珠海穗元服饰有限公司\"/>\r\n      </div>\r\n      <div class=\"login-area\">\r\n        <div class=\"backgroundText\">\r\n          <span>穗元服饰，不是一个人的小情怀</span>\r\n          <br />\r\n          <span>而是一群人的光荣与梦想</span>\r\n          <br />\r\n          <span>一个行业的机遇和使命</span>\r\n        </div>\r\n        <div class=\"login-area-center\">\r\n          <div id=\"wx_icon\"></div>\r\n          <div class=\"right-login\">\r\n            <div class=\"login-title\">穗元服饰账号登录</div>\r\n            <div v-show=\"!checkStatus\" class=\"form-con\">\r\n              <Form ref=\"loginForm\" :model=\"form\" :rules=\"rules\" @keydown.enter.native=\"handleSubmit\" style=\"position: relative\">\r\n                <FormItem prop=\"username\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">账号</div>\r\n                    <input type=\"text\" v-model=\"form.username\" />\r\n                  </div>\r\n                </FormItem>\r\n                <FormItem prop=\"password\">\r\n                  <div class=\"input-box\">\r\n                    <div class=\"title\">密码</div>\r\n                    <input :type=\"passwordType\" class=\"password\" v-model=\"form.password\"/>\r\n                    <div class=\"eye\">\r\n                      <img :src=\"eyeImg\" @click=\"changeEyeStatus\"  alt=\"查看密码\"/>\r\n                    </div>\r\n                  </div>\r\n                  <Checkbox class=\"autoLogin\" v-model=\"form.auto\">自动登录</Checkbox>\r\n                </FormItem>\r\n                <FormItem>\r\n                  <Button @click=\"handleSubmit\" :size=\"buttonSize\" :loading=\"loading\" :long=\"true\" style=\"background:#1C6BBA;color:white\">登录</Button>\r\n                </FormItem>\r\n              </Form>\r\n            </div>\r\n            <div class=\"popTip\" v-show=\"checkStatus\">\r\n              <div style=\"margin-bottom: 5px\">\r\n                <a @click=\"closeCheck\">\r\n                  <Icon size=\"12\" type=\"md-arrow-back\" />返回\r\n                </a>\r\n              </div>\r\n              <div>\r\n                <slide-verify ref=\"slideBlock\" v-show=\"needCheck\" class=\"slide-box\" style=\"width: 280px\" :r=\"8\" :l=\"32\" :w=\"280\" :h=\"120\" :imgs=\"loginImg\"\r\n                  @success=\"handleCheckSuccess\" @fail=\"handleCheckFail\" slider-text=\"向右滑动完成验证\"></slide-verify>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"footer-area\">\r\n        <div class=\"login-footer-copyright\">\r\n          Copyright ©️ SuiYun.cn. All rights reserved\r\n        </div>\r\n        <div class=\"login-footer-copyright\">\r\n          珠海穗元服饰有限公司-中国服饰行业领跑者 <br/>联系方式：400-660-2205\r\n          email：<EMAIL>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions } from \"vuex\";\r\nimport loginLogo from \"@/assets/images/login-logo.png\";\r\nimport { setToken } from \"@/libs/util\";\r\nimport { getRequest } from \"@/libs/axios.js\";\r\nimport check1 from \"@/assets/images/loginImg/check1.jpg\";\r\nimport check2 from \"@/assets/images/loginImg/check2.jpg\";\r\nimport check3 from \"@/assets/images/loginImg/check3.jpg\";\r\nimport check4 from \"@/assets/images/loginImg/check4.jpg\";\r\nimport check5 from \"@/assets/images/loginImg/check5.jpg\";\r\nimport check6 from \"@/assets/images/loginImg/check6.jpg\";\r\n\r\nexport default {\r\n  name: \"LoginForm\",\r\n  props: {\r\n    usernameRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"账号不能为空\", trigger: \"blur\" }];\r\n      }\r\n    },\r\n    passwordRules: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{ required: true, message: \"密码不能为空\", trigger: \"blur\" }];\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      loginImg: [check1, check2, check3, check4, check5, check6],\r\n      loginLogo,\r\n      buttonSize: \"large\",\r\n      passwordType: \"password\",\r\n      eyeStatus: true,\r\n      eyeImg: require(\"@/assets/icons/eye.png\"),\r\n      //新滑动验证\r\n      checkStatus: false,\r\n      needCheck: false,\r\n      errTimes: 0, //输入密码错误的次数\r\n      checkResult: false, //滑动验证的结果\r\n      //新滑动验证结束\r\n      form: {\r\n        username: \"\",\r\n        password: \"\",\r\n        auto: false\r\n      },\r\n      config: {}\r\n    };\r\n  },\r\n  computed: {\r\n    rules() {\r\n      return {\r\n        username: this.usernameRules,\r\n        password: this.passwordRules\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    eyeStatus(val) {\r\n      if (val) {\r\n        this.eyeImg = require(\"@/assets/icons/eye.png\");\r\n        this.passwordType = \"password\";\r\n      } else {\r\n        this.eyeImg = require(\"@/assets/icons/eye-active.png\");\r\n        this.passwordType = \"Text\";\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    ...mapActions([\"handleLogin\", \"getUserInfo\"]),\r\n    //返回输入登录信息\r\n    closeCheck() {\r\n      this.checkStatus = false;\r\n    },\r\n    //验证通过\r\n    handleCheckSuccess() {\r\n      this.checkResult = true;\r\n      this.$refs.loginForm.validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          let username = this.form.username;\r\n          let password = this.form.password;\r\n          let auto = this.form.auto;\r\n          this.handleLogin({ username, password, auto })\r\n            .then(res => {\r\n              if (res && res['code'] === 0) {\r\n                this.$router.push({ name: this.$config.homeName });\r\n                localStorage.setItem(\"needCheck\", \"false\");\r\n                this.needCheck = false;\r\n                this.checkStatus = false;\r\n                this.errTimes = 0;\r\n              }}).catch(err => {\r\n                if (err) {\r\n                this.errTimes++;\r\n                this.checkStatus = false;\r\n                this.$refs.slideBlock.reset();\r\n                this.checkResult = false;\r\n                if (this.errTimes > 2) {\r\n                  localStorage.setItem(\"needCheck\", \"true\");\r\n                  this.needCheck = true;\r\n                }\r\n              }}).finally(() => {this.loading = false;});\r\n        }\r\n      });\r\n    },\r\n    //验证失败\r\n    handleCheckFail() {\r\n      this.checkResult = false;\r\n    },\r\n    //密码是否可见\r\n    changeEyeStatus() {\r\n      this.eyeStatus = !this.eyeStatus;\r\n    },\r\n    //点击登录\r\n    handleSubmit() {\r\n      if (this.needCheck === true) {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.checkStatus = true;\r\n          }\r\n        });\r\n      } else {\r\n        this.$refs.loginForm.validate(valid => {\r\n          if (valid) {\r\n            this.loading = true;\r\n            let username = this.form.username;\r\n            let password = this.form.password;\r\n            let auto = this.form.auto;\r\n            this.handleLogin({ username, password, auto })\r\n              .then(res => {\r\n\r\n                if (res && res['code'] === 0) {\r\n                  this.$router.push({ name: this.$config.homeName });\r\n                  localStorage.setItem(\"needCheck\", \"false\");\r\n                  this.needCheck = false;\r\n                  this.errTimes = 0;\r\n                }}).catch(err => {\r\n                  if (err) {\r\n                    this.errTimes++;\r\n                    this.checkStatus = false;\r\n                    this.$refs.slideBlock.reset();\r\n                    this.checkResult = false;\r\n                    if (this.errTimes > 2) {\r\n                      localStorage.setItem(\"needCheck\", \"true\");\r\n                      this.needCheck = true;\r\n                    }\r\n                }})\r\n              .finally(() => {this.loading = false;});\r\n          }});\r\n      }\r\n    },\r\n    handleAuthLogin() {\r\n      let token = this.$route.query.token;\r\n      let UserId = this.$route.query.UserId;\r\n      if (typeof token === \"undefined\") {\r\n      } else {\r\n        getRequest(\"/base/login/checkThirdIp\", { UserId: UserId })\r\n          .then(res => {\r\n            if (res[\"code\"] === \"0\") {\r\n              setToken(token);\r\n              this.$router.push({ name: this.$config.homeName });\r\n            }\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n      }\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleAuthLogin();\r\n    this.needCheck = localStorage.getItem(\"needCheck\")\r\n      ? localStorage.getItem(\"needCheck\") === \"true\" : false;\r\n  }\r\n};\r\nwindow.onload = function() {\r\n  const redirect_uri =\r\n    process.env.NODE_ENV === \"production\"\r\n      ? encodeURIComponent(\"https://amz.i-suiyuan.com/api-v1.0/base/oauth/qiyewechat/callback\")\r\n      : undefined;\r\n  window.WwLogin({\r\n    id: \"wx_icon\",\r\n    appid: \"ww41fa91e90d30fdd8\",\r\n    agentid: \"1000002\",\r\n    redirect_uri: redirect_uri,\r\n    state: \"2241241241\",\r\n    href: location.origin + \"/QR_code.css\"\r\n  });\r\n};\r\n</script>\r\n<style></style>\r\n"], "mappings": ";;;AAyEA,SAAAA,UAAA;AACA,OAAAC,SAAA;AACA,SAAAC,QAAA;AACA,SAAAC,UAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,aAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;IACAC,aAAA;MACAP,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA,GAAAnB,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,MAAA;MACAR,SAAA,EAAAA,SAAA;MACAuB,UAAA;MACAC,YAAA;MACAC,SAAA;MACAC,MAAA,EAAAC,OAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,QAAA;MAAA;MACAC,WAAA;MAAA;MACA;MACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA;QACAL,QAAA,OAAAtB,aAAA;QACAuB,QAAA,OAAAf;MACA;IACA;EACA;EACAoB,KAAA;IACAd,SAAA,WAAAA,UAAAe,GAAA;MACA,IAAAA,GAAA;QACA,KAAAd,MAAA,GAAAC,OAAA;QACA,KAAAH,YAAA;MACA;QACA,KAAAE,MAAA,GAAAC,OAAA;QACA,KAAAH,YAAA;MACA;IACA;EACA;EACAiB,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACA3C,UAAA;IACA;IACA4C,UAAA,WAAAA,WAAA;MACA,KAAAf,WAAA;IACA;IACA;IACAgB,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MACA,KAAAd,WAAA;MACA,KAAAe,KAAA,CAAAC,SAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,KAAA,CAAAxB,OAAA;UACA,IAAAY,QAAA,GAAAY,KAAA,CAAAb,IAAA,CAAAC,QAAA;UACA,IAAAC,QAAA,GAAAW,KAAA,CAAAb,IAAA,CAAAE,QAAA;UACA,IAAAC,IAAA,GAAAU,KAAA,CAAAb,IAAA,CAAAG,IAAA;UACAU,KAAA,CAAAK,WAAA;YAAAjB,QAAA,EAAAA,QAAA;YAAAC,QAAA,EAAAA,QAAA;YAAAC,IAAA,EAAAA;UAAA,GACAgB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,IAAAA,GAAA;cACAP,KAAA,CAAAQ,OAAA,CAAAC,IAAA;gBAAA7C,IAAA,EAAAoC,KAAA,CAAAU,OAAA,CAAAC;cAAA;cACAC,YAAA,CAAAC,OAAA;cACAb,KAAA,CAAAhB,SAAA;cACAgB,KAAA,CAAAjB,WAAA;cACAiB,KAAA,CAAAf,QAAA;YACA;UAAA,GAAA6B,KAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAf,KAAA,CAAAf,QAAA;cACAe,KAAA,CAAAjB,WAAA;cACAiB,KAAA,CAAAC,KAAA,CAAAe,UAAA,CAAAC,KAAA;cACAjB,KAAA,CAAAd,WAAA;cACA,IAAAc,KAAA,CAAAf,QAAA;gBACA2B,YAAA,CAAAC,OAAA;gBACAb,KAAA,CAAAhB,SAAA;cACA;YACA;UAAA,GAAAkC,OAAA;YAAAlB,KAAA,CAAAxB,OAAA;UAAA;QACA;MACA;IACA;IACA;IACA2C,eAAA,WAAAA,gBAAA;MACA,KAAAjC,WAAA;IACA;IACA;IACAkC,eAAA,WAAAA,gBAAA;MACA,KAAAxC,SAAA,SAAAA,SAAA;IACA;IACA;IACAyC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAtC,SAAA;QACA,KAAAiB,KAAA,CAAAC,SAAA,CAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAkB,MAAA,CAAAvC,WAAA;UACA;QACA;MACA;QACA,KAAAkB,KAAA,CAAAC,SAAA,CAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAkB,MAAA,CAAA9C,OAAA;YACA,IAAAY,QAAA,GAAAkC,MAAA,CAAAnC,IAAA,CAAAC,QAAA;YACA,IAAAC,QAAA,GAAAiC,MAAA,CAAAnC,IAAA,CAAAE,QAAA;YACA,IAAAC,IAAA,GAAAgC,MAAA,CAAAnC,IAAA,CAAAG,IAAA;YACAgC,MAAA,CAAAjB,WAAA;cAAAjB,QAAA,EAAAA,QAAA;cAAAC,QAAA,EAAAA,QAAA;cAAAC,IAAA,EAAAA;YAAA,GACAgB,IAAA,WAAAC,GAAA;cAEA,IAAAA,GAAA,IAAAA,GAAA;gBACAe,MAAA,CAAAd,OAAA,CAAAC,IAAA;kBAAA7C,IAAA,EAAA0D,MAAA,CAAAZ,OAAA,CAAAC;gBAAA;gBACAC,YAAA,CAAAC,OAAA;gBACAS,MAAA,CAAAtC,SAAA;gBACAsC,MAAA,CAAArC,QAAA;cACA;YAAA,GAAA6B,KAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAO,MAAA,CAAArC,QAAA;gBACAqC,MAAA,CAAAvC,WAAA;gBACAuC,MAAA,CAAArB,KAAA,CAAAe,UAAA,CAAAC,KAAA;gBACAK,MAAA,CAAApC,WAAA;gBACA,IAAAoC,MAAA,CAAArC,QAAA;kBACA2B,YAAA,CAAAC,OAAA;kBACAS,MAAA,CAAAtC,SAAA;gBACA;cACA;YAAA,GACAkC,OAAA;cAAAI,MAAA,CAAA9C,OAAA;YAAA;UACA;QAAA;MACA;IACA;IACA+C,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,KAAA;MACA,IAAAG,MAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAC,MAAA;MACA,WAAAH,KAAA,mBACA;QACApE,UAAA;UAAAuE,MAAA,EAAAA;QAAA,GACAtB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAnD,QAAA,CAAAqE,KAAA;YACAD,MAAA,CAAAhB,OAAA,CAAAC,IAAA;cAAA7C,IAAA,EAAA4D,MAAA,CAAAd,OAAA,CAAAC;YAAA;UACA;QACA,GACAO,OAAA;UACAM,MAAA,CAAAhD,OAAA;QACA;MACA;IACA;EAAA,EACA;EACAqD,OAAA,WAAAA,QAAA;IACA,KAAAN,eAAA;IACA,KAAAvC,SAAA,GAAA4B,YAAA,CAAAkB,OAAA,gBACAlB,YAAA,CAAAkB,OAAA;EACA;AACA;AACAC,MAAA,CAAAC,MAAA;EACA,IAAAC,YAAA,GACAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBACAC,kBAAA,wEACAC,SAAA;EACAP,MAAA,CAAAQ,OAAA;IACAC,EAAA;IACAC,KAAA;IACAC,OAAA;IACAT,YAAA,EAAAA,YAAA;IACAU,KAAA;IACAC,IAAA,EAAAC,QAAA,CAAAC,MAAA;EACA;AACA"}]}