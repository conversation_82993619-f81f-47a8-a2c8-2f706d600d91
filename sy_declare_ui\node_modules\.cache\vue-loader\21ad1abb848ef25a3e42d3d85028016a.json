{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\api\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\gateway\\api\\index.vue", "mtime": 1752737748510}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/gateway/api", "sourcesContent": ["<template xmlns=\"http://www.w3.org/1999/html\">\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline>\r\n        <FormItem prop=\"path\">\r\n          <Input type=\"text\" v-model=\"pageInfo.path\" placeholder=\"请输入请求路径\" />\r\n        </FormItem>\r\n        <FormItem prop=\"apiName\">\r\n          <Input type=\"text\" v-model=\"pageInfo.apiName\" placeholder=\"请输入接口名称\" />\r\n        </FormItem>\r\n        <FormItem prop=\"apiCode\">\r\n          <Input type=\"text\" v-model=\"pageInfo.apiCode\" placeholder=\"请输入接口编码\" />\r\n        </FormItem>\r\n        <FormItem prop=\"serviceId\">\r\n          <Input type=\"text\" v-model=\"pageInfo.serviceId\" placeholder=\"请输入服务名\" />\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button\r\n            :disabled=\"!hasAuthority('apiAdd')\"\r\n            class=\"search-btn\"\r\n            type=\"primary\"\r\n            @click=\"handleModal()\">\r\n            <span>添加</span>\r\n          </Button>\r\n        </ButtonGroup>\r\n        <Dropdown v-if=\"tableSelection.length>0 && hasAuthority('apiDel')\" @on-click=\"handleBatchClick\" style=\"margin-left: 20px\">\r\n          <Button>\r\n            <span>批量操作</span>\r\n            <Icon type=\"ios-arrow-down\"></Icon>\r\n          </Button>\r\n          <DropdownMenu slot=\"list\">\r\n            <DropdownItem name=\"remove\">删除</DropdownItem>\r\n            <Dropdown placement=\"right-start\">\r\n              <DropdownItem>\r\n                <span>状态</span>\r\n                <Icon type=\"ios-arrow-forward\"></Icon>\r\n              </DropdownItem>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"status0\">启用</DropdownItem>\r\n                <DropdownItem name=\"status1\">禁用</DropdownItem>\r\n                <DropdownItem name=\"status2\">维护中</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n            <Dropdown placement=\"right-start\">\r\n              <DropdownItem>\r\n                <span>公开访问</span>\r\n                <Icon type=\"ios-arrow-forward\"></Icon>\r\n              </DropdownItem>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"open0\">允许公开访问</DropdownItem>\r\n                <DropdownItem name=\"open1\">拒绝公开访问</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n            <Dropdown placement=\"right-start\">\r\n              <DropdownItem>\r\n                <span>身份认证</span>\r\n                <Icon type=\"ios-arrow-forward\"></Icon>\r\n              </DropdownItem>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"auth0\">开启身份认证</DropdownItem>\r\n                <DropdownItem name=\"auth1\">关闭身份认证</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </DropdownMenu>\r\n        </Dropdown>\r\n      </div>\r\n      <Alert :show-icon=\"true\">\r\n        <span>\r\n          自动扫描\r\n          <code>@EnableResourceServer</code>资源服务器接口信息,注:自动添加的接口,都是未公开的.\r\n          <code>只有公开的接口,才可以通过网关访问。否则将提示:\"请求地址,拒绝访问!\"</code>\r\n        </span>\r\n      </Alert>\r\n      <Table\r\n        @on-selection-change=\"handleTableSelectChange\"\r\n        :border=\"true\"\r\n        :max-height=\"autoTableHeight($refs.autoTableRef)\"\r\n        ref=\"autoTableRef\"\r\n        :columns=\"columns\"\r\n        :data=\"data\"\r\n        :loading=\"loading\"\r\n      >\r\n        <template v-slot:apiName=\"{ row }\">\r\n          <span>{{row.apiName}}</span>\r\n        </template>\r\n        <template v-slot:isAuth=\"{ row }\">\r\n          <Tag color=\"green\" v-if=\"row.isOpen===0\">允许公开访问</Tag>\r\n          <Tag color=\"red\" v-else-if=\"row.isOpen!==0\">拒绝公开访问</Tag>\r\n          <Tag color=\"green\" v-if=\"row.isAuth===0\">开启身份认证</Tag>\r\n          <Tag color=\"red\" v-else-if=\"row.isAuth!==0\">关闭身份认证</Tag>\r\n          <Tag v-if=\"row.status===0\" color=\"green\">启用</Tag>\r\n          <Tag v-else-if=\"row.status===2\" color=\"orange\">维护中</Tag>\r\n          <Tag v-else color=\"red\">禁用</Tag>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('apiEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n          <a v-if=\"hasAuthority('apiDel')\" @click=\"handleRemove(row)\">删除</a>\r\n        </template>\r\n      </Table>\r\n      <Page\r\n        :transfer=\"true\"\r\n        size=\"small\"\r\n        :total=\"pageInfo.total\"\r\n        :current=\"pageInfo.page\"\r\n        :page-size=\"pageInfo.limit\"\r\n        :show-elevator=\"true\"\r\n        :show-sizer=\"true\"\r\n        :show-total=\"true\"\r\n        @on-change=\"handlePage\"\r\n        @on-page-size-change=\"handlePageSize\"\r\n      ></Page>\r\n    </Card>\r\n    <Modal\r\n      v-model=\"modalVisible\"\r\n      :title=\"modalTitle\"\r\n      width=\"40\"\r\n      @on-cancel=\"handleReset\"\r\n    >\r\n      <div>\r\n        <Alert :show-icon=\"true\" v-if=\"!!formItem.id\">\r\n          <span>自动扫描接口swagger注解。</span>\r\n          <Poptip placement=\"bottom\" title=\"示例代码\">\r\n            <a>示例代码</a>\r\n            <div slot=\"content\">\r\n              <div v-highlight>\r\n                <pre>\r\n                      // 接口介绍\r\n                      @ApiOperation(value = \"接口名称\", notes = \"接口备注\")\r\n                      @PostMapping(\"/testApi\")\r\n                      // 忽略接口,将不再添加或修改次接口\r\n                      @ApiIgnore\r\n                      public ResultBody testApi() {\r\n                          return ResultBody.success();\r\n                      }\r\n                </pre>\r\n              </div>\r\n            </div>\r\n          </Poptip>\r\n        </Alert>\r\n        <Form ref=\"form1\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\" inline>\r\n          <FormItem label=\"服务名称\" prop=\"serviceId\">\r\n            <Select\r\n              :disabled=\"!!(formItem.id && formItem.isPersist === 1)\"\r\n              v-model=\"formItem.serviceId\"\r\n              filterable\r\n              clearable\r\n            >\r\n              <Option\r\n                v-for=\"item in selectServiceList\"\r\n                :value=\"item.serviceId\" v-bind:key=\"item.serviceId\"\r\n              >{{ item.serviceName }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem label=\"接口分类\" prop=\"apiCategory\">\r\n            <Input v-model=\"formItem.apiCategory\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"接口编码\" prop=\"apiCode\">\r\n            <Input\r\n              :disabled=\"!!(formItem.id && formItem.isPersist === 1)\"\r\n              v-model=\"formItem.apiCode\"\r\n              placeholder=\"请输入内容\"\r\n            ></Input>\r\n          </FormItem>\r\n          <FormItem label=\"接口名称\" prop=\"apiName\">\r\n            <Input\r\n              :disabled=\"!!(formItem.id && formItem.isPersist === 1)\"\r\n              v-model=\"formItem.apiName\"\r\n              placeholder=\"请输入内容\"\r\n            ></Input>\r\n          </FormItem>\r\n          <FormItem label=\"请求地址\" prop=\"path\">\r\n            <Input\r\n              :disabled=\"!!(formItem.id && formItem.isPersist === 1)\"\r\n              v-model=\"formItem.path\"\r\n              placeholder=\"请输入内容\"\r\n            ></Input>\r\n          </FormItem>\r\n          <FormItem label=\"优先级\">\r\n            <InputNumber v-model=\"formItem.priority\"></InputNumber>\r\n          </FormItem>\r\n          <FormItem label=\"身份认证\">\r\n            <RadioGroup v-model=\"formItem.isAuth\" type=\"button\">\r\n              <Radio :disabled=\"!!(formItem.id && formItem.isPersist === 0)\" label=\"0\">开启</Radio>\r\n              <Radio :disabled=\"!!(formItem.id && formItem.isPersist === 0)\" label=\"1\">关闭</Radio>\r\n            </RadioGroup>\r\n            <p>\r\n              <code>开启：未认证登录,提示\"认证失败,请重新登录!\";关闭: 不需要认证登录</code>\r\n            </p>\r\n          </FormItem>\r\n          <FormItem label=\"公开访问\">\r\n            <RadioGroup v-model=\"formItem.isOpen\" type=\"button\">\r\n              <Radio label=\"0\">允许</Radio>\r\n              <Radio label=\"1\">拒绝</Radio>\r\n            </RadioGroup>\r\n            <p>\r\n              <code>拒绝:提示\"请求地址,拒绝访问!\"</code>\r\n            </p>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio label=\"0\">启用</Radio>\r\n              <Radio label=\"1\">禁用</Radio>\r\n              <Radio label=\"2\">维护中</Radio>\r\n            </RadioGroup>\r\n            <p>\r\n              <code>禁用：提示\"请求地址,禁止访问!\";维护中：提示\"正在升级维护中,请稍后再试!\";</code>\r\n            </p>\r\n          </FormItem>\r\n          <FormItem label=\"描述\">\r\n            <Input v-model=\"formItem.apiDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Api from \"@/api/base/gateway/api\";\r\nimport gatewayLog from \"@/api/base/gateway/gatewayLog\";\r\nimport {autoTableHeight} from \"@/libs/tools\"\r\n\r\nexport default {\r\n  name: \"systemApi\",\r\n  data() {\r\n    const validateEn = (rule, value, callback) => {\r\n      let reg = /^[_.a-zA-Z0-9]+$/;\r\n      if (value === \"\") {\r\n        callback(new Error(\"接口标识不能为空\"));\r\n      } else if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"只允许字母、数字、点、下划线\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      autoTableHeight,\r\n      loading: false,\r\n      modalVisible: false,\r\n      modalTitle: \"\",\r\n      saving: false,\r\n      tableSelection: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        path: \"\",\r\n        apiName: \"\",\r\n        apiCode: \"\",\r\n        serviceId: \"\"\r\n      },\r\n      selectServiceList: [],\r\n      formItemRules: {\r\n        serviceId: [\r\n          { required: true, message: \"所属服务不能为空\", trigger: \"blur\" }\r\n        ],\r\n        apiCategory: [\r\n          { required: true, message: \"接口分类不能为空\", trigger: \"blur\" }\r\n        ],\r\n        path: [\r\n          { required: true, message: \"接口地址不能为空\", trigger: \"blur\" }\r\n        ],\r\n        apiCode: [{ required: true, validator: validateEn, trigger: \"blur\" }],\r\n        apiName: [\r\n          { required: true, message: \"接口名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        apiCode: \"\",\r\n        apiName: \"\",\r\n        apiCategory: \"default\",\r\n        path: \"\",\r\n        status: 0,\r\n        isAuth: 0,\r\n        openSwatch: false,\r\n        authSwatch: true,\r\n        serviceId: \"\",\r\n        priority: 0,\r\n        apiDesc: \"\",\r\n        isOpen: 0\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          width: 60,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"md5编码\",\r\n          key: \"apiCode\",\r\n          width: 300\r\n        },\r\n        {\r\n          title: \"名称\",\r\n          key: \"apiName\",\r\n          slot: \"apiName\",\r\n          width: 300,\r\n          filters: [\r\n            {\r\n              label: \"启用\",\r\n              value: 0\r\n            },\r\n            {\r\n              label: \"禁用\",\r\n              value: 1\r\n            }\r\n          ],\r\n          filterMultiple: false,\r\n          filterMethod(value, row) {\r\n            if (value === 0) {\r\n              return row.status === 0;\r\n            } else if (value === 1) {\r\n              return row.status === 1;\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: \"地址\",\r\n          key: \"path\",\r\n          width: 200\r\n        },\r\n        {\r\n          title: \"分类\",\r\n          key: \"apiCategory\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"服务名称\",\r\n          key: \"serviceId\",\r\n          width: 200\r\n        },\r\n        {\r\n          title: \"接口安全\",\r\n          key: \"isAuth\",\r\n          slot: \"isAuth\",\r\n          width: 300\r\n        },\r\n        {\r\n          title: \"描述\",\r\n          key: \"apiDesc\",\r\n          width: 200\r\n        },\r\n        {\r\n          title: \"最后更新时间\",\r\n          key: \"updateTime\",\r\n          width: 180\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          key: \"\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          width: 120\r\n        }\r\n      ],\r\n      data: []\r\n    };\r\n  },\r\n  methods: {\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.modalTitle = \"编辑接口 - \" + data.apiName;\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else {\r\n        this.modalTitle = \"添加接口\";\r\n      }\r\n      this.formItem.status = this.formItem.status + \"\";\r\n      this.formItem.isAuth = this.formItem.isAuth + \"\";\r\n      this.formItem.isOpen = this.formItem.isOpen + \"\";\r\n      this.modalVisible = true;\r\n    },\r\n    handleResetForm(form) {\r\n      if(form === 'searchForm'){\r\n        this.$refs['searchForm'].resetFields();\r\n      }else if(form === 'form1'){\r\n        this.$refs['form1'].resetFields();\r\n      }\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        apiCode: \"\",\r\n        apiName: \"\",\r\n        apiCategory: \"default\",\r\n        path: \"\",\r\n        status: 0,\r\n        isAuth: 0,\r\n        serviceId: \"\",\r\n        priority: 0,\r\n        apiDesc: \"\",\r\n        isOpen: 0\r\n      };\r\n      //重置验证\r\n      this.handleResetForm(\"form1\");\r\n      this.modalVisible = false;\r\n      this.saving = false;\r\n    },\r\n    handleSubmit() {\r\n      this.$refs[\"form1\"].validate(valid => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          if (this.formItem.id) {\r\n            Api.editApi(this.formItem)\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"保存成功\");\r\n                }\r\n                this.handleReset();\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n              });\r\n          } else {\r\n            Api.addApi(this.formItem)\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"保存成功\");\r\n                }\r\n                this.handleReset();\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n              });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleRemove(data) {\r\n      this.$Modal.confirm({\r\n        title: \"确定删除吗？\",\r\n        onOk: () => {\r\n          Api.removeApis([data.id]).then(res => {\r\n            this.handleSearch();\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(\"删除成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleSearch(page) {\r\n      this.tableSelection = [];\r\n      if (page) {\r\n        this.pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      Api.listPage(this.pageInfo)\r\n        .then(res => {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = parseInt(res.data.total);\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleLoadServiceList() {\r\n      gatewayLog.getServiceList().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.selectServiceList = res.data;\r\n        }\r\n      });\r\n    },\r\n    handleTableSelectChange(selection) {\r\n      this.tableSelection = selection;\r\n    },\r\n    handleBatchClick(name) {\r\n      if (name) {\r\n        this.$Modal.confirm({\r\n          title: `已勾选${this.tableSelection.length}项,是否继续执行操作？`,\r\n          onOk: () => {\r\n            let ids = [];\r\n            this.tableSelection.map(item => {\r\n              if (!ids.includes(item.id)) {\r\n                ids.push(item.id);\r\n              }\r\n            });\r\n            if(name ==='remove'){\r\n              Api.removeApis(ids).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"批量操作成功\");\r\n                }\r\n                this.handleSearch();\r\n              });\r\n            }else{\r\n              let field = name.substr(0,name.length-1);\r\n              let value = name.replace(field,'');\r\n              Api.batchUpdateField({ ids: ids, value: value,field:field}).then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"批量操作成功\");\r\n                }\r\n                this.handleSearch();\r\n              });\r\n          }}});\r\n      }\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleLoadServiceList();\r\n    this.handleSearch();\r\n  }\r\n};\r\n</script>\r\n"]}]}