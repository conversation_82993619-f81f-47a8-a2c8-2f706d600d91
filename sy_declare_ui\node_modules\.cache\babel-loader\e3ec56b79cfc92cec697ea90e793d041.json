{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue", "mtime": 1752737748510}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Common", "Dictionary", "autoTableHeight", "name", "data", "h", "$createElement", "statusOps", "modalTitle1", "modalTitle2", "state1", "state2", "loading1", "loading2", "saving1", "saving2", "modalVisible1", "modalVisible2", "modalVisible3", "data1", "data2", "total1", "total2", "selectRow", "columns", "title", "type", "width", "align", "key", "render", "_ref", "row", "value", "_ref2", "code", "slot", "valueColumns", "_ref3", "_ref4", "tooltip", "formQuery1", "status", "page", "limit", "formQuery2", "parentId", "formItem2", "id", "description", "sort", "formItem1", "formItemRules1", "required", "message", "trigger", "formItemRules2", "methods", "handleModal", "Object", "assign", "handleModal2", "action", "handleSearch", "_this", "listPage", "then", "res", "parseInt", "total", "records", "finally", "listValuePage", "handleSet", "handleResetForm", "$refs", "resetFields", "handleRemove", "_this2", "$Modal", "confirm", "content", "onOk", "remove", "$Message", "success", "removeValue", "handleClick", "handleSubmit1", "_this3", "validate", "valid", "add", "handleReset", "edit", "handleSubmit2", "_this4", "addValue", "editValue", "saving3", "handlePage1", "handlePageSize1", "size", "handlePage2", "handlePageSize2", "mounted"], "sources": ["src/view/module/base/dictionary/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"formSearch1\" class=\"searchForm\" :model=\"formQuery1\" inline\r\n            @keydown.enter.native=\"handleSearch('formSearch1')\">\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" v-model=\"formQuery1.name\" placeholder=\"请输入字典名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"code\">\r\n          <Input type=\"text\" v-model=\"formQuery1.code \" placeholder=\"请输入字典编号\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"formQuery1.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  :transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch('formSearch1')\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('formSearch1')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top \">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data1\" :loading=\"loading1\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <a @click=\"handleModal(row)\" v-if=\"hasAuthority('dictionaryEdit')\">编辑</a>&nbsp;\r\n          <a @click=\"handleSet(row)\" v-if=\"hasAuthority('dictionarySet')\">字典配置</a>&nbsp;\r\n          <a @click=\"handleClick('remove',row,'formSearch1')\" v-if=\"hasAuthority('dictionaryDel')\">删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total1\" size=\"small\" :current=\"formQuery1.page\" :page-size=\"formQuery1.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage1\"\r\n            @on-page-size-change=\"handlePageSize1\"></Page>\r\n    </Card>\r\n    <!-- 数据字典添加与编辑 -->\r\n    <Modal v-model=\"modalVisible1\" :title=\"modalTitle1\" :width=\"500\" @on-cancel=\"handleReset('form1')\">\r\n      <Form ref=\"form1\" :model=\"formItem1\" :rules=\"formItemRules1\" :label-width=\"100\" class=\"dicListForm\">\r\n        <FormItem label=\"字典编号\" prop=\"code\">\r\n          <Input v-model=\"formItem1.code\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"字典名称\" prop=\"name\">\r\n          <Input v-model=\"formItem1.name\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem1.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem1.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"\">\r\n        <Button type=\"default\" @click=\"handleReset('form1')\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit1()\" :loading=\"saving1\">保存</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 配置字典列表 -->\r\n    <Modal v-model=\"modalVisible2\" title=\"字典列表\" :width=\"900\" @on-cancel=\"handleReset('formSearch2')\"\r\n           class=\"dicListModal\">\r\n      <Form ref=\"formSearch2\" :model=\"formQuery2\" inline :label-width=\"40\"\r\n            @keydown.enter.native=\"handleSearch('formSearch2')\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formQuery2.name\" :maxlength=\"50\" placeholder=\"请输入内容\" style=\"width:200px\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"status\">\r\n          <Select v-model=\"formQuery2.status\" style=\"width:200px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" style=\"margin-right:15px;\" @click=\"handleSearch('formSearch2')\">查询</Button>\r\n          <Button @click=\"handleResetForm('formSearch2')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"margin-top: 8px\">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal2('add')\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :columns=\"valueColumns\" :data=\"data2\" :loading=\"loading2\" :border=\"true\" :max-height=\"560\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <Button type=\"text\" @click=\"handleModal2('edit',row)\">编辑</Button>\r\n          <Button type=\"text\" @click=\"handleClick('remove',row,'formSearch2')\">删除</Button>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total2\" :current=\"formQuery2.page\" :page-size=\"formQuery2.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage2\"\r\n            @on-page-size-change=\"handlePageSize2\"></Page>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('formSearch2')\">关闭</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 添加与编辑字典列表 -->\r\n    <Modal v-model=\"modalVisible3\" :title=\"modalTitle2\" :width=\"500\" @on-cancel=\"handleReset('form2')\">\r\n      <Form ref=\"form2\" :model=\"formItem2\" :rules=\"formItemRules2\" :label-width=\"80\" class=\"dicListForm\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formItem2.name\" :maxlength=\"100\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"数据值\" prop=\"value\">\r\n          <Input v-model=\"formItem2.value\" :maxlength=\"5000\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem2.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"排序值\" prop=\"sort\">\r\n          <InputNumber :min=\"1\" v-model=\"formItem2.sort\"></InputNumber> &nbsp;<span>值越小越靠前,支持小数</span>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem2.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('form2')\">关闭</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit2()\" :loading=\"saving2\" v-if=\"hasAuthority('dictionarySet')\">确定</Button>\r\n      </template>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Common from '@/api/basic/common'\r\nimport Dictionary from '@/api/base/dictionary'\r\nimport {autoTableHeight} from \"@/libs/tools.js\"\r\n\r\nexport default {\r\n  name: 'dict',\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.statusOps,\r\n\r\n      modalTitle1: '添加字典',\r\n      modalTitle2: '添加字典值',\r\n      state1: 'add',//字典操作\r\n      state2: 'add',//字典值操作\r\n      loading1: false,\r\n      loading2: false,\r\n      saving1: false,\r\n      saving2: false,\r\n      modalVisible1: false,//字典新增修改弹框\r\n      modalVisible2: false,//字典明细弹框\r\n      modalVisible3: false,//字典值新增修改弹框\r\n      data1: [],\r\n      data2: [],\r\n      total1: 0,\r\n      total2: 0,\r\n      selectRow: {},\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 80,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '字典名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '字典编号',\r\n          key: 'code',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.code}>{row.code}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 300,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      valueColumns: [\r\n        {\r\n          title: '名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '数据值',\r\n          key: 'value',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.value}>{row.value}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center',\r\n          tooltip: true\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 150,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      formQuery1: {\r\n        name: '',\r\n        code: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formQuery2: {\r\n        parentId: null,\r\n        name: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formItem2: {\r\n        id: '',\r\n        parentId: '',\r\n        name: '',\r\n        value: '',\r\n        description: '',\r\n        status: 0,\r\n        sort: 1\r\n      },\r\n      formItem1: {\r\n        id: '',\r\n        code: '',\r\n        name: '',\r\n        description: '',\r\n        status: 0\r\n      },\r\n      formItemRules1: {\r\n        code: [\r\n          {required: true, message: '字典编号不能为空', trigger: 'blur'}\r\n        ],\r\n        name: [\r\n          {required: true, message: '字典名称不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n      formItemRules2: {\r\n        name: [\r\n          {required: true, message: '字典值名称不能为空', trigger: 'blur'}\r\n        ],\r\n        value: [\r\n          {required: true, message: '字典值名称对应的值不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 数据字典添加与编辑\r\n     */\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.state1 = 'edit';\r\n        this.modalTitle1 = '编辑字典';\r\n        this.formItem1 = Object.assign({}, this.formItem1, data);\r\n      } else {\r\n        this.state1 = 'add';\r\n        this.modalTitle1 = '添加字典';\r\n        this.formItem1 = {};\r\n      }\r\n      this.modalVisible1 = true;\r\n    },\r\n    /**\r\n     * 数据字典值添加与编辑\r\n     */\r\n    handleModal2(action,data) {\r\n      this.state2 = action;\r\n      if (this.state2 === 'edit') {\r\n        this.modalTitle2 = '编辑字典值';\r\n        this.formItem2 = Object.assign({}, this.formItem2, data);\r\n      } else {\r\n        this.modalTitle2 = '添加字典值';\r\n        this.formItem2 = {id: '',\r\n          parentId: '',\r\n          name: '',\r\n          value: '',\r\n          description: '',\r\n          status: 0,\r\n          sort: 1};\r\n      }\r\n      this.formItem2.parentId = this.selectRow.id\r\n      this.modalVisible3 = true;\r\n    },\r\n    /**\r\n     * 查询，重置\r\n     */\r\n    handleSearch(name) {\r\n      if (name === 'formSearch1') {\r\n        this.loading1 = true;\r\n        Dictionary.listPage(this.formQuery1).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total1 = parseInt(res.data.total);\r\n            this.data1 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading1 = false\r\n        });\r\n      } else {\r\n        this.loading2 = true;\r\n        this.formQuery2.parentId = this.selectRow.id;\r\n        Dictionary.listValuePage(this.formQuery2).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total2 = parseInt(res.data.total);\r\n            this.data2 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading2 = false\r\n        });\r\n      }\r\n    },\r\n    handleSet(row) {\r\n      this.modalVisible2 = true\r\n      this.selectRow = row\r\n      this.handleResetForm(\"formSearch2\");\r\n    },\r\n    handleResetForm(name) {\r\n      if(name === \"formSearch1\"){\r\n        this.$refs[\"formSearch1\"].resetFields();\r\n      }else{\r\n        this.$refs[\"formSearch2\"].resetFields();\r\n      }\r\n      this.handleSearch(name, 1, 10);\r\n    },\r\n    handleRemove(data, type) {\r\n      this.$Modal.confirm({\r\n        title: '提示！',\r\n        content: '你确定要删除这条数据吗',\r\n        onOk: () => {\r\n          if (type === 'formSearch1') {\r\n            Dictionary.remove(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch1')\r\n              }\r\n            });\r\n          } else {\r\n            Dictionary.removeValue(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch2')\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleClick(name,row, type) {\r\n      switch (name) {\r\n        case 'remove':\r\n          this.handleRemove(row, type)\r\n          break\r\n      }\r\n    },\r\n    handleSubmit1() {\r\n      this.saving1 = true;\r\n      this.$refs['form1'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state1 === 'add') {\r\n            Dictionary.add(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          } else {\r\n            Dictionary.edit(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('编辑成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving1 = false\r\n        }\r\n      });\r\n    },\r\n    handleSubmit2() {\r\n      this.saving2 = true;\r\n      this.$refs['form2'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state2 === 'add') {\r\n            Dictionary.addValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          } else {\r\n            Dictionary.editValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving2 = false\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReset(name) {\r\n      if (name === 'form1') {\r\n        this.modalVisible1 = false;\r\n        this.saving1 = false\r\n        this.$refs['form1'].resetFields();\r\n      } else if (name === 'form2') {\r\n        this.saving3 = false\r\n        this.modalVisible3 = false;\r\n        this.$refs['form2'].resetFields();\r\n      } else if (name === 'formSearch2') { // name=formQuery2\r\n        this.modalVisible2 = false;\r\n        this.saving2 = false\r\n        this.$refs['formSearch2'].resetFields();\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage1(page) {\r\n      this.formQuery1.page = page;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    handlePageSize1(size) {\r\n      this.formQuery1.limit = size;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage2(page) {\r\n      this.formQuery2.page = page;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n    handlePageSize2(size) {\r\n      this.formQuery2.limit = size;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handlePage1(1)\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n\r\n.dicListModal {\r\n  .ivu-modal-body {\r\n    min-height: 450px;\r\n\r\n    .ivu-table-cell {\r\n      padding-left: 4px;\r\n      padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n.dicListForm {\r\n  padding-right: 25px;\r\n\r\n  textarea {\r\n    resize: none;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;AA2IA,OAAAA,MAAA;AACA,OAAAC,UAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA;MACAJ,eAAA,EAAAA,eAAA;MACAK,SAAA,EAAAP,MAAA,CAAAO,SAAA;MAEAC,WAAA;MACAC,WAAA;MACAC,MAAA;MAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MACAC,QAAA;MACAC,OAAA;MACAC,OAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,aAAA;MAAA;MACAC,KAAA;MACAC,KAAA;MACAC,MAAA;MACAC,MAAA;MACAC,SAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAzB,CAAA,EAAA0B,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UACA,OAAA3B,CAAA;YAAA;cAAAF,IAAA;cAAA8B,KAAA,EAAAD,GAAA,CAAA7B;YAAA;UAAA,IAAA6B,GAAA,CAAA7B,IAAA;QACA;MACA,GACA;QACAsB,KAAA;QACAI,GAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAzB,CAAA,EAAA6B,KAAA;UAAA,IAAAF,GAAA,GAAAE,KAAA,CAAAF,GAAA;UACA,OAAA3B,CAAA;YAAA;cAAAF,IAAA;cAAA8B,KAAA,EAAAD,GAAA,CAAAG;YAAA;UAAA,IAAAH,GAAA,CAAAG,IAAA;QACA;MACA,GACA;QACAV,KAAA;QACAI,GAAA;QACAD,KAAA;MACA,GACA;QACAH,KAAA;QACAI,GAAA;QACAO,IAAA;QACAT,KAAA;MACA,GACA;QACAF,KAAA;QACAW,IAAA;QACAT,KAAA;QACAC,KAAA;MACA,EACA;MACAS,YAAA,GACA;QACAZ,KAAA;QACAI,GAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAzB,CAAA,EAAAiC,KAAA;UAAA,IAAAN,GAAA,GAAAM,KAAA,CAAAN,GAAA;UACA,OAAA3B,CAAA;YAAA;cAAAF,IAAA;cAAA8B,KAAA,EAAAD,GAAA,CAAA7B;YAAA;UAAA,IAAA6B,GAAA,CAAA7B,IAAA;QACA;MACA,GACA;QACAsB,KAAA;QACAI,GAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAzB,CAAA,EAAAkC,KAAA;UAAA,IAAAP,GAAA,GAAAO,KAAA,CAAAP,GAAA;UACA,OAAA3B,CAAA;YAAA;cAAAF,IAAA;cAAA8B,KAAA,EAAAD,GAAA,CAAAC;YAAA;UAAA,IAAAD,GAAA,CAAAC,KAAA;QACA;MACA,GACA;QACAR,KAAA;QACAI,GAAA;QACAD,KAAA;QACAY,OAAA;MACA,GACA;QACAf,KAAA;QACAI,GAAA;QACAO,IAAA;QACAT,KAAA;MACA,GACA;QACAF,KAAA;QACAW,IAAA;QACAT,KAAA;QACAC,KAAA;MACA,EACA;MACAa,UAAA;QACAtC,IAAA;QACAgC,IAAA;QACAO,MAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;QACAC,QAAA;QACA3C,IAAA;QACAuC,MAAA;QACAC,IAAA;QACAC,KAAA;MACA;MACAG,SAAA;QACAC,EAAA;QACAF,QAAA;QACA3C,IAAA;QACA8B,KAAA;QACAgB,WAAA;QACAP,MAAA;QACAQ,IAAA;MACA;MACAC,SAAA;QACAH,EAAA;QACAb,IAAA;QACAhC,IAAA;QACA8C,WAAA;QACAP,MAAA;MACA;MACAU,cAAA;QACAjB,IAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApD,IAAA,GACA;UAAAkD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA;QACArD,IAAA,GACA;UAAAkD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAtB,KAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA;IACA;AACA;AACA;IACAC,WAAA,WAAAA,YAAAtD,IAAA;MACA,IAAAA,IAAA;QACA,KAAAM,MAAA;QACA,KAAAF,WAAA;QACA,KAAA2C,SAAA,GAAAQ,MAAA,CAAAC,MAAA,UAAAT,SAAA,EAAA/C,IAAA;MACA;QACA,KAAAM,MAAA;QACA,KAAAF,WAAA;QACA,KAAA2C,SAAA;MACA;MACA,KAAAnC,aAAA;IACA;IACA;AACA;AACA;IACA6C,YAAA,WAAAA,aAAAC,MAAA,EAAA1D,IAAA;MACA,KAAAO,MAAA,GAAAmD,MAAA;MACA,SAAAnD,MAAA;QACA,KAAAF,WAAA;QACA,KAAAsC,SAAA,GAAAY,MAAA,CAAAC,MAAA,UAAAb,SAAA,EAAA3C,IAAA;MACA;QACA,KAAAK,WAAA;QACA,KAAAsC,SAAA;UAAAC,EAAA;UACAF,QAAA;UACA3C,IAAA;UACA8B,KAAA;UACAgB,WAAA;UACAP,MAAA;UACAQ,IAAA;QAAA;MACA;MACA,KAAAH,SAAA,CAAAD,QAAA,QAAAvB,SAAA,CAAAyB,EAAA;MACA,KAAA9B,aAAA;IACA;IACA;AACA;AACA;IACA6C,YAAA,WAAAA,aAAA5D,IAAA;MAAA,IAAA6D,KAAA;MACA,IAAA7D,IAAA;QACA,KAAAS,QAAA;QACAX,UAAA,CAAAgE,QAAA,MAAAxB,UAAA,EAAAyB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAH,KAAA,CAAA3C,MAAA,GAAA+C,QAAA,CAAAD,GAAA,CAAA/D,IAAA,CAAAiE,KAAA;YACAL,KAAA,CAAA7C,KAAA,GAAAgD,GAAA,CAAA/D,IAAA,CAAAkE,OAAA;UACA;QACA,GAAAC,OAAA;UACAP,KAAA,CAAApD,QAAA;QACA;MACA;QACA,KAAAC,QAAA;QACA,KAAAgC,UAAA,CAAAC,QAAA,QAAAvB,SAAA,CAAAyB,EAAA;QACA/C,UAAA,CAAAuE,aAAA,MAAA3B,UAAA,EAAAqB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAH,KAAA,CAAA1C,MAAA,GAAA8C,QAAA,CAAAD,GAAA,CAAA/D,IAAA,CAAAiE,KAAA;YACAL,KAAA,CAAA5C,KAAA,GAAA+C,GAAA,CAAA/D,IAAA,CAAAkE,OAAA;UACA;QACA,GAAAC,OAAA;UACAP,KAAA,CAAAnD,QAAA;QACA;MACA;IACA;IACA4D,SAAA,WAAAA,UAAAzC,GAAA;MACA,KAAAf,aAAA;MACA,KAAAM,SAAA,GAAAS,GAAA;MACA,KAAA0C,eAAA;IACA;IACAA,eAAA,WAAAA,gBAAAvE,IAAA;MACA,IAAAA,IAAA;QACA,KAAAwE,KAAA,gBAAAC,WAAA;MACA;QACA,KAAAD,KAAA,gBAAAC,WAAA;MACA;MACA,KAAAb,YAAA,CAAA5D,IAAA;IACA;IACA0E,YAAA,WAAAA,aAAAzE,IAAA,EAAAsB,IAAA;MAAA,IAAAoD,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACAvD,KAAA;QACAwD,OAAA;QACAC,IAAA,WAAAA,KAAA;UACA,IAAAxD,IAAA;YACAzB,UAAA,CAAAkF,MAAA,CAAA/E,IAAA,CAAA4C,EAAA,EAAAkB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAW,MAAA,CAAAM,QAAA,CAAAC,OAAA;gBACAP,MAAA,CAAAf,YAAA;cACA;YACA;UACA;YACA9D,UAAA,CAAAqF,WAAA,CAAAlF,IAAA,CAAA4C,EAAA,EAAAkB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAW,MAAA,CAAAM,QAAA,CAAAC,OAAA;gBACAP,MAAA,CAAAf,YAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAwB,WAAA,WAAAA,YAAApF,IAAA,EAAA6B,GAAA,EAAAN,IAAA;MACA,QAAAvB,IAAA;QACA;UACA,KAAA0E,YAAA,CAAA7C,GAAA,EAAAN,IAAA;UACA;MACA;IACA;IACA8D,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAA3E,OAAA;MACA,KAAA6D,KAAA,UAAAe,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA/E,MAAA;YACAT,UAAA,CAAA2F,GAAA,CAAAH,MAAA,CAAAtC,SAAA,EAAAe,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAsB,MAAA,CAAAI,WAAA;gBACAJ,MAAA,CAAAL,QAAA,CAAAC,OAAA;gBACAI,MAAA,CAAA1B,YAAA;cACA;YACA,GAAAQ,OAAA;cACAkB,MAAA,CAAA3E,OAAA;YACA;UACA;YACAb,UAAA,CAAA6F,IAAA,CAAAL,MAAA,CAAAtC,SAAA,EAAAe,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAsB,MAAA,CAAAI,WAAA;gBACAJ,MAAA,CAAAL,QAAA,CAAAC,OAAA;gBACAI,MAAA,CAAA1B,YAAA;cACA;YACA,GAAAQ,OAAA;cACAkB,MAAA,CAAA3E,OAAA;YACA;UACA;QACA;UACA2E,MAAA,CAAA3E,OAAA;QACA;MACA;IACA;IACAiF,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAjF,OAAA;MACA,KAAA4D,KAAA,UAAAe,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAK,MAAA,CAAArF,MAAA;YACAV,UAAA,CAAAgG,QAAA,CAAAD,MAAA,CAAAjD,SAAA,EAAAmB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA6B,MAAA,CAAAH,WAAA;gBACAG,MAAA,CAAAZ,QAAA,CAAAC,OAAA;gBACAW,MAAA,CAAAjC,YAAA;cACA;YACA,GAAAQ,OAAA;cACAyB,MAAA,CAAAjF,OAAA;YACA;UACA;YACAd,UAAA,CAAAiG,SAAA,CAAAF,MAAA,CAAAjD,SAAA,EAAAmB,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA6B,MAAA,CAAAH,WAAA;gBACAG,MAAA,CAAAZ,QAAA,CAAAC,OAAA;gBACAW,MAAA,CAAAjC,YAAA;cACA;YACA,GAAAQ,OAAA;cACAyB,MAAA,CAAAjF,OAAA;YACA;UACA;QACA;UACAiF,MAAA,CAAAjF,OAAA;QACA;MACA;IACA;IAEA8E,WAAA,WAAAA,YAAA1F,IAAA;MACA,IAAAA,IAAA;QACA,KAAAa,aAAA;QACA,KAAAF,OAAA;QACA,KAAA6D,KAAA,UAAAC,WAAA;MACA,WAAAzE,IAAA;QACA,KAAAgG,OAAA;QACA,KAAAjF,aAAA;QACA,KAAAyD,KAAA,UAAAC,WAAA;MACA,WAAAzE,IAAA;QAAA;QACA,KAAAc,aAAA;QACA,KAAAF,OAAA;QACA,KAAA4D,KAAA,gBAAAC,WAAA;MACA;IACA;IAEA;AACA;AACA;IACAwB,WAAA,WAAAA,YAAAzD,IAAA;MACA,KAAAF,UAAA,CAAAE,IAAA,GAAAA,IAAA;MACA,KAAAoB,YAAA;IACA;IACAsC,eAAA,WAAAA,gBAAAC,IAAA;MACA,KAAA7D,UAAA,CAAAG,KAAA,GAAA0D,IAAA;MACA,KAAAvC,YAAA;IACA;IACA;AACA;AACA;IACAwC,WAAA,WAAAA,YAAA5D,IAAA;MACA,KAAAE,UAAA,CAAAF,IAAA,GAAAA,IAAA;MACA,KAAAoB,YAAA;IACA;IACAyC,eAAA,WAAAA,gBAAAF,IAAA;MACA,KAAAzD,UAAA,CAAAD,KAAA,GAAA0D,IAAA;MACA,KAAAvC,YAAA;IACA;EACA;EACA0C,OAAA,WAAAA,QAAA;IACA,KAAAL,WAAA;EACA;AAEA"}]}