# Nacos配置验证指南

## 问题解决

### 原因分析
之前的错误 `jdbcUrl is required with driverClassName` 是因为：

1. **本地配置覆盖**：`application-local.yml` 中还有本地数据源配置，覆盖了Nacos配置
2. **属性名称问题**：HikariCP需要 `jdbcUrl` 而不是 `url`
3. **配置绑定问题**：`@ConfigurationProperties` 自动绑定存在问题

### 解决方案
1. ✅ 完全移除 `application-local.yml` 中的所有数据源配置
2. ✅ 修改 `NacosDataSourceConfiguration` 使用 `@Value` 手动绑定配置
3. ✅ 在代码中将 `url` 转换为 `jdbcUrl`

## 当前配置状态

### 1. application-local.yml
```yaml
# 数据源配置已完全迁移到Nacos配置中心
# 请确保在Nacos的workflow.properties中配置以下必需参数：
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.datasource.url=************************************?useSSL=false&useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&rewriteBatchedStatements=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
# spring.datasource.username=root
# spring.datasource.password=123456
# spring.datasource.type=com.zaxxer.hikari.HikariDataSource

spring:
  cloud:
    nacos:
      config:
        enabled: true
```

### 2. NacosDataSourceConfiguration.java
```java
@Configuration
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "true")
public class NacosDataSourceConfiguration {

    @Value("${spring.datasource.url}")
    private String url;
    
    @Value("${spring.datasource.username}")
    private String username;
    
    @Value("${spring.datasource.password}")
    private String password;
    
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    @Bean(name = "dataSource")
    @Primary
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        
        // 手动设置配置，将url转换为jdbcUrl
        dataSource.setJdbcUrl(url);  // 关键：HikariCP使用jdbcUrl
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);
        
        // 连接池配置
        dataSource.setMaximumPoolSize(20);
        dataSource.setMinimumIdle(5);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);
        dataSource.setLeakDetectionThreshold(60000);
        dataSource.setPoolName("ErpNacosPool");
        
        return dataSource;
    }
}
```

## Nacos配置要求

### 必需在Nacos中创建的配置

**配置ID**: `workflow.properties`  
**分组**: `DEFAULT_GROUP`  
**配置内容**:

```properties
# 基本数据源配置（必需）
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=************************************?useSSL=false&useUnicode=true&characterEncoding=utf-8&allowMultiQueries=true&rewriteBatchedStatements=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
```

**注意事项**:
- 配置中使用 `spring.datasource.url`，代码会自动转换为 HikariCP 需要的 `jdbcUrl`
- 确保数据库连接信息正确
- 确保数据库服务正在运行

## 验证步骤

### 1. 检查Nacos配置
1. 登录Nacos控制台
2. 进入配置管理 -> 配置列表
3. 确认 `workflow.properties` 存在且内容正确

### 2. 启动应用
启动应用后应该看到以下日志：
```
🔄 开始创建Nacos数据源配置
📊 从Nacos获取配置: url=************************************?..., username=root, driver=com.mysql.cj.jdbc.Driver
✅ Nacos数据源配置创建成功
🔄 创建aimoProdMysql数据源别名（指向主数据源）
🔄 创建aimoTestMysql数据源别名（指向主数据源）
```

### 3. 错误排查
如果仍然出现错误：

#### 错误：配置未加载
```
Could not resolve placeholder 'spring.datasource.url'
```
**解决方案**：
- 检查Nacos服务是否运行
- 检查 `bootstrap.yml` 中的Nacos配置
- 检查 `workflow.properties` 是否存在

#### 错误：数据库连接失败
```
Communications link failure
```
**解决方案**：
- 检查数据库服务是否运行
- 检查数据库连接信息是否正确
- 检查网络连接

#### 错误：仍然提示jdbcUrl required
**解决方案**：
- 确认 `application-local.yml` 中没有任何数据源配置
- 重启应用
- 检查日志中的配置加载信息

## 配置优先级

Spring Boot配置加载优先级（高到低）：
1. 命令行参数
2. application-local.yml（本地配置）
3. Nacos配置中心
4. application.yml

**重要**：确保本地配置文件中没有数据源配置，否则会覆盖Nacos配置。

---
**更新时间**: 2025-08-05  
**状态**: 问题已修复  
**下一步**: 重新启动应用进行验证
