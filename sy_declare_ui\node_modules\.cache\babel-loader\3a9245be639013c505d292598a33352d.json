{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\gatewayLog.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\gateway\\gatewayLog.js", "mtime": 1752737748398}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwp2YXIgbG9nUGF0aCA9ICIvYmFzZS9nYXRld2F5IjsKdmFyIHJlZnJlc2hHYXRld2F5ID0gZnVuY3Rpb24gcmVmcmVzaEdhdGV3YXkoKSB7CiAgdmFyIGRhdGEgPSB7fTsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGxvZ1BhdGggKyAnL3JlZnJlc2gnLAogICAgZGF0YTogZGF0YSwKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CgovKioNCiAqIOiOt+WPluWIhumhteaVsOaNrg0KICovCnZhciBsaXN0UGFnZSA9IGZ1bmN0aW9uIGxpc3RQYWdlKHBhcmFtcykgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogbG9nUGF0aCArICcvbGlzdFBhZ2UnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CgovKioNCiAqIOiOt+W<PERSON>luacjeWKoeWIl+ihqA0KICovCnZhciBnZXRTZXJ2aWNlTGlzdCA9IGZ1bmN0aW9uIGdldFNlcnZpY2VMaXN0KCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogbG9nUGF0aCArICcvZ2V0U2VydmljZUxpc3QnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbGlzdFBhZ2U6IGxpc3RQYWdlLAogIHJlZnJlc2hHYXRld2F5OiByZWZyZXNoR2F0ZXdheSwKICBnZXRTZXJ2aWNlTGlzdDogZ2V0U2VydmljZUxpc3QKfTs="}, {"version": 3, "names": ["request", "logPath", "refreshGateway", "data", "url", "method", "listPage", "params", "getServiceList"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/gateway/gatewayLog.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\nconst logPath = \"/base/gateway\";\r\nconst refreshGateway = () => {\r\n  const data = {}\r\n  return request({\r\n    url: logPath + '/refresh',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: logPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 获取服务列表\r\n */\r\nconst getServiceList = () => {\r\n  return request({\r\n    url: logPath + '/getServiceList',\r\n    method: 'get'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  refreshGateway,\r\n  getServiceList,\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AAEpC,IAAMC,OAAO,GAAG,eAAe;AAC/B,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAC3B,IAAMC,IAAI,GAAG,CAAC,CAAC;EACf,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,OAAO,GAAG,UAAU;IACzBE,IAAI,EAAJA,IAAI;IACJE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOP,OAAO,CAAC;IACbI,GAAG,EAAEH,OAAO,GAAG,WAAW;IAC1BM,MAAM,EAANA,MAAM;IACNF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMG,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAC3B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAEH,OAAO,GAAG,iBAAiB;IAChCI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbC,QAAQ,EAARA,QAAQ;EACRJ,cAAc,EAAdA,cAAc;EACdM,cAAc,EAAdA;AACF,CAAC"}]}