{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\role\\index.vue", "mtime": 1752737748514}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Role", "Common", "Authority", "listConvertTree", "PersonSelectEx", "autoTableHeight", "name", "components", "data", "validateCode", "rule", "value", "callback", "reg", "Error", "test", "statusOps", "loading", "modalVisible", "modalTitle", "modalType", "loadTree", "modalRole", "saving", "current", "forms", "selectMenus", "selectUsers", "selectShops", "selectShops1", "selectPlatforms", "pageInfo", "total", "page", "limit", "roleCode", "<PERSON><PERSON><PERSON>", "formItemRules", "required", "validator", "trigger", "message", "formItem", "id", "status", "roleDesc", "grantMenus", "grantActions", "menuName", "userIds", "columns", "type", "width", "align", "title", "key", "slot", "fixed", "menuColumns", "min<PERSON><PERSON><PERSON>", "template", "methods", "handleSearchMenu", "handleLoadRoleGranted", "handleResetMenu", "handleCheckAll", "indeterminate", "checkAll", "allData", "map", "item", "parentId", "push", "checkAllGroup", "handleModal", "Object", "assign", "$refs", "resetFields", "handleLoadRoleUsers", "handleResetForm", "form", "handleSearch", "handleTabClick", "handleReset", "_this", "personSelectRef", "onVisibleChange", "handleSubmit", "_this2", "validate", "valid", "edit", "then", "res", "$Message", "success", "finally", "add", "authorityIds", "getCheckedAuthorities", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "xItem", "childrenIds", "children", "v", "authorityId", "flag", "_iterator2", "_step2", "yItem", "includes", "err", "e", "f", "grantAuthorityForRole", "roleId", "addRoleUsers", "_this3", "listPage", "records", "parseInt", "handlePage", "handlePageSize", "size", "handleRemove", "_this4", "$Modal", "confirm", "onOk", "remove", "menus", "getCheckedProp", "concat", "_this5", "that", "p1", "getAuthorityMenu", "p2", "getAuthorityForRole", "Promise", "all", "values", "res1", "res2", "code", "opt", "<PERSON><PERSON><PERSON>", "parent<PERSON><PERSON>", "startPid", "length", "actions", "authority", "indexOf", "_isChecked", "Math", "min", "apply", "getRoleUsers", "for<PERSON>ach", "mounted"], "sources": ["src/view/module/base/role/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"roleManage\">\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline\r\n            @keydown.enter.native=\"(e) => {e.preventDefault();handleSearch(1);}\">\r\n        <FormItem prop=\"roleName\">\r\n          <Input type=\"text\" v-model=\"pageInfo.roleName\" placeholder=\"角色名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"roleCode\">\r\n          <Input type=\"text\" v-model=\"pageInfo.roleCode\" placeholder=\"角色编码\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  :transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal('', 'add')\" :disabled=\"!hasAuthority('roleAdd')\">添加</Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':(v.key ===1?'error':'warning')\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a style=\"margin-right: 8px\" @click=\"handleModal(row, 'view')\">查看</a>\r\n          <a @click=\"handleModal(row, 'edit')\" v-if=\"hasAuthority('roleEdit')\">编辑</a>&nbsp;\r\n          <a @click=\"handleRemove(row)\" v-if=\"hasAuthority('roleDel')\">删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" size=\"small\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\" :show-sizer=\"true\" :show-total=\"true\"\r\n            @on-change=\"handlePage\" @on-page-size-change=\"handlePageSize\"></Page>\r\n    </Card>\r\n    <!-- 点击添加之后的弹窗 -->\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" width=\"40\" @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Tabs @on-click=\"handleTabClick\" :value=\"current\">\r\n          <TabPane label=\"角色信息\" name=\"form1\">\r\n            <Form v-show=\"current === 'form1'\" ref=\"form1\" :disabled=\"modalType === 'view'\" :model=\"formItem\"\r\n                  :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"角色标识\" prop=\"roleCode\">\r\n                <Input v-model=\"formItem.roleCode\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"角色名称\" prop=\"roleName\">\r\n                <Input v-model=\"formItem.roleName\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n              <FormItem label=\"状态\">\r\n                <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n                  <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n                </RadioGroup>\r\n              </FormItem>\r\n              <FormItem label=\"描述\">\r\n                <Input v-model=\"formItem.roleDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n          <TabPane :disabled=\"!formItem.id\" label=\"分配权限\" name=\"form2\">\r\n            <Form v-show=\"current === 'form2'\" ref=\"form2\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n              <FormItem label=\"菜单名称\" prop=\"menuName\">\r\n                <Input v-model=\"formItem.menuName\" placeholder=\"请输入\" style=\"width: 250px\"\r\n                       @keydown.enter.native=\"(e) => {e.preventDefault();handleSearchMenu()}\"/>\r\n                <Button type=\"primary\" @click=\"handleSearchMenu\" style=\"margin: 0 20px\" :loading=\"loadTree\">查询\r\n                </Button>\r\n                <Button @click=\"handleResetMenu\">重置</Button>\r\n              </FormItem>\r\n              <FormItem label=\"功能菜单\" prop=\"grantMenus\">\r\n                <tree-table ref=\"tree\" style=\"max-height: 480px; overflow: auto\" expand-key=\"menuName\"\r\n                            :expand-type=\"false\" :is-fold=\"false\" :tree-type=\"true\" :selectable=\"modalType !== 'view'\"\r\n                            :columns=\"menuColumns\" :data=\"selectMenus\" >\r\n                  <template v-slot:operation=\"scope\">\r\n                    <CheckboxGroup key=\"box1\" v-model=\"formItem.grantActions\">\r\n                      <Checkbox :disabled=\"modalType === 'view'\" v-for=\"item in scope.row.actionList\"\r\n                                :key=\"item.authorityId\" :label=\"item.authorityId\" >\r\n                        <span :title=\"item.actionDesc\">{{ item.actionName }}</span>\r\n                      </Checkbox>\r\n                    </CheckboxGroup>\r\n                  </template>\r\n                </tree-table>\r\n              </FormItem>\r\n            </Form>\r\n          </TabPane>\r\n          <TabPane :disabled=\"!formItem.id\" label=\"角色成员\" name=\"form3\">\r\n            <Form v-show=\"current === 'form3'\" ref=\"form3\" :disabled=\"modalType === 'view'\" :model=\"formItem\"\r\n                  :rules=\"formItemRules\" @keydown.enter.native=\"(e) => { e.preventDefault(); }\">\r\n              <PersonSelectEx ref=\"personSelectRef\" @clearSelected=\"formItem.userIds = []\" groupName=\"role_manage_edit\" v-model=\"formItem.userIds\"/>\r\n            </Form>\r\n          </TabPane>\r\n        </Tabs>\r\n        <div class=\"drawer-footer\" style=\"border-top: none\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" v-if=\"modalType !== 'view'\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Role from \"@/api/base/role\";\r\nimport Common from '@/api/basic/common'\r\nimport Authority from \"@/api/system/authority_1\";\r\nimport {listConvertTree} from \"@/libs/util\";\r\nimport PersonSelectEx from \"./PersonSelectEx.vue\"; //引入人员选择组件\r\nimport {autoTableHeight} from \"@/libs/tools.js\";\r\n\r\nexport default {\r\n  name: \"systemRole\",\r\n  components: {\r\n    PersonSelectEx\r\n  },\r\n  data() {\r\n    const validateCode = (rule, value, callback) => {\r\n      let reg = /^[_a-zA-Z0-9]+$/;\r\n      if (value === \"\") {\r\n        callback(new Error(\"角色标识不能为空\"));\r\n      } else if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"只允许字母、数字、下划线\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.statusOps,\r\n      loading: false,\r\n      modalVisible: false,\r\n      modalTitle: \"\",\r\n      modalType: \"\",\r\n      loadTree: false,\r\n      modalRole: \"\",\r\n      saving: false,\r\n      current: \"form1\",\r\n      forms: [\r\n        \"form1\",\r\n        \"form2\",\r\n        \"form3\"\r\n      ],\r\n      selectMenus: [],\r\n      selectUsers: [],\r\n      selectShops: [],\r\n      selectShops1: [[]],\r\n      selectPlatforms: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n      },\r\n      formItemRules: {\r\n        roleCode: [{required: true, validator: validateCode, trigger: \"blur\"}],\r\n        roleName: [\r\n          {required: true, message: \"角色名称不能为空\", trigger: \"blur\"},\r\n        ],\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n        status: 0,\r\n        roleDesc: \"\",\r\n        grantMenus: [],\r\n        grantActions: [],\r\n        menuName: \"\",\r\n        userIds: [],\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          width: 60,\r\n          align: \"center\",\r\n        },\r\n        {\r\n          title: \"角色名称\",\r\n          key: \"roleName\",\r\n          width: 300,\r\n        },\r\n        {\r\n          title: \"角色标识\",\r\n          key: \"roleCode\",\r\n          width: 300,\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          slot: \"status\",\r\n          key: \"status\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"最后修改时间\",\r\n          key: \"updateTime\",\r\n          width: 200,\r\n        },\r\n        {\r\n          title: \"描述\",\r\n          key: \"roleDesc\",\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          width: 200,\r\n        },\r\n      ],\r\n      menuColumns: [\r\n        {\r\n          title: \"菜单\",\r\n          key: \"menuName\",\r\n          minWidth: \"250px\",\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          type: \"template\",\r\n          template: \"operation\",\r\n          minWidth: \"200px\",\r\n        },\r\n      ],\r\n      data: [],\r\n    };\r\n  },\r\n  methods: {\r\n    handleSearchMenu() {\r\n      this.handleLoadRoleGranted(this.formItem.id);\r\n    },\r\n    handleResetMenu() {\r\n      this.formItem.menuName = '';\r\n      this.handleLoadRoleGranted(this.formItem.id);\r\n    },\r\n    handleCheckAll(id) {\r\n      if (this.indeterminate) {\r\n        this.checkAll = true;\r\n      } else {\r\n        this.checkAll = !this.checkAll;\r\n      }\r\n      this.indeterminate = false;\r\n\r\n      let allData = [];\r\n      this.selectShops.map((item) => {\r\n        if (id === item.parentId) {\r\n          allData.push(item.id);\r\n        }\r\n      });\r\n      if (this.checkAll) {\r\n        this.checkAllGroup = allData;\r\n        this.checkAll = true;\r\n      } else {\r\n        this.checkAllGroup = [];\r\n        this.checkAll = false;\r\n      }\r\n    },\r\n    handleModal(data, type) {\r\n      if (!type && !this.modalType) {\r\n        //没有操作类型\r\n        return;\r\n      }\r\n      if (data) {\r\n        if (type === \"view\") {\r\n          this.modalType = \"view\";\r\n          this.modalRole = data.roleName;\r\n        }\r\n        if (type === \"edit\") {\r\n          this.modalType = \"edit\";\r\n          this.modalRole = data.roleName;\r\n        }\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else if (type === \"add\") {\r\n        this.modalType = \"add\";\r\n        this.modalRole = \"\";\r\n      }\r\n      /* 添加角色 */\r\n      if (this.current === this.forms[0]) {\r\n        if (this.modalType === \"view\") {\r\n          this.modalTitle = \"查看角色 - \" + this.modalRole;\r\n        }\r\n        if (this.modalType === \"edit\") {\r\n          this.modalTitle = \"编辑角色 - \" + this.modalRole;\r\n        }\r\n        if (this.modalType === \"add\") {\r\n          this.modalTitle = \"添加角色\";\r\n          this.$refs['form1'].resetFields();\r\n        }\r\n      }\r\n      /* 分配权限 */\r\n      if (this.current === this.forms[1] && (this.modalType === 'edit' || this.modalType === 'view')) {\r\n        this.modalTitle = \"分配权限 - \" + this.modalRole;\r\n        this.handleLoadRoleGranted(this.formItem.id);\r\n      }\r\n      /* 角色成员 */\r\n      if (this.current === this.forms[2] && (this.modalType === 'edit' || this.modalType === 'view')) {\r\n        this.modalTitle = \"角色成员 - \" + this.modalRole;\r\n        this.handleLoadRoleUsers(this.formItem.id);\r\n      }\r\n      this.modalVisible = true;\r\n    },\r\n    handleResetForm(form) {\r\n      this.$refs[form].resetFields();\r\n      if (form === 'searchForm') {\r\n        this.handleSearch(1);\r\n      }\r\n    },\r\n    handleTabClick(name) {\r\n      this.current = name;\r\n      this.handleModal();\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        roleCode: \"\",\r\n        roleName: \"\",\r\n        status: 0,\r\n        menuName: \"\",\r\n        roleDesc: \"\"\r\n      };\r\n      //重置验证\r\n      this.modalVisible = false;\r\n      this.forms.map((form) => {\r\n        this.handleResetForm(form);\r\n      });\r\n      this.current = this.forms[0];\r\n      this.formItem.userIds = [];\r\n      this.formItem.grantMenus = [];\r\n      this.formItem.grantActions = [];\r\n      this.saving = false;\r\n      const {personSelectRef} = this.$refs;\r\n      personSelectRef.onVisibleChange(false);\r\n    },\r\n    handleSubmit() {\r\n      if (this.current === this.forms[0]) {\r\n        this.$refs[this.current].validate((valid) => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            if (this.formItem.id) {\r\n              Role.edit(this.formItem)\r\n                  .then((res) => {\r\n                    if (res['code'] === 0) {\r\n                      this.$Message.success(\"保存成功\");\r\n                      this.handleReset();\r\n                    }\r\n                    this.handleSearch();\r\n                  })\r\n                  .finally(() => {\r\n                    this.saving = false;\r\n                  });\r\n            } else {\r\n              Role.add(this.formItem)\r\n                  .then((res) => {\r\n                    if (res['code'] === 0) {\r\n                      this.$Message.success(\"保存成功\");\r\n                      this.handleReset();\r\n                    }\r\n                    this.handleSearch();\r\n                  })\r\n                  .finally(() => {\r\n                    this.saving = false;\r\n                  });\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (this.current === this.forms[1]) {\r\n        this.$refs[this.current].validate((valid) => {\r\n          if (valid) {\r\n            const authorityIds = this.getCheckedAuthorities();\r\n            for (const xItem of this.selectMenus) {\r\n              const childrenIds = xItem.children ? xItem.children.map((v) => v.authorityId) : [];\r\n              let flag = false;\r\n              for (const yItem of childrenIds) {\r\n                if (authorityIds.includes(yItem)) {\r\n                  flag = true;\r\n                  break;\r\n                }\r\n              }\r\n              if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                authorityIds.push(xItem.authorityId);\r\n              }\r\n            }\r\n            this.saving = true;\r\n            Authority.grantAuthorityForRole({\r\n              roleId: this.formItem.id,\r\n              authorityIds: authorityIds,\r\n            }).then((res) => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"授权成功\");\r\n                this.modalVisible = false;\r\n              }\r\n            }).finally(() => {\r\n              this.saving = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n\r\n      if (this.current === this.forms[2]) {\r\n        this.saving = true;\r\n        Role.addRoleUsers({\r\n          roleId: this.formItem.id,\r\n          userIds: this.formItem.userIds,\r\n        }).then((res) => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"保存成功\");\r\n            this.modalVisible = false;\r\n            this.handleReset();\r\n          }\r\n        }).finally(() => {\r\n          this.saving = false;\r\n        });\r\n      }\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      Role.listPage(this.pageInfo).then((res) => {\r\n        this.data = res.data.records;\r\n        this.pageInfo.total = parseInt(res.data.total);\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleRemove(data) {\r\n      this.$Modal.confirm({\r\n        title: \"确定删除吗？\",\r\n        onOk: () => {\r\n          Role.remove(data.id).then((res) => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(\"删除成功\");\r\n            }\r\n            this.handleSearch();\r\n          });\r\n        },\r\n      });\r\n    },\r\n    getCheckedAuthorities() {\r\n      const menus = this.$refs[\"tree\"].getCheckedProp(\"authorityId\");\r\n      return menus.concat(this.formItem.grantActions);\r\n    },\r\n    handleLoadRoleGranted() {\r\n      this.loadTree = true;\r\n      if (!this.formItem.id) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      const p1 = Authority.getAuthorityMenu(this.formItem.menuName);\r\n      const p2 = Authority.getAuthorityForRole(this.formItem.id);\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0 && res1.data) {\r\n          let opt = {primaryKey: \"id\", parentKey: \"parentId\", startPid: \"0\",};\r\n          if (res2.code === 0 && res2.data && res2.data.length > 0) {\r\n            let menus = [];\r\n            let actions = [];\r\n            res2.data.map((item) => {\r\n              // 菜单权限\r\n              if (item.authority.indexOf(\"MENU_\") !== -1 && !menus.includes(item.authorityId)) {\r\n                menus.push(item.authorityId);\r\n              }\r\n              // 操作权限\r\n              if (item.authority.indexOf(\"ACTION_\") !== -1 && !actions.includes(item.authorityId)) {\r\n                actions.push(item.authorityId);\r\n              }\r\n            });\r\n            that.formItem.grantMenus = menus;\r\n            that.formItem.grantActions = actions;\r\n          }\r\n          res1.data.map((item) => {\r\n            // 菜单选中\r\n            if (that.formItem.grantMenus.includes(item.authorityId)) {\r\n              item._isChecked = true;\r\n            }\r\n          });\r\n          if(that.formItem.menuName){\r\n            opt.startPid = Math.min.apply(Math, res1.data.map(function(item) {return item[opt.parentKey]}))+\"\";\r\n          }\r\n\r\n          that.selectMenus = listConvertTree(res1.data, opt);\r\n        }\r\n      }).finally(() => this.loadTree = false);\r\n    },\r\n    // id为角色ID\r\n    handleLoadRoleUsers(id) {\r\n      if (!id) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      Role.getRoleUsers(id).then((res) => {\r\n        if (res['code'] === 0) {\r\n          let userIds = [];\r\n          res.data.forEach((item) => userIds.push(item.id));\r\n          that.formItem.userIds = userIds;\r\n        }\r\n      })\r\n    }\r\n  },\r\n  mounted: function () {\r\n    this.handleSearch();\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AA4GA,OAAAA,IAAA;AACA,OAAAC,MAAA;AACA,OAAAC,SAAA;AACA,SAAAC,eAAA;AACA,OAAAC,cAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH,cAAA,EAAAA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA,IAAAC,YAAA,YAAAA,aAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA;MACA,IAAAF,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA,WAAAH,KAAA,YAAAE,GAAA,CAAAE,IAAA,CAAAJ,KAAA;QACAC,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA;MACAP,eAAA,EAAAA,eAAA;MACAW,SAAA,EAAAf,MAAA,CAAAe,SAAA;MACAC,OAAA;MACAC,YAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACAC,MAAA;MACAC,OAAA;MACAC,KAAA,GACA,SACA,SACA,QACA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,eAAA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,aAAA;QACAF,QAAA;UAAAG,QAAA;UAAAC,SAAA,EAAA9B,YAAA;UAAA+B,OAAA;QAAA;QACAJ,QAAA,GACA;UAAAE,QAAA;UAAAG,OAAA;UAAAD,OAAA;QAAA;MAEA;MACAE,QAAA;QACAC,EAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;MACA;MACAC,OAAA,GACA;QACAC,IAAA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAC,KAAA;QACAC,GAAA;QACAH,KAAA;MACA,GACA;QACAE,KAAA;QACAC,GAAA;QACAH,KAAA;MACA,GACA;QACAE,KAAA;QACAE,IAAA;QACAD,GAAA;QACAH,KAAA;MACA,GACA;QACAE,KAAA;QACAC,GAAA;QACAH,KAAA;MACA,GACA;QACAE,KAAA;QACAC,GAAA;MACA,GACA;QACAD,KAAA;QACAE,IAAA;QACAC,KAAA;QACAL,KAAA;MACA,EACA;MACAM,WAAA,GACA;QACAJ,KAAA;QACAC,GAAA;QACAI,QAAA;MACA,GACA;QACAL,KAAA;QACAH,IAAA;QACAS,QAAA;QACAD,QAAA;MACA,EACA;MACAnD,IAAA;IACA;EACA;EACAqD,OAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAC,qBAAA,MAAArB,QAAA,CAAAC,EAAA;IACA;IACAqB,eAAA,WAAAA,gBAAA;MACA,KAAAtB,QAAA,CAAAM,QAAA;MACA,KAAAe,qBAAA,MAAArB,QAAA,CAAAC,EAAA;IACA;IACAsB,cAAA,WAAAA,eAAAtB,EAAA;MACA,SAAAuB,aAAA;QACA,KAAAC,QAAA;MACA;QACA,KAAAA,QAAA,SAAAA,QAAA;MACA;MACA,KAAAD,aAAA;MAEA,IAAAE,OAAA;MACA,KAAAxC,WAAA,CAAAyC,GAAA,WAAAC,IAAA;QACA,IAAA3B,EAAA,KAAA2B,IAAA,CAAAC,QAAA;UACAH,OAAA,CAAAI,IAAA,CAAAF,IAAA,CAAA3B,EAAA;QACA;MACA;MACA,SAAAwB,QAAA;QACA,KAAAM,aAAA,GAAAL,OAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAM,aAAA;QACA,KAAAN,QAAA;MACA;IACA;IACAO,WAAA,WAAAA,YAAAlE,IAAA,EAAA2C,IAAA;MACA,KAAAA,IAAA,UAAA/B,SAAA;QACA;QACA;MACA;MACA,IAAAZ,IAAA;QACA,IAAA2C,IAAA;UACA,KAAA/B,SAAA;UACA,KAAAE,SAAA,GAAAd,IAAA,CAAA4B,QAAA;QACA;QACA,IAAAe,IAAA;UACA,KAAA/B,SAAA;UACA,KAAAE,SAAA,GAAAd,IAAA,CAAA4B,QAAA;QACA;QACA,KAAAM,QAAA,GAAAiC,MAAA,CAAAC,MAAA,UAAAlC,QAAA,EAAAlC,IAAA;MACA,WAAA2C,IAAA;QACA,KAAA/B,SAAA;QACA,KAAAE,SAAA;MACA;MACA;MACA,SAAAE,OAAA,UAAAC,KAAA;QACA,SAAAL,SAAA;UACA,KAAAD,UAAA,oBAAAG,SAAA;QACA;QACA,SAAAF,SAAA;UACA,KAAAD,UAAA,oBAAAG,SAAA;QACA;QACA,SAAAF,SAAA;UACA,KAAAD,UAAA;UACA,KAAA0D,KAAA,UAAAC,WAAA;QACA;MACA;MACA;MACA,SAAAtD,OAAA,UAAAC,KAAA,aAAAL,SAAA,oBAAAA,SAAA;QACA,KAAAD,UAAA,oBAAAG,SAAA;QACA,KAAAyC,qBAAA,MAAArB,QAAA,CAAAC,EAAA;MACA;MACA;MACA,SAAAnB,OAAA,UAAAC,KAAA,aAAAL,SAAA,oBAAAA,SAAA;QACA,KAAAD,UAAA,oBAAAG,SAAA;QACA,KAAAyD,mBAAA,MAAArC,QAAA,CAAAC,EAAA;MACA;MACA,KAAAzB,YAAA;IACA;IACA8D,eAAA,WAAAA,gBAAAC,IAAA;MACA,KAAAJ,KAAA,CAAAI,IAAA,EAAAH,WAAA;MACA,IAAAG,IAAA;QACA,KAAAC,YAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA7E,IAAA;MACA,KAAAkB,OAAA,GAAAlB,IAAA;MACA,KAAAoE,WAAA;IACA;IACAU,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAA3C,QAAA;QACAC,EAAA;QACAR,QAAA;QACAC,QAAA;QACAQ,MAAA;QACAI,QAAA;QACAH,QAAA;MACA;MACA;MACA,KAAA3B,YAAA;MACA,KAAAO,KAAA,CAAA4C,GAAA,WAAAY,IAAA;QACAI,KAAA,CAAAL,eAAA,CAAAC,IAAA;MACA;MACA,KAAAzD,OAAA,QAAAC,KAAA;MACA,KAAAiB,QAAA,CAAAO,OAAA;MACA,KAAAP,QAAA,CAAAI,UAAA;MACA,KAAAJ,QAAA,CAAAK,YAAA;MACA,KAAAxB,MAAA;MACA,IAAA+D,eAAA,QAAAT,KAAA,CAAAS,eAAA;MACAA,eAAA,CAAAC,eAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAjE,OAAA,UAAAC,KAAA;QACA,KAAAoD,KAAA,MAAArD,OAAA,EAAAkE,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAF,MAAA,CAAAlE,MAAA;YACA,IAAAkE,MAAA,CAAA/C,QAAA,CAAAC,EAAA;cACA3C,IAAA,CAAA4F,IAAA,CAAAH,MAAA,CAAA/C,QAAA,EACAmD,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA;kBACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;kBACAP,MAAA,CAAAL,WAAA;gBACA;gBACAK,MAAA,CAAAP,YAAA;cACA,GACAe,OAAA;gBACAR,MAAA,CAAAlE,MAAA;cACA;YACA;cACAvB,IAAA,CAAAkG,GAAA,CAAAT,MAAA,CAAA/C,QAAA,EACAmD,IAAA,WAAAC,GAAA;gBACA,IAAAA,GAAA;kBACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;kBACAP,MAAA,CAAAL,WAAA;gBACA;gBACAK,MAAA,CAAAP,YAAA;cACA,GACAe,OAAA;gBACAR,MAAA,CAAAlE,MAAA;cACA;YACA;UACA;QACA;MACA;MAEA,SAAAC,OAAA,UAAAC,KAAA;QACA,KAAAoD,KAAA,MAAArD,OAAA,EAAAkE,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACA,IAAAQ,YAAA,GAAAV,MAAA,CAAAW,qBAAA;YAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAb,MAAA,CAAA/D,WAAA;cAAA6E,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;gBAAA,IAAAC,KAAA,GAAAJ,KAAA,CAAA5F,KAAA;gBACA,IAAAiG,WAAA,GAAAD,KAAA,CAAAE,QAAA,GAAAF,KAAA,CAAAE,QAAA,CAAAxC,GAAA,WAAAyC,CAAA;kBAAA,OAAAA,CAAA,CAAAC,WAAA;gBAAA;gBACA,IAAAC,IAAA;gBAAA,IAAAC,UAAA,GAAAX,0BAAA,CACAM,WAAA;kBAAAM,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAR,CAAA,IAAAC,IAAA;oBAAA,IAAAS,KAAA,GAAAD,MAAA,CAAAvG,KAAA;oBACA,IAAAwF,YAAA,CAAAiB,QAAA,CAAAD,KAAA;sBACAH,IAAA;sBACA;oBACA;kBACA;gBAAA,SAAAK,GAAA;kBAAAJ,UAAA,CAAAK,CAAA,CAAAD,GAAA;gBAAA;kBAAAJ,UAAA,CAAAM,CAAA;gBAAA;gBACA,IAAAP,IAAA,cAAAb,YAAA,CAAAiB,QAAA,CAAAT,KAAA,CAAAI,WAAA;kBACAZ,YAAA,CAAA3B,IAAA,CAAAmC,KAAA,CAAAI,WAAA;gBACA;cACA;YAAA,SAAAM,GAAA;cAAAhB,SAAA,CAAAiB,CAAA,CAAAD,GAAA;YAAA;cAAAhB,SAAA,CAAAkB,CAAA;YAAA;YACA9B,MAAA,CAAAlE,MAAA;YACArB,SAAA,CAAAsH,qBAAA;cACAC,MAAA,EAAAhC,MAAA,CAAA/C,QAAA,CAAAC,EAAA;cACAwD,YAAA,EAAAA;YACA,GAAAN,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;gBACAP,MAAA,CAAAvE,YAAA;cACA;YACA,GAAA+E,OAAA;cACAR,MAAA,CAAAlE,MAAA;YACA;UACA;QACA;MACA;MAEA,SAAAC,OAAA,UAAAC,KAAA;QACA,KAAAF,MAAA;QACAvB,IAAA,CAAA0H,YAAA;UACAD,MAAA,OAAA/E,QAAA,CAAAC,EAAA;UACAM,OAAA,OAAAP,QAAA,CAAAO;QACA,GAAA4C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAL,MAAA,CAAAM,QAAA,CAAAC,OAAA;YACAP,MAAA,CAAAvE,YAAA;YACAuE,MAAA,CAAAL,WAAA;UACA;QACA,GAAAa,OAAA;UACAR,MAAA,CAAAlE,MAAA;QACA;MACA;IACA;IACA2D,YAAA,WAAAA,aAAAjD,IAAA;MAAA,IAAA0F,MAAA;MACA,IAAA1F,IAAA;QACA,KAAAF,QAAA,CAAAE,IAAA,GAAAA,IAAA;MACA;MACA,KAAAhB,OAAA;MACAjB,IAAA,CAAA4H,QAAA,MAAA7F,QAAA,EAAA8D,IAAA,WAAAC,GAAA;QACA6B,MAAA,CAAAnH,IAAA,GAAAsF,GAAA,CAAAtF,IAAA,CAAAqH,OAAA;QACAF,MAAA,CAAA5F,QAAA,CAAAC,KAAA,GAAA8F,QAAA,CAAAhC,GAAA,CAAAtF,IAAA,CAAAwB,KAAA;MACA,GAAAiE,OAAA;QACA0B,MAAA,CAAA1G,OAAA;MACA;IACA;IACA8G,UAAA,WAAAA,WAAAvG,OAAA;MACA,KAAAO,QAAA,CAAAE,IAAA,GAAAT,OAAA;MACA,KAAA0D,YAAA;IACA;IACA8C,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAlG,QAAA,CAAAG,KAAA,GAAA+F,IAAA;MACA,KAAA/C,YAAA;IACA;IACAgD,YAAA,WAAAA,aAAA1H,IAAA;MAAA,IAAA2H,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACA/E,KAAA;QACAgF,IAAA,WAAAA,KAAA;UACAtI,IAAA,CAAAuI,MAAA,CAAA/H,IAAA,CAAAmC,EAAA,EAAAkD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAqC,MAAA,CAAApG,QAAA,CAAAE,IAAA;cACAkG,MAAA,CAAApC,QAAA,CAAAC,OAAA;YACA;YACAmC,MAAA,CAAAjD,YAAA;UACA;QACA;MACA;IACA;IACAkB,qBAAA,WAAAA,sBAAA;MACA,IAAAoC,KAAA,QAAA3D,KAAA,SAAA4D,cAAA;MACA,OAAAD,KAAA,CAAAE,MAAA,MAAAhG,QAAA,CAAAK,YAAA;IACA;IACAgB,qBAAA,WAAAA,sBAAA;MAAA,IAAA4E,MAAA;MACA,KAAAtH,QAAA;MACA,UAAAqB,QAAA,CAAAC,EAAA;QACA;MACA;MACA,IAAAiG,IAAA;MACA,IAAAC,EAAA,GAAA3I,SAAA,CAAA4I,gBAAA,MAAApG,QAAA,CAAAM,QAAA;MACA,IAAA+F,EAAA,GAAA7I,SAAA,CAAA8I,mBAAA,MAAAtG,QAAA,CAAAC,EAAA;MACAsG,OAAA,CAAAC,GAAA,EAAAL,EAAA,EAAAE,EAAA,GAAAlD,IAAA,WAAAsD,MAAA;QACA,IAAAC,IAAA,GAAAD,MAAA;QACA,IAAAE,IAAA,GAAAF,MAAA;QACA,IAAAC,IAAA,CAAAE,IAAA,UAAAF,IAAA,CAAA5I,IAAA;UACA,IAAA+I,GAAA;YAAAC,UAAA;YAAAC,SAAA;YAAAC,QAAA;UAAA;UACA,IAAAL,IAAA,CAAAC,IAAA,UAAAD,IAAA,CAAA7I,IAAA,IAAA6I,IAAA,CAAA7I,IAAA,CAAAmJ,MAAA;YACA,IAAAnB,KAAA;YACA,IAAAoB,OAAA;YACAP,IAAA,CAAA7I,IAAA,CAAA6D,GAAA,WAAAC,IAAA;cACA;cACA,IAAAA,IAAA,CAAAuF,SAAA,CAAAC,OAAA,qBAAAtB,KAAA,CAAApB,QAAA,CAAA9C,IAAA,CAAAyC,WAAA;gBACAyB,KAAA,CAAAhE,IAAA,CAAAF,IAAA,CAAAyC,WAAA;cACA;cACA;cACA,IAAAzC,IAAA,CAAAuF,SAAA,CAAAC,OAAA,uBAAAF,OAAA,CAAAxC,QAAA,CAAA9C,IAAA,CAAAyC,WAAA;gBACA6C,OAAA,CAAApF,IAAA,CAAAF,IAAA,CAAAyC,WAAA;cACA;YACA;YACA6B,IAAA,CAAAlG,QAAA,CAAAI,UAAA,GAAA0F,KAAA;YACAI,IAAA,CAAAlG,QAAA,CAAAK,YAAA,GAAA6G,OAAA;UACA;UACAR,IAAA,CAAA5I,IAAA,CAAA6D,GAAA,WAAAC,IAAA;YACA;YACA,IAAAsE,IAAA,CAAAlG,QAAA,CAAAI,UAAA,CAAAsE,QAAA,CAAA9C,IAAA,CAAAyC,WAAA;cACAzC,IAAA,CAAAyF,UAAA;YACA;UACA;UACA,IAAAnB,IAAA,CAAAlG,QAAA,CAAAM,QAAA;YACAuG,GAAA,CAAAG,QAAA,GAAAM,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,EAAAZ,IAAA,CAAA5I,IAAA,CAAA6D,GAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAiF,GAAA,CAAAE,SAAA;YAAA;UACA;UAEAb,IAAA,CAAAlH,WAAA,GAAAvB,eAAA,CAAAiJ,IAAA,CAAA5I,IAAA,EAAA+I,GAAA;QACA;MACA,GAAAtD,OAAA;QAAA,OAAA0C,MAAA,CAAAtH,QAAA;MAAA;IACA;IACA;IACA0D,mBAAA,WAAAA,oBAAApC,EAAA;MACA,KAAAA,EAAA;QACA;MACA;MACA,IAAAiG,IAAA;MACA5I,IAAA,CAAAmK,YAAA,CAAAxH,EAAA,EAAAkD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACA,IAAA7C,OAAA;UACA6C,GAAA,CAAAtF,IAAA,CAAA4J,OAAA,WAAA9F,IAAA;YAAA,OAAArB,OAAA,CAAAuB,IAAA,CAAAF,IAAA,CAAA3B,EAAA;UAAA;UACAiG,IAAA,CAAAlG,QAAA,CAAAO,OAAA,GAAAA,OAAA;QACA;MACA;IACA;EACA;EACAoH,OAAA,WAAAA,QAAA;IACA,KAAAnF,YAAA;EACA;AACA"}]}