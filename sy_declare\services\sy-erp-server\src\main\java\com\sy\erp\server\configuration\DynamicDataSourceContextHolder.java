package com.sy.erp.server.configuration;

public class DynamicDataSourceContextHolder {

    public DynamicDataSourceContextHolder(){
        holder.set(syMysql);
    }
    /**
     * 备注见DataSourceEnum
     */
    private static final String aimoProdMysql = DataSourceEnum.AimoProdMysql.getValue();
    private static final String aimoTestMysql = DataSourceEnum.AimoTestMysql.getValue();
    /**
     * 当前主数据库配置
     */
    public static final String syMysql = DataSourceEnum.AimoTestMysql.getValue();
    public static final ThreadLocal<String> holder = new ThreadLocal<>();

    public static void markAimoProdMysql() {
        holder.set(aimoProdMysql);
    }
    public static void markAimoTestMysql() {
        holder.set(aimoTestMysql);
    }
    public static void markDbServer(String dateType) {
        try{
            DataSourceEnum dataSource = DataSourceEnum.byValue(dateType);
            holder.set(dataSource.getValue());
        }catch (Exception e){
            reset();
        }
    }
    /**
     * 重置  aimoTestMysql
     */
    public static void reset() {
        holder.set(syMysql);
    }


    public static String getDataSourceType() {
        return holder.get();
    }

}
