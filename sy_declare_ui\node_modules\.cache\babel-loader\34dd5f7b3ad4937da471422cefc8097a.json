{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\dictionary.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\base\\dictionary.js", "mtime": 1752737748398}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "dictionaryPath", "listPage", "params", "url", "method", "listValuePage", "add", "data", "addValue", "edit", "editValue", "remove", "id", "removeValue"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/base/dictionary.js"], "sourcesContent": ["import request from '@/libs/request'\r\nconst dictionaryPath = \"/base/dictionary\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: dictionaryPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 获取字典值分页数据\r\n */\r\nconst listValuePage=(params) => {\r\n  return request({\r\n    url: dictionaryPath + '/listValuePage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n/**\r\n * 添加数据\r\n */\r\nconst add = (data) => {\r\n  return request({\r\n    url: dictionaryPath + '/addDictionary',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加字典值\r\n */\r\nconst addValue = (data) => {\r\n  return request({\r\n    url: dictionaryPath + '/addDictionaryValue',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n/**\r\n * 更新数据\r\n */\r\nconst edit = (data) => {\r\n\r\n  return request({\r\n    url: dictionaryPath + '/editDictionary',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 更新字典值\r\n */\r\nconst editValue = (data) => {\r\n\r\n  return request({\r\n    url: dictionaryPath + '/editDictionaryValue',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 删除数据\r\n */\r\nconst remove = (id) => {\r\n  const data = {\r\n    id: id\r\n  }\r\n  return request({\r\n    url: dictionaryPath + '/removeDictionary',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 删除字典值\r\n */\r\nconst removeValue = (id) => {\r\n  const data = {\r\n    id: id\r\n  }\r\n  return request({\r\n    url: dictionaryPath + '/removeDictionaryValue',\r\n    data,\r\n    method: 'post'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  listValuePage,\r\n  remove,\r\n  removeValue,\r\n  add,\r\n  addValue,\r\n  edit,\r\n  editValue\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,IAAMC,cAAc,GAAG,kBAAkB;AACzC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,WAAW;IACjCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,aAAa,GAAC,SAAdA,aAAaA,CAAEH,MAAM,EAAK;EAC9B,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,gBAAgB;IACtCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAME,GAAG,GAAG,SAANA,GAAGA,CAAIC,IAAI,EAAK;EACpB,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,gBAAgB;IACtCO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMI,QAAQ,GAAG,SAAXA,QAAQA,CAAID,IAAI,EAAK;EACzB,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,qBAAqB;IAC3CO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAMK,IAAI,GAAG,SAAPA,IAAIA,CAAIF,IAAI,EAAK;EAErB,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,iBAAiB;IACvCO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMM,SAAS,GAAG,SAAZA,SAASA,CAAIH,IAAI,EAAK;EAE1B,OAAOR,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,sBAAsB;IAC5CO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMO,MAAM,GAAG,SAATA,MAAMA,CAAIC,EAAE,EAAK;EACrB,IAAML,IAAI,GAAG;IACXK,EAAE,EAAEA;EACN,CAAC;EACD,OAAOb,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,mBAAmB;IACzCO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMS,WAAW,GAAG,SAAdA,WAAWA,CAAID,EAAE,EAAK;EAC1B,IAAML,IAAI,GAAG;IACXK,EAAE,EAAEA;EACN,CAAC;EACD,OAAOb,OAAO,CAAC;IACbI,GAAG,EAAEH,cAAc,GAAG,wBAAwB;IAC9CO,IAAI,EAAJA,IAAI;IACJH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,aAAa,EAAbA,aAAa;EACbM,MAAM,EAANA,MAAM;EACNE,WAAW,EAAXA,WAAW;EACXP,GAAG,EAAHA,GAAG;EACHE,QAAQ,EAARA,QAAQ;EACRC,IAAI,EAAJA,IAAI;EACJC,SAAS,EAATA;AACF,CAAC"}]}