{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\menu-action.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\menus\\menu-action.vue", "mtime": 1752737748512}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50ZXN0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBBY3Rpb24gZnJvbSAnQC9hcGkvYmFzZS9hY3Rpb24nOwppbXBvcnQgQXV0aG9yaXR5IGZyb20gJ0AvYXBpL3N5c3RlbS9hdXRob3JpdHlfMSc7CmltcG9ydCBDb21tb24gZnJvbSAiQC9hcGkvYmFzaWMvY29tbW9uIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNZW51QWN0aW9uJywKICBwcm9wczogewogICAgdmFsdWU6IE9iamVjdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBoID0gdGhpcy4kY3JlYXRlRWxlbWVudDsKICAgIHZhciB2YWxpZGF0ZUVuID0gZnVuY3Rpb24gdmFsaWRhdGVFbihydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgdmFyIHJlZyA9IC9eW19hLXpBLVowLTldKyQvOwogICAgICBpZiAodmFsdWUgPT09ICcnKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflip/og73moIfor4bkuI3og73kuLrnqbonKSk7CiAgICAgIH0gZWxzZSBpZiAodmFsdWUgIT09ICcnICYmICFyZWcudGVzdCh2YWx1ZSkpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+WPquWFgeiuuOWtl+avjeOAgeaVsOWtl+OAgeS4i+WIkue6vycpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgc3RhdHVzT3BzOiBDb21tb24uc3RhdHVzT3BzLAogICAgICBtb2RhbFZpc2libGU6IGZhbHNlLAogICAgICBzYXZpbmc6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgY3VycmVudDogJ2Zvcm0xJywKICAgICAgZm9ybXM6IFsnZm9ybTEnLCAnZm9ybTInXSwKICAgICAgbW9kYWxUaXRsZTogJycsCiAgICAgIGNvbmZpcm1Nb2RhbDogZmFsc2UsCiAgICAgIHNlbGVjdEFwaXM6IFtdLAogICAgICBmb3JtSXRlbVJ1bGVzOiB7CiAgICAgICAgYWN0aW9uQ29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZUVuLAogICAgICAgICAgbWVzc2FnZTogJ+WKn+iDvee8lueggeS4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBhY3Rpb25OYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5Yqf6IO95ZCN56ew5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGZvcm1JdGVtOiB7CiAgICAgICAgaWQ6ICcnLAogICAgICAgIGFjdGlvbkNvZGU6ICcnLAogICAgICAgIGFjdGlvbk5hbWU6ICcnLAogICAgICAgIGF1dGhvcml0eUlkczogW10sCiAgICAgICAgc3RhdHVzOiAwLAogICAgICAgIG1lbnVJZDogJycsCiAgICAgICAgcHJpb3JpdHk6IDAsCiAgICAgICAgYWN0aW9uRGVzYzogJycKICAgICAgfSwKICAgICAgY29sdW1uczogW3sKICAgICAgICB0aXRsZTogJ+WKn+iDveWQjeensCcsCiAgICAgICAgc2xvdDogJ3N0YXR1cycsCiAgICAgICAgd2lkdGg6IDE1MAogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICflip/og73nvJbnoIEnLAogICAgICAgIGtleTogJ2FjdGlvbkNvZGUnLAogICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihfLCBfcmVmKSB7CiAgICAgICAgICB2YXIgcm93ID0gX3JlZi5yb3c7CiAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgewogICAgICAgICAgICAiZGlyZWN0aXZlcyI6IFt7CiAgICAgICAgICAgICAgbmFtZTogImNvcHl0ZXh0IiwKICAgICAgICAgICAgICB2YWx1ZTogcm93LmFjdGlvbkNvZGUKICAgICAgICAgICAgfV0sCiAgICAgICAgICAgICJzdHlsZSI6ICJ0ZXh0LWFsaWduOmxlZnQiCiAgICAgICAgICB9LCBbcm93LmFjdGlvbkNvZGVdKTsKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+aTjeS9nCcsCiAgICAgICAgc2xvdDogJ2FjdGlvbicsCiAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgIHdpZHRoOiAxNTAKICAgICAgfV0sCiAgICAgIGRhdGE6IFtdCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgaGFuZGxlTW9kYWw6IGZ1bmN0aW9uIGhhbmRsZU1vZGFsKGRhdGEsIHN0ZXApIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB0aGlzLmZvcm1JdGVtID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5mb3JtSXRlbSwgZGF0YSk7CiAgICAgIH0KICAgICAgaWYgKCFzdGVwKSB7CiAgICAgICAgc3RlcCA9IHRoaXMuZm9ybXNbMF07CiAgICAgIH0KICAgICAgaWYgKHN0ZXAgPT09IHRoaXMuZm9ybXNbMF0pIHsKICAgICAgICB0aGlzLm1vZGFsVGl0bGUgPSBkYXRhID8gJ+e8lui+keWKn+iDvSAtICcgKyB0aGlzLnZhbHVlLm1lbnVOYW1lICsgJyA+ICcgKyBkYXRhLmFjdGlvbk5hbWUgOiAn5re75Yqg5Yqf6IO9IC0gJyArIHRoaXMudmFsdWUubWVudU5hbWU7CiAgICAgICAgdGhpcy5tb2RhbFZpc2libGUgPSB0cnVlOwogICAgICAgIHRoaXMuZm9ybUl0ZW0uYWN0aW9uQ29kZSA9IHRoaXMuZm9ybUl0ZW0uaWQgPyB0aGlzLmZvcm1JdGVtLmFjdGlvbkNvZGUgOiB0aGlzLnZhbHVlLm1lbnVDb2RlOwogICAgICB9CiAgICAgIGlmIChzdGVwID09PSB0aGlzLmZvcm1zWzFdKSB7CiAgICAgICAgdGhpcy5tb2RhbFRpdGxlID0gZGF0YSA/ICfmjqXlj6PmjojmnYMgLSAnICsgdGhpcy52YWx1ZS5tZW51TmFtZSArICcgPiAnICsgZGF0YS5hY3Rpb25OYW1lIDogJ+aOpeWPo+aOiOadgyc7CiAgICAgICAgdGhpcy5oYW5kbGVMb2FkQWN0aW9uQXBpKHRoaXMuZm9ybUl0ZW0uaWQpOwogICAgICB9CiAgICAgIHRoaXMuY3VycmVudCA9IHN0ZXA7CiAgICB9LAogICAgaGFuZGxlUmVzZXQ6IGZ1bmN0aW9uIGhhbmRsZVJlc2V0KCkgewogICAgICB0aGlzLmZvcm1JdGVtID0gewogICAgICAgIGlkOiAnJywKICAgICAgICBhY3Rpb25Db2RlOiAnJywKICAgICAgICBhY3Rpb25OYW1lOiAnJywKICAgICAgICBhdXRob3JpdHlJZHM6IFtdLAogICAgICAgIHN0YXR1czogMCwKICAgICAgICBwcmlvcml0eTogMCwKICAgICAgICBhY3Rpb25EZXNjOiAnJwogICAgICB9OwogICAgICAvL+mHjee9rumqjOivgQogICAgICAvLyB0aGlzLmZvcm1zLm1hcChmb3JtID0+IHsKICAgICAgLy8gICB0aGlzLiRyZWZzW2Zvcm1dLnJlc2V0RmllbGRzKCkKICAgICAgLy8gfSkKICAgICAgLy8gdGhpcy5jdXJyZW50ID0gdGhpcy5mb3Jtc1swXQogICAgICB0aGlzLm1vZGFsVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLnNhdmluZyA9IGZhbHNlOwogICAgfSwKICAgIGhhbmRsZVN1Ym1pdDogZnVuY3Rpb24gaGFuZGxlU3VibWl0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBpZiAodGhpcy5jdXJyZW50ID09PSB0aGlzLmZvcm1zWzBdKSB7CiAgICAgICAgdGhpcy4kcmVmc1t0aGlzLmN1cnJlbnRdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgIF90aGlzLnNhdmluZyA9IHRydWU7CiAgICAgICAgICAgIGlmIChfdGhpcy5mb3JtSXRlbS5pZCkgewogICAgICAgICAgICAgIEFjdGlvbi5lZGl0KF90aGlzLmZvcm1JdGVtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgIF90aGlzLmhhbmRsZVJlc2V0KCk7CiAgICAgICAgICAgICAgICBfdGhpcy5oYW5kbGVTZWFyY2goKTsKICAgICAgICAgICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgewogICAgICAgICAgICAgICAgICBfdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KS5maW5hbGx5KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzLnNhdmluZyA9IGZhbHNlOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIEFjdGlvbi5hZGQoX3RoaXMuZm9ybUl0ZW0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgX3RoaXMuaGFuZGxlUmVzZXQoKTsKICAgICAgICAgICAgICAgIF90aGlzLmhhbmRsZVNlYXJjaCgpOwogICAgICAgICAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgX3RoaXMuc2F2aW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgICBpZiAodGhpcy5jdXJyZW50ID09PSB0aGlzLmZvcm1zWzFdKSB7CiAgICAgICAgdGhpcy4kcmVmc1t0aGlzLmN1cnJlbnRdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgIF90aGlzLnNhdmluZyA9IHRydWU7CiAgICAgICAgICAgIEF1dGhvcml0eS5ncmFudEF1dGhvcml0eUZvckFjdGlvbih7CiAgICAgICAgICAgICAgYWN0aW9uSWQ6IF90aGlzLmZvcm1JdGVtLmlkLAogICAgICAgICAgICAgIGF1dGhvcml0eUlkczogX3RoaXMuZm9ybUl0ZW0uYXV0aG9yaXR5SWRzCiAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIF90aGlzLmhhbmRsZVJlc2V0KCk7CiAgICAgICAgICAgICAgX3RoaXMuaGFuZGxlU2VhcmNoKCk7CiAgICAgICAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7CiAgICAgICAgICAgICAgICBfdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfnu5HlrprmiJDlip8nKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIF90aGlzLnNhdmluZyA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZVNlYXJjaDogZnVuY3Rpb24gaGFuZGxlU2VhcmNoKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgaWYgKCF0aGlzLnZhbHVlIHx8ICF0aGlzLnZhbHVlLmlkKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZm9ybUl0ZW0ubWVudUlkID0gdGhpcy52YWx1ZS5pZDsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgQWN0aW9uLmxpc3RBY3Rpb24odGhpcy5mb3JtSXRlbS5tZW51SWQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi5kYXRhID0gcmVzLmRhdGE7CiAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVJlbW92ZTogZnVuY3Rpb24gaGFuZGxlUmVtb3ZlKGRhdGEpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oewogICAgICAgIHRpdGxlOiAn56Gu5a6a5Yig6Zmk5ZCX77yfJywKICAgICAgICBvbk9rOiBmdW5jdGlvbiBvbk9rKCkgewogICAgICAgICAgQWN0aW9uLnJlbW92ZShkYXRhLmlkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXMzLmhhbmRsZVNlYXJjaCgpOwogICAgICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsKICAgICAgICAgICAgICBfdGhpczMucGFnZUluZm8ucGFnZSA9IDE7CiAgICAgICAgICAgICAgX3RoaXMzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUxvYWRBY3Rpb25BcGk6IGZ1bmN0aW9uIGhhbmRsZUxvYWRBY3Rpb25BcGkoaWQpIHsKICAgICAgaWYgKCFpZCkgewogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIHZhciBwMSA9IEF1dGhvcml0eS5nZXRBbGxBcGkoMCk7CiAgICAgIHZhciBwMiA9IEF1dGhvcml0eS5nZXRBdXRob3JpdHlGb3JBY3Rpb24oaWQpOwogICAgICBQcm9taXNlLmFsbChbcDEsIHAyXSkudGhlbihmdW5jdGlvbiAodmFsdWVzKSB7CiAgICAgICAgdmFyIHJlczEgPSB2YWx1ZXNbMF07CiAgICAgICAgdmFyIHJlczIgPSB2YWx1ZXNbMV07CiAgICAgICAgaWYgKHJlczEuY29kZSA9PT0gMCkgewogICAgICAgICAgcmVzMS5kYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpdGVtLmtleSA9IGl0ZW0uYXV0aG9yaXR5SWQ7CiAgICAgICAgICAgIGl0ZW0ubGFiZWwgPSAiIi5jb25jYXQoaXRlbS5wcmVmaXgucmVwbGFjZSgnLyoqJywgJycpKS5jb25jYXQoaXRlbS5wYXRoLCAiIC0gIikuY29uY2F0KGl0ZW0uYXBpTmFtZSk7CiAgICAgICAgICAgIGl0ZW0uZGlzYWJsZWQgPSBpdGVtLnBhdGggPT09ICcvKionOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LnNlbGVjdEFwaXMgPSByZXMxLmRhdGE7CiAgICAgICAgfQogICAgICAgIGlmIChyZXMyLmNvZGUgPT09IDApIHsKICAgICAgICAgIHZhciByZXN1bHQgPSBbXTsKICAgICAgICAgIHJlczIuZGF0YS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgaWYgKCFyZXN1bHQuaW5jbHVkZXMoaXRlbS5hdXRob3JpdHlJZCkpIHsKICAgICAgICAgICAgICByZXN1bHQucHVzaChpdGVtLmF1dGhvcml0eUlkKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LmZvcm1JdGVtLmF1dGhvcml0eUlkcyA9IHJlc3VsdDsKICAgICAgICB9CiAgICAgICAgdGhhdC5tb2RhbFZpc2libGUgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICB0cmFuc2ZlclJlbmRlcjogZnVuY3Rpb24gdHJhbnNmZXJSZW5kZXIoaXRlbSkgewogICAgICByZXR1cm4gIjxzcGFuICB0aXRsZT1cIiIuY29uY2F0KGl0ZW0ubGFiZWwsICJcIj4iKS5jb25jYXQoaXRlbS5sYWJlbCwgIjwvc3Bhbj4iKTsKICAgIH0sCiAgICBoYW5kbGVUcmFuc2ZlckNoYW5nZTogZnVuY3Rpb24gaGFuZGxlVHJhbnNmZXJDaGFuZ2UobmV3VGFyZ2V0S2V5cykgewogICAgICBpZiAobmV3VGFyZ2V0S2V5cy5pbmRleE9mKCcxJykgIT09IC0xKSB7CiAgICAgICAgdGhpcy5mb3JtSXRlbS5hdXRob3JpdHlJZHMgPSBbJzEnXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm1JdGVtLmF1dGhvcml0eUlkcyA9IG5ld1RhcmdldEtleXM7CiAgICAgIH0KICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUoKSB7CiAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30KfTs="}, {"version": 3, "names": ["Action", "Authority", "Common", "name", "props", "value", "Object", "data", "h", "$createElement", "validateEn", "rule", "callback", "reg", "Error", "test", "statusOps", "modalVisible", "saving", "loading", "current", "forms", "modalTitle", "confirmModal", "selectApis", "formItemRules", "actionCode", "required", "validator", "message", "trigger", "actionName", "formItem", "id", "authorityIds", "status", "menuId", "priority", "actionDesc", "columns", "title", "slot", "width", "key", "align", "render", "_", "_ref", "row", "fixed", "methods", "handleModal", "step", "assign", "menuName", "menuCode", "handleLoadActionApi", "handleReset", "handleSubmit", "_this", "$refs", "validate", "valid", "edit", "then", "res", "handleSearch", "$Message", "success", "finally", "add", "grantAuthorityForAction", "actionId", "_this2", "listAction", "handleRemove", "_this3", "$Modal", "confirm", "onOk", "remove", "pageInfo", "page", "that", "p1", "getAllApi", "p2", "getAuthorityForAction", "Promise", "all", "values", "res1", "res2", "code", "map", "item", "authorityId", "label", "concat", "prefix", "replace", "path", "apiName", "disabled", "result", "includes", "push", "transferRender", "handleTransferChange", "newTargetKeys", "indexOf", "watch", "mounted"], "sources": ["src/view/module/base/menus/menu-action.vue"], "sourcesContent": ["<template>\r\n  <div class=\"menu-action-container\">\r\n    <div class=\"search-con search-con-top\">\r\n      <ButtonGroup>\r\n        <Button\r\n          :disabled=\"!(value.id && value.id!=='0' && !value.hasChild)\"\r\n          class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n          <span>添加功能按钮</span>\r\n        </Button>\r\n      </ButtonGroup>\r\n    </div>\r\n    <Alert type=\"info\" :show-icon=\"true\">请绑定相关接口资源。否则请求网关服务器将提示<code>\"权限不足,拒绝访问!\"</code></Alert>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" :max-height=\"690\">\r\n      <template v-slot:status=\"{ row }\">\r\n        <Badge v-for=\"v in statusOps\" v-if=\"v.key === row.status\"\r\n               :status=\"v.key === 0?'success':'error'\" v-bind:key=\"v.key\"></Badge>\r\n        <span v-copytext=\"row.actionName\">{{row.actionName}}</span>\r\n      </template>\r\n      <template v-slot:action=\"{ row }\">\r\n        <a @click=\"handleModal(row)\"  v-if=\"hasAuthority('menuActionEdit')\">编辑</a> &nbsp;\r\n        <a @click=\"handleModal(row,forms[1])\" v-if=\"hasAuthority('menuActionSet')\">接口权限</a> &nbsp;\r\n        <a @click=\"handleRemove(row)\" v-if=\"hasAuthority('menuActionDel')\">删除</a>\r\n      </template>\r\n    </Table>\r\n    <Modal v-model=\"modalVisible\"\r\n           :title=\"modalTitle\"\r\n           width=\"40\"\r\n           @on-cancel=\"handleReset\">\r\n      <div>\r\n        <Form ref=\"form1\" v-show=\"current==='form1'\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n          <FormItem label=\"上级菜单\">\r\n            <Input :disabled=\"true\" v-model=\"value.menuName\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"功能标识\" prop=\"actionCode\">\r\n            <Input v-model=\"formItem.actionCode\" placeholder=\"请输入内容\"></Input>\r\n            <span>菜单标识+自定义标识.默认后缀：View、Edit</span>\r\n          </FormItem>\r\n          <FormItem label=\"功能名称\" prop=\"actionName\">\r\n            <Input v-model=\"formItem.actionName\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n          <FormItem label=\"优先级\">\r\n            <InputNumber v-model=\"formItem.priority\"></InputNumber>\r\n          </FormItem>\r\n          <FormItem label=\"状态\">\r\n            <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n              <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n            </RadioGroup>\r\n          </FormItem>\r\n          <FormItem label=\"描述\">\r\n            <Input v-model=\"formItem.actionDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <Form ref=\"form2\" v-show=\"current==='form2'\" :model=\"formItem\" :rules=\"formItemRules\">\r\n          <FormItem prop=\"authorities\">\r\n            <Transfer\r\n              :data=\"selectApis\"\r\n              :list-style=\"{width: '45%',height: '480px'}\"\r\n              :titles=\"['选择接口', '已选择接口']\"\r\n              :render-format=\"transferRender\"\r\n              :target-keys=\"formItem.authorityIds\"\r\n              @on-change=\"handleTransferChange\"\r\n              :filterable=\"true\">\r\n            </Transfer>\r\n          </FormItem>\r\n        </Form>\r\n        <div class=\"drawer-footer\">\r\n          <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n          <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\">保存</Button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Action from '@/api/base/action';\r\nimport Authority from '@/api/system/authority_1';\r\nimport Common from \"@/api/basic/common\";\r\nexport default {\r\n    name: 'MenuAction',\r\n    props: {\r\n      value: Object\r\n    },\r\n    data () {\r\n      const validateEn = (rule, value, callback) => {\r\n        let reg = /^[_a-zA-Z0-9]+$/\r\n        if (value === '') {\r\n          callback(new Error('功能标识不能为空'))\r\n        } else if (value !== '' && !reg.test(value)) {\r\n          callback(new Error('只允许字母、数字、下划线'))\r\n        } else {\r\n          callback()\r\n        }\r\n      }\r\n      return {\r\n        statusOps: Common.statusOps,\r\n        modalVisible: false,\r\n        saving: false,\r\n        loading: false,\r\n        current: 'form1',\r\n        forms: [\r\n          'form1',\r\n          'form2'\r\n        ],\r\n        modalTitle: '',\r\n        confirmModal: false,\r\n        selectApis: [],\r\n        formItemRules: {\r\n          actionCode: [\r\n            {required: true, validator: validateEn, message: '功能编码不能为空', trigger: 'blur'}\r\n          ],\r\n          actionName: [\r\n            {required: true, message: '功能名称不能为空', trigger: 'blur'}\r\n          ]\r\n        },\r\n        formItem: {\r\n          id: '',\r\n          actionCode: '',\r\n          actionName: '',\r\n          authorityIds: [],\r\n          status: 0,\r\n          menuId: '',\r\n          priority: 0,\r\n          actionDesc: ''\r\n        },\r\n        columns: [\r\n          {\r\n            title: '功能名称',\r\n            slot: 'status',\r\n            width: 150\r\n          },\r\n          {\r\n            title: '功能编码',\r\n            key: 'actionCode',\r\n            align: 'center',\r\n            render: (_, { row }) => <div v-copytext={row.actionCode} style=\"text-align:left\">{row.actionCode}</div>\r\n          },\r\n          {\r\n            title: '操作',\r\n            slot: 'action',\r\n            fixed: 'right',\r\n            align: 'center',\r\n            width: 150\r\n          }\r\n        ],\r\n        data: []\r\n      }\r\n    },\r\n    methods: {\r\n      handleModal (data, step) {\r\n        if (data) {\r\n          this.formItem = Object.assign({}, this.formItem, data)\r\n        }\r\n        if (!step) {\r\n          step = this.forms[0]\r\n        }\r\n        if (step === this.forms[0]) {\r\n          this.modalTitle = data ? '编辑功能 - ' + this.value.menuName + ' > ' + data.actionName : '添加功能 - ' + this.value.menuName\r\n          this.modalVisible = true\r\n          this.formItem.actionCode = this.formItem.id ? this.formItem.actionCode : this.value.menuCode\r\n        }\r\n        if (step === this.forms[1]) {\r\n          this.modalTitle = data ? '接口授权 - ' + this.value.menuName + ' > ' + data.actionName : '接口授权'\r\n          this.handleLoadActionApi(this.formItem.id)\r\n        }\r\n        this.current = step\r\n      },\r\n      handleReset () {\r\n        this.formItem = {\r\n          id: '',\r\n          actionCode: '',\r\n          actionName: '',\r\n          authorityIds: [],\r\n          status: 0,\r\n          priority: 0,\r\n          actionDesc: ''\r\n        }\r\n        //重置验证\r\n        // this.forms.map(form => {\r\n        //   this.$refs[form].resetFields()\r\n        // })\r\n        // this.current = this.forms[0]\r\n        this.modalVisible = false\r\n        this.saving = false\r\n      },\r\n      handleSubmit () {\r\n        if (this.current === this.forms[0]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              if (this.formItem.id) {\r\n                Action.edit(this.formItem).then(res => {\r\n                  this.handleReset()\r\n                  this.handleSearch()\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              } else {\r\n                Action.add(this.formItem).then(res => {\r\n                  this.handleReset()\r\n                  this.handleSearch()\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success('保存成功')\r\n                  }\r\n                }).finally(() => {\r\n                  this.saving = false\r\n                })\r\n              }\r\n            }\r\n          })\r\n        }\r\n        if (this.current === this.forms[1]) {\r\n          this.$refs[this.current].validate((valid) => {\r\n            if (valid) {\r\n              this.saving = true\r\n              Authority.grantAuthorityForAction({\r\n                actionId: this.formItem.id,\r\n                authorityIds: this.formItem.authorityIds\r\n              }).then(res => {\r\n                this.handleReset()\r\n                this.handleSearch()\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success('绑定成功')\r\n                }\r\n              }).finally(() => {\r\n                this.saving = false\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n      },\r\n      handleSearch () {\r\n        if (!this.value || !this.value.id) {\r\n          return\r\n        }\r\n        this.formItem.menuId = this.value.id\r\n        this.loading = true\r\n        Action.listAction(this.formItem.menuId).then(res => {\r\n          this.data = res.data\r\n        }).finally(() => {\r\n          this.loading = false\r\n        })\r\n      },\r\n      handleRemove (data) {\r\n        this.$Modal.confirm({\r\n          title: '确定删除吗？',\r\n          onOk: () => {\r\n            Action.remove(data.id).then(res => {\r\n              this.handleSearch()\r\n              if (res['code'] === 0) {\r\n                this.pageInfo.page = 1\r\n                this.$Message.success('删除成功')\r\n              }\r\n            })\r\n          }\r\n        })\r\n      },\r\n      handleLoadActionApi (id) {\r\n        if (!id) {\r\n          return\r\n        }\r\n        const that = this\r\n        const p1 = Authority.getAllApi(0)\r\n        const p2 = Authority.getAuthorityForAction(id)\r\n        Promise.all([p1, p2]).then(function (values) {\r\n          let res1 = values[0]\r\n          let res2 = values[1]\r\n          if (res1.code === 0) {\r\n            res1.data.map(item => {\r\n              item.key = item.authorityId\r\n              item.label = `${item.prefix.replace('/**', '')}${item.path} - ${item.apiName}`\r\n              item.disabled = item.path === '/**'\r\n            })\r\n            that.selectApis = res1.data\r\n          }\r\n          if (res2.code === 0) {\r\n            const result = []\r\n            res2.data.map(item => {\r\n              if (!result.includes(item.authorityId)) {\r\n                result.push(item.authorityId)\r\n              }\r\n            })\r\n            that.formItem.authorityIds = result\r\n          }\r\n          that.modalVisible = true\r\n        })\r\n      },\r\n      transferRender (item) {\r\n        return `<span  title=\"${item.label}\">${item.label}</span>`\r\n      },\r\n      handleTransferChange (newTargetKeys) {\r\n        if (newTargetKeys.indexOf('1') !== -1) {\r\n          this.formItem.authorityIds = ['1']\r\n        } else {\r\n          this.formItem.authorityIds = newTargetKeys\r\n        }\r\n      },\r\n    },\r\n    watch: {\r\n      value () {\r\n        this.handleSearch()\r\n      }\r\n    },\r\n    mounted: function () {\r\n    }\r\n  }\r\n</script>\r\n<style lang=\"less\">\r\n.menu-action-container{\r\n  .ivu-table-wrapper {\r\n      .ivu-table-cell{\r\n        padding-left: 8px;\r\n        padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AA2EA,OAAAA,MAAA;AACA,OAAAC,SAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,KAAA,EAAAC;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA,IAAAC,UAAA,YAAAA,WAAAC,IAAA,EAAAN,KAAA,EAAAO,QAAA;MACA,IAAAC,GAAA;MACA,IAAAR,KAAA;QACAO,QAAA,KAAAE,KAAA;MACA,WAAAT,KAAA,YAAAQ,GAAA,CAAAE,IAAA,CAAAV,KAAA;QACAO,QAAA,KAAAE,KAAA;MACA;QACAF,QAAA;MACA;IACA;IACA;MACAI,SAAA,EAAAd,MAAA,CAAAc,SAAA;MACAC,YAAA;MACAC,MAAA;MACAC,OAAA;MACAC,OAAA;MACAC,KAAA,GACA,SACA,QACA;MACAC,UAAA;MACAC,YAAA;MACAC,UAAA;MACAC,aAAA;QACAC,UAAA,GACA;UAAAC,QAAA;UAAAC,SAAA,EAAAlB,UAAA;UAAAmB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,UAAA,GACA;UAAAJ,QAAA;UAAAE,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,QAAA;QACAC,EAAA;QACAP,UAAA;QACAK,UAAA;QACAG,YAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAG,GAAA;QACAC,KAAA;QACAC,MAAA,WAAAA,OAAAC,CAAA,EAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAAxC,CAAA;YAAA;cAAAL,IAAA;cAAAE,KAAA,EAAA2C,GAAA,CAAAtB;YAAA;YAAA;UAAA,IAAAsB,GAAA,CAAAtB,UAAA;QAAA;MACA,GACA;QACAc,KAAA;QACAC,IAAA;QACAQ,KAAA;QACAL,KAAA;QACAF,KAAA;MACA,EACA;MACAnC,IAAA;IACA;EACA;EACA2C,OAAA;IACAC,WAAA,WAAAA,YAAA5C,IAAA,EAAA6C,IAAA;MACA,IAAA7C,IAAA;QACA,KAAAyB,QAAA,GAAA1B,MAAA,CAAA+C,MAAA,UAAArB,QAAA,EAAAzB,IAAA;MACA;MACA,KAAA6C,IAAA;QACAA,IAAA,QAAA/B,KAAA;MACA;MACA,IAAA+B,IAAA,UAAA/B,KAAA;QACA,KAAAC,UAAA,GAAAf,IAAA,oBAAAF,KAAA,CAAAiD,QAAA,WAAA/C,IAAA,CAAAwB,UAAA,oBAAA1B,KAAA,CAAAiD,QAAA;QACA,KAAArC,YAAA;QACA,KAAAe,QAAA,CAAAN,UAAA,QAAAM,QAAA,CAAAC,EAAA,QAAAD,QAAA,CAAAN,UAAA,QAAArB,KAAA,CAAAkD,QAAA;MACA;MACA,IAAAH,IAAA,UAAA/B,KAAA;QACA,KAAAC,UAAA,GAAAf,IAAA,oBAAAF,KAAA,CAAAiD,QAAA,WAAA/C,IAAA,CAAAwB,UAAA;QACA,KAAAyB,mBAAA,MAAAxB,QAAA,CAAAC,EAAA;MACA;MACA,KAAAb,OAAA,GAAAgC,IAAA;IACA;IACAK,WAAA,WAAAA,YAAA;MACA,KAAAzB,QAAA;QACAC,EAAA;QACAP,UAAA;QACAK,UAAA;QACAG,YAAA;QACAC,MAAA;QACAE,QAAA;QACAC,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAArB,YAAA;MACA,KAAAC,MAAA;IACA;IACAwC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,SAAAvC,OAAA,UAAAC,KAAA;QACA,KAAAuC,KAAA,MAAAxC,OAAA,EAAAyC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAH,KAAA,CAAAzC,MAAA;YACA,IAAAyC,KAAA,CAAA3B,QAAA,CAAAC,EAAA;cACAjC,MAAA,CAAA+D,IAAA,CAAAJ,KAAA,CAAA3B,QAAA,EAAAgC,IAAA,WAAAC,GAAA;gBACAN,KAAA,CAAAF,WAAA;gBACAE,KAAA,CAAAO,YAAA;gBACA,IAAAD,GAAA;kBACAN,KAAA,CAAAQ,QAAA,CAAAC,OAAA;gBACA;cACA,GAAAC,OAAA;gBACAV,KAAA,CAAAzC,MAAA;cACA;YACA;cACAlB,MAAA,CAAAsE,GAAA,CAAAX,KAAA,CAAA3B,QAAA,EAAAgC,IAAA,WAAAC,GAAA;gBACAN,KAAA,CAAAF,WAAA;gBACAE,KAAA,CAAAO,YAAA;gBACA,IAAAD,GAAA;kBACAN,KAAA,CAAAQ,QAAA,CAAAC,OAAA;gBACA;cACA,GAAAC,OAAA;gBACAV,KAAA,CAAAzC,MAAA;cACA;YACA;UACA;QACA;MACA;MACA,SAAAE,OAAA,UAAAC,KAAA;QACA,KAAAuC,KAAA,MAAAxC,OAAA,EAAAyC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAH,KAAA,CAAAzC,MAAA;YACAjB,SAAA,CAAAsE,uBAAA;cACAC,QAAA,EAAAb,KAAA,CAAA3B,QAAA,CAAAC,EAAA;cACAC,YAAA,EAAAyB,KAAA,CAAA3B,QAAA,CAAAE;YACA,GAAA8B,IAAA,WAAAC,GAAA;cACAN,KAAA,CAAAF,WAAA;cACAE,KAAA,CAAAO,YAAA;cACA,IAAAD,GAAA;gBACAN,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACA;YACA,GAAAC,OAAA;cACAV,KAAA,CAAAzC,MAAA;YACA;UACA;QACA;MACA;IAEA;IACAgD,YAAA,WAAAA,aAAA;MAAA,IAAAO,MAAA;MACA,UAAApE,KAAA,UAAAA,KAAA,CAAA4B,EAAA;QACA;MACA;MACA,KAAAD,QAAA,CAAAI,MAAA,QAAA/B,KAAA,CAAA4B,EAAA;MACA,KAAAd,OAAA;MACAnB,MAAA,CAAA0E,UAAA,MAAA1C,QAAA,CAAAI,MAAA,EAAA4B,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAAlE,IAAA,GAAA0D,GAAA,CAAA1D,IAAA;MACA,GAAA8D,OAAA;QACAI,MAAA,CAAAtD,OAAA;MACA;IACA;IACAwD,YAAA,WAAAA,aAAApE,IAAA;MAAA,IAAAqE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;QACAtC,KAAA;QACAuC,IAAA,WAAAA,KAAA;UACA/E,MAAA,CAAAgF,MAAA,CAAAzE,IAAA,CAAA0B,EAAA,EAAA+B,IAAA,WAAAC,GAAA;YACAW,MAAA,CAAAV,YAAA;YACA,IAAAD,GAAA;cACAW,MAAA,CAAAK,QAAA,CAAAC,IAAA;cACAN,MAAA,CAAAT,QAAA,CAAAC,OAAA;YACA;UACA;QACA;MACA;IACA;IACAZ,mBAAA,WAAAA,oBAAAvB,EAAA;MACA,KAAAA,EAAA;QACA;MACA;MACA,IAAAkD,IAAA;MACA,IAAAC,EAAA,GAAAnF,SAAA,CAAAoF,SAAA;MACA,IAAAC,EAAA,GAAArF,SAAA,CAAAsF,qBAAA,CAAAtD,EAAA;MACAuD,OAAA,CAAAC,GAAA,EAAAL,EAAA,EAAAE,EAAA,GAAAtB,IAAA,WAAA0B,MAAA;QACA,IAAAC,IAAA,GAAAD,MAAA;QACA,IAAAE,IAAA,GAAAF,MAAA;QACA,IAAAC,IAAA,CAAAE,IAAA;UACAF,IAAA,CAAApF,IAAA,CAAAuF,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAApD,GAAA,GAAAoD,IAAA,CAAAC,WAAA;YACAD,IAAA,CAAAE,KAAA,MAAAC,MAAA,CAAAH,IAAA,CAAAI,MAAA,CAAAC,OAAA,aAAAF,MAAA,CAAAH,IAAA,CAAAM,IAAA,SAAAH,MAAA,CAAAH,IAAA,CAAAO,OAAA;YACAP,IAAA,CAAAQ,QAAA,GAAAR,IAAA,CAAAM,IAAA;UACA;UACAlB,IAAA,CAAA3D,UAAA,GAAAmE,IAAA,CAAApF,IAAA;QACA;QACA,IAAAqF,IAAA,CAAAC,IAAA;UACA,IAAAW,MAAA;UACAZ,IAAA,CAAArF,IAAA,CAAAuF,GAAA,WAAAC,IAAA;YACA,KAAAS,MAAA,CAAAC,QAAA,CAAAV,IAAA,CAAAC,WAAA;cACAQ,MAAA,CAAAE,IAAA,CAAAX,IAAA,CAAAC,WAAA;YACA;UACA;UACAb,IAAA,CAAAnD,QAAA,CAAAE,YAAA,GAAAsE,MAAA;QACA;QACArB,IAAA,CAAAlE,YAAA;MACA;IACA;IACA0F,cAAA,WAAAA,eAAAZ,IAAA;MACA,yBAAAG,MAAA,CAAAH,IAAA,CAAAE,KAAA,SAAAC,MAAA,CAAAH,IAAA,CAAAE,KAAA;IACA;IACAW,oBAAA,WAAAA,qBAAAC,aAAA;MACA,IAAAA,aAAA,CAAAC,OAAA;QACA,KAAA9E,QAAA,CAAAE,YAAA;MACA;QACA,KAAAF,QAAA,CAAAE,YAAA,GAAA2E,aAAA;MACA;IACA;EACA;EACAE,KAAA;IACA1G,KAAA,WAAAA,MAAA;MACA,KAAA6D,YAAA;IACA;EACA;EACA8C,OAAA,WAAAA,QAAA,GACA;AACA"}]}