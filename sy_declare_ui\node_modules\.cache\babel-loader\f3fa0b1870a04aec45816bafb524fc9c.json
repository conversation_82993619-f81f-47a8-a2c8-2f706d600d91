{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\basf\\currency.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\basf\\currency.js", "mtime": 1752737748400}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwp2YXIgY3VycmVuY3lQYXRoID0gIi9iYXNlL2N1cnJlbmN5IjsKdmFyIGdldEFsbCA9IGZ1bmN0aW9uIGdldEFsbCgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IGN1cnJlbmN5UGF0aCArICcvZ2V0QWxsJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfTsKdmFyIGdldEJ5SWQgPSBmdW5jdGlvbiBnZXRCeUlkKGlkKSB7CiAgdmFyIHBhcmFtcyA9IHsKICAgIGlkOiBpZAogIH07CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBjdXJyZW5jeVBhdGggKyAnL2dldEJ5SWQnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBnZXRCeUlkOiBnZXRCeUlkLAogIGdldEFsbDogZ2V0QWxsCn07"}, {"version": 3, "names": ["request", "currencyPath", "getAll", "url", "method", "getById", "id", "params"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/basf/currency.js"], "sourcesContent": ["import request from '@/libs/request'\r\n\r\nconst currencyPath = \"/base/currency\";\r\nconst getAll = () => {\r\n  return request({\r\n    url: currencyPath + '/getAll',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nconst getById = (id) => {\r\n  const params = {\r\n    id: id\r\n  }\r\n  return request({\r\n    url: currencyPath + '/getById',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport default {\r\n  getById,\r\n  getAll\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AAEpC,IAAMC,YAAY,GAAG,gBAAgB;AACrC,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAAS;EACnB,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAEF,YAAY,GAAG,SAAS;IAC7BG,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAIC,EAAE,EAAK;EACtB,IAAMC,MAAM,GAAG;IACbD,EAAE,EAAEA;EACN,CAAC;EACD,OAAON,OAAO,CAAC;IACbG,GAAG,EAAEF,YAAY,GAAG,UAAU;IAC9BM,MAAM,EAANA,MAAM;IACNH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,eAAe;EACbC,OAAO,EAAPA,OAAO;EACPH,MAAM,EAANA;AACF,CAAC"}]}