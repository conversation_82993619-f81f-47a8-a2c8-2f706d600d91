{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue?vue&type=style&index=0&id=62c1a3b4&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\product\\index.vue", "mtime": 1752737748513}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouc2VhcmNoLWNvbi10b3B7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZzogMDsNCiAgLy/moIfnrb7jgIFza3XjgIFhc2lu5pCc57Si6aG5DQogIC5tdWx0aUNsYXNzew0KICAgIC5mbGV4LWh7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgbWFyZ2luLXRvcDoycHg7DQogICAgfQ0KICB9DQp9DQoud2lkdGhDbGFzcyB7DQogIHdpZHRoOiAzNTBweA0KfQ0KLmJ1dHRvbk1hcmdpbnsNCiAgbWFyZ2luLWxlZnQ6MTVweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/base/product", "sourcesContent": ["<!--\r\n@create date 2025-02-07\r\n@desc 产品管理\r\n-->\r\n\r\n<template>\r\n  <div class=\"search-con-top\">\r\n    <Card :shadow=\"true\">\r\n      <div>\r\n        <Form ref=\"searchFormRef\" class=\"searchForm\" :model=\"searchForm\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"code\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品编码(回车分隔)\" @changeValue=\"(values)=>{ multiValuesCode = values || []; }\" ref=\"multipleRefCodeRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleCode=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleCode\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentCode\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownCode\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"name\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品名称(回车分隔)\" @changeValue=\"(values)=>{ multiValuesName = values || []; }\" ref=\"multipleRefNameRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleName=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleName\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentName\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownName\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"spec\" class=\"multiClass\">\r\n            <div class=\"flex-h\">\r\n              <Multiple placeholder=\"请输入产品规格(回车分隔)\" @changeValue=\"(values)=>{ multiValuesSpec = values || []; }\" ref=\"multipleRefSpecRef\" style=\"height:32px;\"></Multiple>\r\n              <Button :visible=\"false\" @click=\"()=>{popVisibleSpec=true;}\">输入</Button>\r\n              <Dropdown trigger=\"custom\" :visible=\"popVisibleSpec\" style=\"margin-left: 3px\" :transfer=\"true\" transfer-class-name=\"orderBillDrop\">\r\n                <template #list>\r\n                  <DropdownMenu class=\"popContentClass\">\r\n                    <Input v-model=\"popContentSpec\" type=\"textarea\" :autosize=\"{minRows: 4,maxRows: 8}\" placeholder=\"请输入内容，回车或逗号分隔\" style=\"width: 260px\"/>\r\n                    <div style=\"text-align: right; padding-top: 3px\">\r\n                      <Button type=\"info\" size=\"small\" @click=\"closeDropdownSpec\">确定</Button>\r\n                    </div>\r\n                  </DropdownMenu>\r\n                </template>\r\n              </Dropdown>\r\n            </div>\r\n          </FormItem>\r\n          <FormItem prop=\"isCombo\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isCombo\" placeholder=\"是否组合品\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem prop=\"isThd\" :clear=\"true\">\r\n            <Select type=\"text\" v-model=\"searchForm.isThd\" placeholder=\"是否同步\" style=\"width:160px\">\r\n              <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n            </Select>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleReset()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div style=\"margin-bottom: 10px;\">\r\n        <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\"\r\n                :format=\"['xls', 'xlsx']\"\r\n                :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button type=\"primary\" >上传文件</Button>\r\n        </Upload></div>\r\n        <Button @click=\"downTemplate\" class=\"buttonMargin\" :loading=\"loading\">下载模板</Button>\r\n        <Button @click=\"handleExport\" class=\"buttonMargin\" :loading=\"loading\">导出</Button>\r\n        <Button @click=\"()=>{syncVisible=true;}\" class=\"buttonMargin\" :loading=\"loading\">同步数据</Button>\r\n        <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n               :data=\"tableData\" :loading=\"loading\" :span-method=\"handleSpan\">\r\n          <template v-slot:isCombo=\"{row}\">\r\n            <Badge v-for=\"v in statusList\" :status=\"v.key === 0?'success':'warning'\" :text=\"v.value\" v-if=\"v.key === row.isCombo\" v-bind:key=\"v.key\"></Badge>\r\n          </template>\r\n        </Table>\r\n        <Page :total=\"searchForm.total\" size=\"small\" :current=\"searchForm.page\" :page-size=\"searchForm.limit\" :show-elevator=\"true\"\r\n              :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      </div>\r\n      <Modal :title=\"'同步产品信息'\" width=\"500px\" @on-cancel=\"()=>{syncVisible=false; $refs['syncFormRef'].resetFields();}\" @on-ok=\"onSync\" :value=\"syncVisible\" >\r\n        <Form ref=\"syncFormRef\" :model=\"syncForm\" :label-width=\"100\">\r\n          <FormItem label=\"销售Sku\" prop=\"codes\">\r\n            <Input v-model=\"syncForm.codes\" type=\"textarea\" placeholder=\"请输入内容,多个以逗号隔开,最多支持10个\"></Input>\r\n          </FormItem>\r\n        </Form>\r\n        <div slot=\"footer\"></div>\r\n        <template v-slot:footer=\"{}\">\r\n          <Button @click=\"()=>{syncVisible=false;$refs['syncFormRef'].resetFields();}\" :disabled=\"syncLoading\">取消</Button>\r\n          <Button type=\"primary\" @click=\"onSync\" :loading=\"syncLoading\" style=\"margin-left: 15px\">确认</Button>\r\n        </template>\r\n      </Modal>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { autoTableHeight} from \"@/libs/tools.js\";\r\nimport { getToken, getUrl } from '@/libs/util';\r\nimport Multiple from \"@/view/module/common/multipleInput.vue\";\r\nimport Product from \"@/api/base/product\";\r\nimport ShopSelect from \"_c/shopSelect/index.vue\";\r\nimport UpcCode from \"@/api/newApply/upcCode\";\r\nexport default {\r\n  name: \"productManage\",\r\n  components: {ShopSelect, Multiple},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      syncVisible:false,\r\n      syncLoading:false,\r\n      syncForm:{\"codes\":null},\r\n      loading: false,\r\n      multiValuesCode:[],\r\n      popVisibleCode:false,\r\n      popContentCode: undefined,\r\n      multiValuesName:[],\r\n      popVisibleName:false,\r\n      popContentName: undefined,\r\n      multiValuesSpec:[],\r\n      popVisibleSpec:false,\r\n      popContentSpec: undefined,\r\n      mergeColumns:['index','code','name','spec','isCombo','syncFlag','updateTime'],\r\n      statusList:[{\"key\":-1,\"value\":\"全部\"},{\"key\":0,\"value\":\"否\"},{\"key\":1,\"value\":\"是\"}],\r\n      importURl: getUrl() + Product.path+ '/importFile',\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      searchForm: {\r\n        isCombo:null,\r\n        isThd:null,\r\n        page:1,\r\n        limit:10\r\n      },\r\n      tableData: [], //表格数据\r\n      columns: [{title: '#', key: 'index',width: 60,align: 'center'},\r\n        {title: '编码',key: 'code',width: 150,resizable:true,render: (_, { row }) => (<span v-copytext={row['code']}>{row['code']}</span>)},\r\n        {title: '类型',key: 'name',minWidth: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['name']}>{row['name']}</span>)},\r\n        {title: '规格',key: 'spec',minWidth: 250,resizable:true,render: (_, { row }) => (<span v-copytext={row['spec']}>{row['spec']}</span>)},\r\n        {title: '是否组合品',key: 'isCombo',width: 120,resizable:true,slot:'isCombo'},\r\n        {title: '是否同步',key: 'syncFlag',width: 120,resizable:true,render: (_, { row }) => (<span v-copytext={row['syncFlag']}>{row['syncFlag']}</span>)},\r\n        {title: '同步时间',key: 'updateTime',width: 180,resizable:true,render:(_, { row }) => (<span v-copytext={row['updateTime']}>{row['updateTime']}</span>)},\r\n        {title: '子编码', align: \"center\",key: 'childSku', width: 120, render: (h, {row}) => (<span v-copytext={row.childSku}>{row.childSku}</span>)},\r\n        {title: '子规格', align: \"center\",key: 'childSpec', minWidth: 200, render: (h, {row}) => (<span v-copytext={row.childSpec}>{row.childSpec}</span>)},\r\n        {title: '子数量', align: \"center\",key: 'itemQuantity', width: 80, render: (h, {row}) => (<span v-copytext={row.itemQuantity}>{row.itemQuantity}</span>)},\r\n      ],\r\n    };\r\n  },\r\n  //组件初始化进行的操作\r\n  mounted() {\r\n    this.handleSearch();\r\n  },\r\n  methods: {\r\n    closeDropdownCode() { //关闭输入文本框\r\n      const { popContentCode } = this;\r\n      const { multipleRefCodeRef } = this.$refs;\r\n      this.popVisibleCode = false;\r\n      if(!popContentCode) return;\r\n      const content = popContentCode ? popContentCode.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesCode = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesCode = [...new Set(this.multiValuesCode)];\r\n      if(multipleRefCodeRef && multipleRefCodeRef.setValueArray){\r\n        multipleRefCodeRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownName() { //关闭输入文本框\r\n      const { popContentName } = this;\r\n      const { multipleRefNameRef } = this.$refs;\r\n      this.popVisibleName = false;\r\n      if(!popContentName) return;\r\n      const content = popContentName ? popContentName.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesName = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesName = [...new Set(this.multiValuesName)];\r\n      if(multipleRefNameRef && multipleRefNameRef.setValueArray){\r\n        multipleRefNameRef.setValueArray(this.multiValuesCode);\r\n      }\r\n    },\r\n    closeDropdownSpec() { //关闭输入文本框\r\n      const { popContentSpec } = this;\r\n      const { multipleRefSpecRef } = this.$refs;\r\n      this.popVisibleSpec = false;\r\n      if(!popContentSpec) return;\r\n      const content = popContentSpec ? popContentSpec.trim().replace(/，/g, \",\") : '';\r\n      this.multiValuesSpec = content.split('\\n').filter(v=>!!v);\r\n      this.multiValuesSpec = [...new Set(this.multiValuesSpec)];\r\n      if(multipleRefSpecRef && multipleRefSpecRef.setValueArray){\r\n        multipleRefSpecRef.setValueArray(this.multiValuesSpec);\r\n      }\r\n    },\r\n    getParam(){\r\n      const params = {\r\n        ...this.searchForm\r\n      };\r\n      const getStr = value => value && Array.isArray(value) ? value.join(\"&#&\") : undefined;\r\n      if (this.multiValuesCode.length > 0){\r\n        params[\"codes\"] = getStr(this.multiValuesCode);\r\n      }\r\n      if (this.multiValuesName.length > 0){\r\n        params[\"names\"] = getStr(this.multiValuesName);\r\n      }\r\n      if (this.multiValuesSpec.length > 0){\r\n        params[\"specs\"] = getStr(this.multiValuesSpec);\r\n      }\r\n      return params;\r\n    },\r\n    getDataSource(data = []) {\r\n      const result = [];\r\n      let index = 1;\r\n      for (const item of data) {\r\n        item['index'] = index++;\r\n        if (item && item.detailList) {\r\n          if (item.detailList.length === 0){\r\n            item.detailList = [{}];\r\n            result.push(item);\r\n          }else{\r\n            let rowSpan = item.detailList.length;\r\n            item.detailList.forEach((child, index) => {\r\n              const obj = {\r\n                ...item,\r\n                rowSpan: index ===0?rowSpan:0,\r\n                childSku: child && child.childSku,\r\n                childSpec: child && child.childSpec,\r\n                itemQuantity:child && child.quantity\r\n              };\r\n              result.push(obj);\r\n            });\r\n          }\r\n        }\r\n      }\r\n      return result;\r\n    },\r\n    handleSpan({ row, column}) {\r\n      if (this.mergeColumns.includes(column.key)) {\r\n        return {\r\n          rowspan: row.rowSpan,\r\n          colspan: 1\r\n        };\r\n      }\r\n    },\r\n    //查询\r\n    handleSearch() {\r\n      const params = this.getParam();\r\n      this.loading = true\r\n      Product.listPage(params).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.tableData = this.getDataSource(res.data.records || []);\r\n          this.searchForm.total = parseInt(res.data.total);\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleReset() {\r\n      //重置验证\r\n      this.$refs['searchFormRef'].resetFields();\r\n      this.searchForm.shops=[];\r\n      this.resetMultiple(true);\r\n    },\r\n    resetMultiple(clearTxt = false) {\r\n      if (clearTxt === true) {\r\n        this.multiValuesCode = [];\r\n        const { multipleRefCodeRef } = this.$refs;\r\n        if (multipleRefCodeRef && multipleRefCodeRef.setValueArray) {\r\n          multipleRefCodeRef.setValueArray([]);\r\n        }\r\n        this.multiValuesName = [];\r\n        const { multipleRefNameRef } = this.$refs;\r\n        if (multipleRefNameRef && multipleRefNameRef.setValueArray) {\r\n          multipleRefNameRef.setValueArray([]);\r\n        }\r\n        this.multiValuesSpec = [];\r\n        const { multipleRefSpecRef } = this.$refs;\r\n        if (multipleRefSpecRef && multipleRefSpecRef.setValueArray) {\r\n          multipleRefSpecRef.setValueArray([]);\r\n        }\r\n      }\r\n      this.popContentCode = undefined;\r\n      this.popVisibleCode = false;\r\n      this.popContentName = undefined;\r\n      this.popVisibleName = false;\r\n      this.popContentSpec = undefined;\r\n      this.popVisibleSpec = false;\r\n    },\r\n    handlePage(current) {\r\n      this.searchForm.page = current\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.searchForm.limit = size\r\n      this.handleSearch()\r\n    },\r\n    handleExport(){\r\n      this.loading = true;\r\n      let params = this.getParam();\r\n      params['fileName'] = \"产品信息表\" + new Date().getTime() + \".xls\";\r\n      Product.exportFile(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    onSync(){\r\n      //点击确定\r\n      this.syncLoading = true;\r\n      const content = this.syncForm.codes ? this.syncForm.codes.trim().replace(/，/g, \",\") : '';\r\n      let codes = content.split('\\n').filter(v=>!!v).join(\",\");\r\n      Product.syncProduct({\"codes\":codes}).then((res)=>{\r\n        if(res && res['code'] ===0){\r\n          this.$Message.success(\"提交同步任务成功,任务正在执行\");\r\n          this.onCancel();\r\n        }else{\r\n          this.$Message.error(res['message'])\r\n        }\r\n      }).catch(()=>{}).finally(()=>{this.syncLoading = false;});\r\n    },\r\n    downTemplate() {\r\n      this.loading = true;\r\n      let params = {};\r\n      params['fileName'] = \"组合品导入模板.xls\";\r\n      Product.downTemplate(params, () => {\r\n        this.loading = false\r\n      });\r\n    },\r\n    handleImportError (err, file) {\r\n      this.$Message.error(file.message);\r\n    },\r\n    handleImportSuccess (res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res.code === 0) {\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.error(res.message);\r\n      }\r\n    },\r\n    handleMaxSize () {\r\n      this.$Message.warning('大小不能超过10M.');\r\n    },\r\n    handleImportFormatError (file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.search-con-top{\r\n  position: relative;\r\n  padding: 0;\r\n  //标签、sku、asin搜索项\r\n  .multiClass{\r\n    .flex-h{\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-top:2px;\r\n    }\r\n  }\r\n}\r\n.widthClass {\r\n  width: 350px\r\n}\r\n.buttonMargin{\r\n  margin-left:15px;\r\n}\r\n</style>\r\n"]}]}