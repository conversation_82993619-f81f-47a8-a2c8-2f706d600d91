{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\basf\\product.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\basf\\product.js", "mtime": 1752737748401}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC9saWJzL3JlcXVlc3QnOwppbXBvcnQgeyBOZXNwb3N0UmVxdWVzdCB9IGZyb20gJ0AvbGlicy9heGlvcy5qcyc7CnZhciBwcm9kdWN0UGF0aCA9ICIvYmFzZS9wcm9kdWN0IjsKLyoqDQogKiDojrflj5bliIbpobXmlbDmja4NCiAqLwp2YXIgbGlzdFBhZ2UgPSBmdW5jdGlvbiBsaXN0UGFnZShwYXJhbXMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IHByb2R1Y3RQYXRoICsgJy9saXN0UGFnZScsCiAgICBwYXJhbXM6IHBhcmFtcywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfTsKdmFyIHN5bmNQcm9kdWN0ID0gZnVuY3Rpb24gc3luY1Byb2R1Y3QocGFyYW1zKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiBwcm9kdWN0UGF0aCArICcvc3luY1Byb2R1Y3QnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9Owp2YXIgZXhwb3J0RmlsZSA9IGZ1bmN0aW9uIGV4cG9ydEZpbGUocGFyYW1zKSB7CiAgcmV0dXJuIE5lc3Bvc3RSZXF1ZXN0KHByb2R1Y3RQYXRoICsgJy9leHBvcnRGaWxlJywgcGFyYW1zKTsKfTsKdmFyIGxpc3RSZWxheFBhZ2UgPSBmdW5jdGlvbiBsaXN0UmVsYXhQYWdlKHBhcmFtcykgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogcHJvZHVjdFBhdGggKyAnL2xpc3RSZWxheFBhZ2UnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CnZhciBleHBvcnRSZWxheEZpbGUgPSBmdW5jdGlvbiBleHBvcnRSZWxheEZpbGUocGFyYW1zKSB7CiAgcmV0dXJuIE5lc3Bvc3RSZXF1ZXN0KHByb2R1Y3RQYXRoICsgJy9leHBvcnRSZWxheEZpbGUnLCBwYXJhbXMpOwp9Owp2YXIgYWRkUmVsYXggPSBmdW5jdGlvbiBhZGRSZWxheChwYXJhbXMpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6IHByb2R1Y3RQYXRoICsgJy9hZGRSZWxheCcsCiAgICBwYXJhbXM6IHBhcmFtcywKICAgIG1ldGhvZDogJ3Bvc3QnCiAgfSk7Cn07CnZhciBkZWxSZWxheCA9IGZ1bmN0aW9uIGRlbFJlbGF4KGlkcykgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogcHJvZHVjdFBhdGggKyAnL2RlbFJlbGF4P2lkPScgKyBpZHMsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9OwpleHBvcnQgdmFyIGxpc3RBbGxTcHUgPSBmdW5jdGlvbiBsaXN0QWxsU3B1KHBhcmFtcykgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogcHJvZHVjdFBhdGggKyAnL2xpc3RBbGxTcHUnLAogICAgcGFyYW1zOiBwYXJhbXMsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBsaXN0UGFnZTogbGlzdFBhZ2UsCiAgc3luY1Byb2R1Y3Q6IHN5bmNQcm9kdWN0LAogIGV4cG9ydEZpbGU6IGV4cG9ydEZpbGUsCiAgbGlzdFJlbGF4UGFnZTogbGlzdFJlbGF4UGFnZSwKICBleHBvcnRSZWxheEZpbGU6IGV4cG9ydFJlbGF4RmlsZSwKICBhZGRSZWxheDogYWRkUmVsYXgsCiAgZGVsUmVsYXg6IGRlbFJlbGF4Cn07"}, {"version": 3, "names": ["request", "NespostRequest", "productPath", "listPage", "params", "url", "method", "syncProduct", "exportFile", "listRelaxPage", "exportRelaxFile", "addRelax", "delRelax", "ids", "listAllSpu"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/basf/product.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport { NespostRequest } from '@/libs/axios.js';\r\nconst productPath = \"/base/product\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: productPath + '/listPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\nconst syncProduct = (params) => {\r\n  return request({\r\n    url: productPath + '/syncProduct',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\nconst exportFile = (params)=>{\r\n  return NespostRequest(productPath + '/exportFile',params);\r\n}\r\nconst listRelaxPage = (params) => {\r\n  return request({\r\n    url: productPath + '/listRelaxPage',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\nconst exportRelaxFile = (params)=>{\r\n  return NespostRequest(productPath + '/exportRelaxFile',params);\r\n}\r\nconst addRelax = (params) => {\r\n  return request({\r\n    url: productPath + '/addRelax',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\nconst delRelax = (ids) => {\r\n  return request({\r\n    url: productPath + '/delRelax?id='+ids,\r\n    method: 'post'\r\n  })\r\n}\r\nexport const listAllSpu = (params) => {\r\n  return request({\r\n    url: productPath + '/listAllSpu',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\nexport default {\r\n  listPage,\r\n  syncProduct,\r\n  exportFile,\r\n  listRelaxPage,\r\n  exportRelaxFile,\r\n  addRelax,\r\n  delRelax\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,IAAMC,WAAW,GAAG,eAAe;AACnC;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,WAAW;IAC9BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIH,MAAM,EAAK;EAC9B,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,cAAc;IACjCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAIJ,MAAM,EAAG;EAC3B,OAAOH,cAAc,CAACC,WAAW,GAAG,aAAa,EAACE,MAAM,CAAC;AAC3D,CAAC;AACD,IAAMK,aAAa,GAAG,SAAhBA,aAAaA,CAAIL,MAAM,EAAK;EAChC,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,gBAAgB;IACnCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMI,eAAe,GAAG,SAAlBA,eAAeA,CAAIN,MAAM,EAAG;EAChC,OAAOH,cAAc,CAACC,WAAW,GAAG,kBAAkB,EAACE,MAAM,CAAC;AAChE,CAAC;AACD,IAAMO,QAAQ,GAAG,SAAXA,QAAQA,CAAIP,MAAM,EAAK;EAC3B,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,WAAW;IAC9BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,GAAG,EAAK;EACxB,OAAOb,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,eAAe,GAACW,GAAG;IACtCP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAMQ,UAAU,GAAG,SAAbA,UAAUA,CAAIV,MAAM,EAAK;EACpC,OAAOJ,OAAO,CAAC;IACbK,GAAG,EAAEH,WAAW,GAAG,aAAa;IAChCE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,eAAe;EACbH,QAAQ,EAARA,QAAQ;EACRI,WAAW,EAAXA,WAAW;EACXC,UAAU,EAAVA,UAAU;EACVC,aAAa,EAAbA,aAAa;EACbC,eAAe,EAAfA,eAAe;EACfC,QAAQ,EAARA,QAAQ;EACRC,QAAQ,EAARA;AACF,CAAC"}]}