{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\vatNo.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\api\\custom\\vatNo.js", "mtime": 1752737748406}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "NespostRequest", "exportRequest", "vatNoPath", "listPage", "params", "url", "method", "saveVatNo", "get<PERSON>y", "remove", "getLogRefType", "download", "callback", "config", "fileName", "downloadTemplate"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/api/custom/vatNo.js"], "sourcesContent": ["import request from '@/libs/request'\r\nimport {NespostRequest} from '@/libs/axios.js';\r\nimport exportRequest from \"@/libs/exportRequest\";\r\n\r\nconst vatNoPath = \"/base/vatNo\";\r\n/**\r\n * 获取分页数据\r\n */\r\nconst listPage = (params) => {\r\n  return request({\r\n    url: vatNoPath + '/listPage',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 添加数据\r\n */\r\nconst saveVatNo = (params) => {\r\n  return NespostRequest(vatNoPath + '/saveVatNo', params)\r\n}\r\n/**\r\n * 获取明细数据\r\n */\r\nconst getBy = (params) => {\r\n  return request({\r\n    url: vatNoPath + '/getBy',\r\n    params,\r\n    method: 'get'\r\n  })\r\n}\r\n/**\r\n * 删除数据\r\n */\r\nconst remove = (params) => {\r\n  return request({\r\n    url: vatNoPath + '/delVatNo',\r\n    params,\r\n    method: 'post'\r\n  })\r\n}\r\n/**\r\n * 获取日志\r\n */\r\nconst getLogRefType = () => {\r\n  return request({\r\n    url: vatNoPath + '/getLogRefType',\r\n    method: 'get'\r\n  })\r\n}\r\nconst download = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(vatNoPath + '/download',config,callback);\r\n}\r\nconst downloadTemplate = (params,callback)=>{\r\n  const config = {\r\n    params:params,\r\n    method: 'get',\r\n    fileName:params['fileName']\r\n  }\r\n  return exportRequest(vatNoPath + '/downloadTemplate',config,callback);\r\n}\r\nexport default {\r\n  listPage,\r\n  saveVatNo,\r\n  getBy,\r\n  remove,\r\n  getLogRefType,\r\n  download,\r\n  downloadTemplate\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAAQC,cAAc,QAAO,iBAAiB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,IAAMC,SAAS,GAAG,aAAa;AAC/B;AACA;AACA;AACA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,MAAM,EAAK;EAC3B,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,SAAS,GAAG,WAAW;IAC5BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIH,MAAM,EAAK;EAC5B,OAAOJ,cAAc,CAACE,SAAS,GAAG,YAAY,EAAEE,MAAM,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA,IAAMI,KAAK,GAAG,SAARA,KAAKA,CAAIJ,MAAM,EAAK;EACxB,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,SAAS,GAAG,QAAQ;IACzBE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMG,MAAM,GAAG,SAATA,MAAMA,CAAIL,MAAM,EAAK;EACzB,OAAOL,OAAO,CAAC;IACbM,GAAG,EAAEH,SAAS,GAAG,WAAW;IAC5BE,MAAM,EAANA,MAAM;IACNE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,OAAOX,OAAO,CAAC;IACbM,GAAG,EAAEH,SAAS,GAAG,gBAAgB;IACjCI,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AACD,IAAMK,QAAQ,GAAG,SAAXA,QAAQA,CAAIP,MAAM,EAACQ,QAAQ,EAAG;EAClC,IAAMC,MAAM,GAAG;IACbT,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbQ,QAAQ,EAACV,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,SAAS,GAAG,WAAW,EAACW,MAAM,EAACD,QAAQ,CAAC;AAC/D,CAAC;AACD,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIX,MAAM,EAACQ,QAAQ,EAAG;EAC1C,IAAMC,MAAM,GAAG;IACbT,MAAM,EAACA,MAAM;IACbE,MAAM,EAAE,KAAK;IACbQ,QAAQ,EAACV,MAAM,CAAC,UAAU;EAC5B,CAAC;EACD,OAAOH,aAAa,CAACC,SAAS,GAAG,mBAAmB,EAACW,MAAM,EAACD,QAAQ,CAAC;AACvE,CAAC;AACD,eAAe;EACbT,QAAQ,EAARA,QAAQ;EACRI,SAAS,EAATA,SAAS;EACTC,KAAK,EAALA,KAAK;EACLC,MAAM,EAANA,MAAM;EACNC,aAAa,EAAbA,aAAa;EACbC,QAAQ,EAARA,QAAQ;EACRI,gBAAgB,EAAhBA;AACF,CAAC"}]}