{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\clipboard.js", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\directive\\clipboard.js", "mtime": 1752737748498}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IENsaXBib2FyZCBmcm9tICdjbGlwYm9hcmQnOwpleHBvcnQgZGVmYXVsdCB7CiAgYmluZDogZnVuY3Rpb24gYmluZChlbCwgYmluZGluZykgewogICAgdmFyIGNsaXBib2FyZCA9IG5ldyBDbGlwYm9hcmQoZWwsIHsKICAgICAgdGV4dDogZnVuY3Rpb24gdGV4dCgpIHsKICAgICAgICByZXR1cm4gYmluZGluZy52YWx1ZS52YWx1ZTsKICAgICAgfQogICAgfSk7CiAgICBlbC5fX3N1Y2Nlc3NfY2FsbGJhY2tfXyA9IGJpbmRpbmcudmFsdWUuc3VjY2VzczsKICAgIGVsLl9fZXJyb3JfY2FsbGJhY2tfXyA9IGJpbmRpbmcudmFsdWUuZXJyb3I7CiAgICBjbGlwYm9hcmQub24oJ3N1Y2Nlc3MnLCBmdW5jdGlvbiAoZSkgewogICAgICB2YXIgY2FsbGJhY2sgPSBlbC5fX3N1Y2Nlc3NfY2FsbGJhY2tfXzsKICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soZSk7CiAgICB9KTsKICAgIGNsaXBib2FyZC5vbignZXJyb3InLCBmdW5jdGlvbiAoZSkgewogICAgICB2YXIgY2FsbGJhY2sgPSBlbC5fX2Vycm9yX2NhbGxiYWNrX187CiAgICAgIGNhbGxiYWNrICYmIGNhbGxiYWNrKGUpOwogICAgfSk7CiAgICBlbC5fX2NsaXBib2FyZF9fID0gY2xpcGJvYXJkOwogIH0sCiAgdXBkYXRlOiBmdW5jdGlvbiB1cGRhdGUoZWwsIGJpbmRpbmcpIHsKICAgIGVsLl9fY2xpcGJvYXJkX18udGV4dCA9IGZ1bmN0aW9uICgpIHsKICAgICAgcmV0dXJuIGJpbmRpbmcudmFsdWUudmFsdWU7CiAgICB9OwogICAgZWwuX19zdWNjZXNzX2NhbGxiYWNrX18gPSBiaW5kaW5nLnZhbHVlLnN1Y2Nlc3M7CiAgICBlbC5fX2Vycm9yX2NhbGxiYWNrX18gPSBiaW5kaW5nLnZhbHVlLmVycm9yOwogIH0sCiAgdW5iaW5kOiBmdW5jdGlvbiB1bmJpbmQoZWwsIGJpbmRpbmcpIHsKICAgIGRlbGV0ZSBlbC5fX3N1Y2Nlc3NfY2FsbGJhY2tfXzsKICAgIGRlbGV0ZSBlbC5fX2Vycm9yX2NhbGxiYWNrX187CiAgICBlbC5fX2NsaXBib2FyZF9fLmRlc3Ryb3koKTsKICAgIGRlbGV0ZSBlbC5fX2NsaXBib2FyZF9fOwogIH0KfTs="}, {"version": 3, "names": ["Clipboard", "bind", "el", "binding", "clipboard", "text", "value", "__success_callback__", "success", "__error_callback__", "error", "on", "e", "callback", "__clipboard__", "update", "unbind", "destroy"], "sources": ["D:/Users/<USER>/Desktop/dev/sy_declare_ui/src/directive/clipboard.js"], "sourcesContent": ["import Clipboard from 'clipboard'\r\nexport default {\r\n  bind: (el, binding) => {\r\n    const clipboard = new Clipboard(el, {\r\n      text: () => binding.value.value\r\n    })\r\n    el.__success_callback__ = binding.value.success\r\n    el.__error_callback__ = binding.value.error\r\n    clipboard.on('success', e => {\r\n      const callback = el.__success_callback__\r\n      callback && callback(e)\r\n    })\r\n    clipboard.on('error', e => {\r\n      const callback = el.__error_callback__\r\n      callback && callback(e)\r\n    })\r\n    el.__clipboard__ = clipboard\r\n  },\r\n  update: (el, binding) => {\r\n    el.__clipboard__.text = () => binding.value.value\r\n    el.__success_callback__ = binding.value.success\r\n    el.__error_callback__ = binding.value.error\r\n  },\r\n  unbind: (el, binding) => {\r\n    delete el.__success_callback__\r\n    delete el.__error_callback__\r\n    el.__clipboard__.destroy()\r\n    delete el.__clipboard__\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,WAAW;AACjC,eAAe;EACbC,IAAI,EAAE,SAAAA,KAACC,EAAE,EAAEC,OAAO,EAAK;IACrB,IAAMC,SAAS,GAAG,IAAIJ,SAAS,CAACE,EAAE,EAAE;MAClCG,IAAI,EAAE,SAAAA,KAAA;QAAA,OAAMF,OAAO,CAACG,KAAK,CAACA,KAAK;MAAA;IACjC,CAAC,CAAC;IACFJ,EAAE,CAACK,oBAAoB,GAAGJ,OAAO,CAACG,KAAK,CAACE,OAAO;IAC/CN,EAAE,CAACO,kBAAkB,GAAGN,OAAO,CAACG,KAAK,CAACI,KAAK;IAC3CN,SAAS,CAACO,EAAE,CAAC,SAAS,EAAE,UAAAC,CAAC,EAAI;MAC3B,IAAMC,QAAQ,GAAGX,EAAE,CAACK,oBAAoB;MACxCM,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;IACzB,CAAC,CAAC;IACFR,SAAS,CAACO,EAAE,CAAC,OAAO,EAAE,UAAAC,CAAC,EAAI;MACzB,IAAMC,QAAQ,GAAGX,EAAE,CAACO,kBAAkB;MACtCI,QAAQ,IAAIA,QAAQ,CAACD,CAAC,CAAC;IACzB,CAAC,CAAC;IACFV,EAAE,CAACY,aAAa,GAAGV,SAAS;EAC9B,CAAC;EACDW,MAAM,EAAE,SAAAA,OAACb,EAAE,EAAEC,OAAO,EAAK;IACvBD,EAAE,CAACY,aAAa,CAACT,IAAI,GAAG;MAAA,OAAMF,OAAO,CAACG,KAAK,CAACA,KAAK;IAAA;IACjDJ,EAAE,CAACK,oBAAoB,GAAGJ,OAAO,CAACG,KAAK,CAACE,OAAO;IAC/CN,EAAE,CAACO,kBAAkB,GAAGN,OAAO,CAACG,KAAK,CAACI,KAAK;EAC7C,CAAC;EACDM,MAAM,EAAE,SAAAA,OAACd,EAAE,EAAEC,OAAO,EAAK;IACvB,OAAOD,EAAE,CAACK,oBAAoB;IAC9B,OAAOL,EAAE,CAACO,kBAAkB;IAC5BP,EAAE,CAACY,aAAa,CAACG,OAAO,CAAC,CAAC;IAC1B,OAAOf,EAAE,CAACY,aAAa;EACzB;AACF,CAAC"}]}