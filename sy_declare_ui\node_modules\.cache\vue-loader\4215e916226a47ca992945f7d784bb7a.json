{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\productRelax.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\productRelax.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["productRelax.vue"], "names": [], "mappings": ";AAgEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "productRelax.vue", "sourceRoot": "src/view/module/basf/product", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <div class=\"search-con search-con-top\">\r\n        <Form ref=\"searchForm\" class=\"searchForm\" :model=\"formValues\" :inline=\"true\" @submit.native.prevent>\r\n          <FormItem prop=\"sku\">\r\n            <Input type=\"text\" v-model=\"formValues.sku\" placeholder=\"产品SKU\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"productName\">\r\n            <Input type=\"text\" v-model=\"formValues.productName\" placeholder=\"产品名称\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"relaxSku\">\r\n            <Input type=\"text\" v-model=\"formValues.relaxSku\" placeholder=\"被关联sku\"/>\r\n          </FormItem>\r\n          <FormItem prop=\"productName\">\r\n            <Input type=\"text\" v-model=\"formValues.relaxProductName\" placeholder=\"被关联产品名称\"/>\r\n          </FormItem>\r\n          <FormItem>\r\n            <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>&nbsp;\r\n            <Button @click=\"handleResetForm()\">重置</Button>\r\n          </FormItem>\r\n        </Form>\r\n      </div>\r\n      <div class=\"search-con search-con-top\">\r\n        <ButtonGroup>\r\n          <Button @click=\"handleModal('add')\"><span>新增关联</span></Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"handleModal('del')\">删除</Button>\r\n          <Button style=\"margin-left:15px;\" type=\"primary\" @click=\"handleModal('importRelax')\"><span>导入关联</span></Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"executeExport();\">导出产品</Button>\r\n          <Button style=\"margin-left:15px;\" @click=\"handleModal('exportLog')\">查看导出记录</Button>\r\n        </ButtonGroup>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\" @on-selection-change=\"onSelectChange\">\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\"\r\n            :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      <Modal v-model=\"addModelVisible\" :title=\"addModelTitle\" @on-cancel=\"handleResetAddForm\">\r\n        <div style=\"width:400px\">\r\n          <Form ref=\"addFormRef\" :model=\"addForm\" :rules=\"addFormItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"产品编码\" prop=\"sku\">\r\n              <Input v-model=\"addForm.sku\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"关联产品编码\" prop=\"relaxSku\">\r\n              <Input v-model=\"addForm.relaxSku\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n          </Form>\r\n          <div class=\"drawer-footer\">\r\n            <Button type=\"default\" @click=\"handleResetAddForm\">取消</Button>&nbsp;\r\n            <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"loading\">保存</Button>\r\n          </div>\r\n        </div>\r\n      </Modal>\r\n    </Card>\r\n    <ExportFile :onCancel=\"()=>{this.modalExportVisible=false;}\" :visible=\"modalExportVisible\" ref=\"ExportModalRef\"\r\n                :title=\"exportTitle\" :taskType=\"exportTaskType\"\r\n                :shadow=\"true\" :executeUrl=\"executeUrl\" :fileName=\"exportFileName\"/>\r\n    <ImportFile :onCancel=\"()=>{this.modalImportVisible=false;}\" :visible=\"modalImportVisible\" ref=\"ImportModalRef\" :title=\"importTitle\" :taskType=\"importTaskType\"\r\n                :downTemplateUrl=\"templateUrl\" :templateName=\"templateName\" :url=\"importUrl\" :shadow=\"true\" :executeUrl=\"executeUrl\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Product from '@/api/basf/product'\r\nimport ExportFileJs from '@/api/common/exportFile'\r\nimport {autoTableHeight} from '@/libs/tools.js'\r\nimport ExportFile from \"@/view/module/common/exportFile.vue\";\r\nimport ImportFile from \"@/view/module/common/importFile.vue\";\r\nexport default {\r\n  name: 'ProductRelax',\r\n  components: {\r\n    ExportFile,ImportFile\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      loading: false,\r\n      selectedRows: [],\r\n      exportTime: new Date()-5000,\r\n      formValues: {\r\n        sku: null,\r\n        sellerSku: null,\r\n        productName: null,\r\n        spu:null,\r\n        relaxProductName:null\r\n      },\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      columns: [{type: 'selection', key: 'selection',width: 60},\r\n        {title: '产品编码',key: 'sku',align: \"center\",width: 120,render: (h, {row}) => (<span v-copytext={row.sku}>{row.sku}</span>)},\r\n        {title: '产品名称', key: 'productName', align: \"center\",width: 350, render: (h, {row}) => (<span v-copytext={row.productName}>{row.productName}</span>)},\r\n        {title: '产品属性', key: 'productAttr', align: \"center\",width: 200, render: (h, {row}) => (<span v-copytext={row[\"productAttr\"]}>{row[\"productAttr\"]}</span>)},\r\n        {title: '关联产品编码', key: 'relaxSku', align: \"center\",width: 350, render: (h, {row}) => (<span v-copytext={row.relaxSku}>{row.relaxSku}</span>)},\r\n        {title: '关联产品名称', key: 'relaxProductName', align: \"center\",width: 350, render: (h, {row}) => (<span v-copytext={row.relaxProductName}>{row.relaxProductName}</span>)},\r\n        {title: '关联产品属性', key: 'relaxProductAttr', align: \"center\",width: 350, render: (h, {row}) => (<span v-copytext={row.relaxProductAttr}>{row.relaxProductAttr}</span>)},\r\n      ],\r\n      data: [],\r\n      executeUrl: \"/base/product/executeRelax\",\r\n      modalExportVisible: false,\r\n      exportTitle: \"导出产品关联列表\",\r\n      exportTaskType: \"PRODUCT_RELAX_EXPORT\",\r\n      exportFileName: \"产品关联列表\",\r\n      modalImportVisible:false,\r\n      importTitle: \"导入产品关联列表\",\r\n      importTaskType: \"PRODUCT_RELAX_IMPORT\",\r\n      templateUrl:\"/base/product/downloadRelaxTemplate\",\r\n      templateName:\"产品关联模板\",\r\n      importUrl:\"/base/product/importRelaxFile\",\r\n      addModelTitle:\"新增\",\r\n      addModelVisible:false,\r\n      addForm:{sku: null, relaxSku: null},\r\n      addFormItemRules:[],\r\n    }\r\n  },\r\n  methods: {\r\n    getParams(needPage = false) {\r\n      const {formValues, pageInfo} = this;\r\n      const pageParam = needPage ? pageInfo : {};\r\n      return {\r\n        ...pageParam, ...formValues\r\n      };\r\n    },\r\n    //表格选中行\r\n    onSelectChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n    handleSearch() {\r\n      this.loading = true\r\n      Product.listRelaxPage(this.getParams(true)).then((res) => {\r\n        if (res && res['code'] === 0) {\r\n          const data = res.data || {};\r\n          this.data = data.records || [];\r\n          const getValue = (value, defaultValue) =>\r\n            value || value === 0 ? +value : defaultValue;\r\n          this.pageInfo = {\r\n            total: getValue(data.total, 0),\r\n            page: getValue(data.page, 1),\r\n            limit: getValue(data.limit, 10)\r\n          };\r\n        } else this.data = [];\r\n      }).catch(() => {\r\n        this.data = [];\r\n      }).finally(() => {\r\n        this.loading = false;\r\n        this.selectedRows = [];\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch()\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.pageInfo.page = 1;\r\n      this.handleSearch()\r\n    },\r\n    handleResetForm() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handleResetAddForm() {\r\n      this.$refs['addFormRef'].resetFields();\r\n      this.addModelVisible = false;\r\n    },\r\n    handleSubmit(){\r\n      let form = this.$refs['addFormRef']\r\n      form.validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          Product.addRelax(this.addForm).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('保存成功')\r\n              this.handleResetAddForm()\r\n            }\r\n            this.handleSearch()\r\n          }).finally(() => {\r\n            this.saving = false\r\n          })\r\n        }\r\n      });\r\n    },\r\n    handleModal(type) {\r\n      if(type === 'importRelax'){\r\n        this.modalImportVisible = true;\r\n      } else if (type === 'exportLog') {\r\n        this.modalExportVisible = true;\r\n      } else if (type === 'add') {\r\n        this.addModelVisible = true;\r\n      } else if (type === 'del') {\r\n        this.handleModalDelete();\r\n      }\r\n    },\r\n    handleModalDelete(){\r\n      if (this.selectedRows.length === 0) {\r\n        return;\r\n      }\r\n      const ids = this.selectedRows.map(v => v.id).join(',');\r\n      if (ids === null || ids === '') {\r\n        this.$Message.success('请选择需要删除的记录！');\r\n        return;\r\n      }\r\n      let that = this;\r\n      this.$Modal.confirm({\r\n        title: '确认删除已选数据吗？',\r\n        content: '温馨提示：数据删除后需要重新生成，请谨慎操作！',\r\n        onOk: () => {\r\n          Product.delRelax(ids).then(res => {\r\n              if (res['code'] === 0) {\r\n                that.handleSearch();\r\n                that.selectedRows = [];\r\n              }\r\n            }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    executeExport() {\r\n      if (new Date() - this.exportTime <= 5000) {\r\n        this.$Message.success('导出间隔5S！');\r\n        return;\r\n      }\r\n      this.exportTime = new Date();\r\n      Product.exportRelaxFile(this.getParams(false)).then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.$Message.success('导出消息添加成功！');\r\n          this.loading = false;\r\n          ExportFileJs.intervalFunc({\"id\": res.data, \"fileName\": this.exportFileName});\r\n        } else {\r\n          this.$Message.error(res['message']);\r\n        }\r\n      }).catch(() => {\r\n\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n  },\r\n\r\n  mounted: function () {\r\n    this.handleSearch();\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}