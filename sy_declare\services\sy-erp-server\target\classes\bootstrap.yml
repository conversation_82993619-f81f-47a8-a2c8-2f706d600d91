server:
    port: 8235
    servlet:
        session:
            cookie:
                name: OAUTH2SESSION
spring:
    application:
        name: sy-erp-server
    cloud:
        #手动配置Bus id,
        bus:
            id: ${spring.application.name}:${server.port}
        nacos:
            config:
                enabled: true
                username: nacos
                password: sy@nacos2022
                file-extension: properties
                shared-configs[0]:
                    data-id: common.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[1]:
                    data-id: db.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[2]:
                    data-id: redis.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[3]:
                    data-id: rabbitmq.properties
                    refresh: true
                    group: DEFAULT_GROUP
                shared-configs[4]:
                    data-id: workflow.properties
                    refresh: true
                    group: DEFAULT_GROUP

    # 数据源配置完全从Nacos的workflow.properties中获取
    # 包括基本配置和连接池配置：
    # spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
    # spring.datasource.url=********************************************************************************************************************************************************************************************************
    # spring.datasource.username=root
    # spring.datasource.password=123456
    # spring.datasource.type=com.zaxxer.hikari.HikariDataSource
    # spring.datasource.hikari.maximum-pool-size=20
    # spring.datasource.hikari.minimum-idle=5
    # spring.datasource.hikari.connection-timeout=30000
    # spring.datasource.hikari.idle-timeout=600000
    # spring.datasource.hikari.max-lifetime=1800000
    # spring.datasource.hikari.leak-detection-threshold=60000
    # spring.datasource.hikari.pool-name=ErpNacosPool

    main:
        allow-bean-definition-overriding: true
    #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: local
      # 文件上传限制
    servlet:
        multipart:
            max-file-size: 10MB
            max-request-size: 10MB
    thymeleaf:
        cache: false
        encoding: UTF-8
        mode: LEGACYHTML5
        prefix: classpath:/templates/
        suffix: .html

management:
    endpoints:
        web:
            exposure:
                include: '*'

# Feign 和 Hystrix 配置
feign:
    hystrix:
        enabled: true
    compression:
        request:
            enabled: false
        response:
            enabled: false
    client:
        config:
            default:
                connectTimeout: 10000
                readTimeout: 30000
            # 为 ERP 工作流服务配置更长的超时时间
            sy-erp-server:
                connectTimeout: 15000
                readTimeout: 60000

# Hystrix 配置
hystrix:
    command:
        default:
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 30000  # 增加到30秒
            circuitBreaker:
                enabled: true
                requestVolumeThreshold: 20
                sleepWindowInMilliseconds: 5000
                errorThresholdPercentage: 50
        # 为工作流相关操作配置更长的超时时间
        ErpWorkflowClient#completeTask(String,Map):
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 60000  # 完成任务操作60秒超时
        ErpWorkflowClient#startProcess(String,String,Map):
            execution:
                isolation:
                    thread:
                        timeoutInMilliseconds: 45000  # 启动流程45秒超时
aimo:
    swagger2:
        enabled: true
        description: 平台用户认证服务器
        title: 平台用户认证服务器
    client:
        oauth2:
            admin:
                client-id: 7gBZcbsC7kLIWCdELIl8nxcs
                client-secret: 0osTIhce7uPvDKHz6aa67bhCukaKoYl4
#mybatis plus 设置
mybatis-plus:
 #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sy.erp.client.**.entity
  mapper-locations: classpath:mapper/*.xml