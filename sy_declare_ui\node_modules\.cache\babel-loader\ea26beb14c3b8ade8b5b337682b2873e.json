{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\product\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFByb2R1Y3RJbmZvIGZyb20gJy4vcHJvZHVjdEluZm8udnVlJzsgLy/kuqflk4Hkv6Hmga8KaW1wb3J0IFByb2R1Y3RSZWxheCBmcm9tICcuL3Byb2R1Y3RSZWxheC52dWUnOyAvL+S6p+WTgeWFs+iBlApleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ3Byb2R1Y3RMb29rJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHt9OwogIH0sCiAgY29tcG9uZW50czogewogICAgUHJvZHVjdEluZm86IFByb2R1Y3RJbmZvLAogICAgUHJvZHVjdFJlbGF4OiBQcm9kdWN0UmVsYXgKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICBjbGlja1RhYjogZnVuY3Rpb24gY2xpY2tUYWIoKSB7CiAgICAgIHZhciBfdGhpcyQkcmVmcyA9IHRoaXMuJHJlZnMsCiAgICAgICAgUHJvZHVjdEluZm8gPSBfdGhpcyQkcmVmcy5Qcm9kdWN0SW5mbywKICAgICAgICBQcm9kdWN0UmVsYXggPSBfdGhpcyQkcmVmcy5Qcm9kdWN0UmVsYXg7CiAgICAgIGlmIChQcm9kdWN0SW5mbyAmJiBQcm9kdWN0SW5mby5jbG9zZURyb3Bkb3duKSB7CiAgICAgICAgUHJvZHVjdEluZm8uY2xvc2VEcm9wZG93bigpOwogICAgICB9CiAgICAgIGlmIChQcm9kdWN0UmVsYXggJiYgUHJvZHVjdFJlbGF4LmNsb3NlRHJvcGRvd24pIHsKICAgICAgICBQcm9kdWN0UmVsYXguY2xvc2VEcm9wZG93bigpOwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["ProductInfo", "ProductRelax", "name", "data", "components", "mounted", "methods", "clickTab", "_this$$refs", "$refs", "closeDropdown"], "sources": ["src/view/module/basf/product/index.vue"], "sourcesContent": ["<!--\r\n@create date 2024-01-09\r\n@desc 产品信息表\r\n-->\r\n<template>\r\n  <div>\r\n    <Tabs type=\"card\" @on-click=\"clickTab\">\r\n      <TabPane label=\"产品信息\" name=\"1\" tab=\"global\">\r\n        <ProductInfo ref=\"productInfo\"/>\r\n      </TabPane>\r\n      <TabPane label=\"产品关联\" name=\"2\" tab=\"global\">\r\n        <ProductRelax ref=\"productRelax\"/>\r\n      </TabPane>\r\n    </Tabs>\r\n  </div>\r\n</template>\r\n<script>\r\nimport ProductInfo from './productInfo.vue'; //产品信息\r\nimport ProductRelax from './productRelax.vue'; //产品关联\r\nexport default {\r\n  name: 'productLook',\r\n  data () {\r\n    return {};\r\n  },\r\n  components: {\r\n    ProductInfo,ProductRelax\r\n  },\r\n  mounted () {\r\n  },\r\n  methods: {\r\n    clickTab () {\r\n      const { ProductInfo,ProductRelax } = this.$refs;\r\n      if (ProductInfo && ProductInfo.closeDropdown) {\r\n        ProductInfo.closeDropdown();\r\n      }\r\n      if (ProductRelax && ProductRelax.closeDropdown) {\r\n        ProductRelax.closeDropdown();\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n"], "mappings": "AAiBA,OAAAA,WAAA;AACA,OAAAC,YAAA;AACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,UAAA;IACAJ,WAAA,EAAAA,WAAA;IAAAC,YAAA,EAAAA;EACA;EACAI,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,IAAAC,WAAA,QAAAC,KAAA;QAAAT,WAAA,GAAAQ,WAAA,CAAAR,WAAA;QAAAC,YAAA,GAAAO,WAAA,CAAAP,YAAA;MACA,IAAAD,WAAA,IAAAA,WAAA,CAAAU,aAAA;QACAV,WAAA,CAAAU,aAAA;MACA;MACA,IAAAT,YAAA,IAAAA,YAAA,CAAAS,aAAA;QACAT,YAAA,CAAAS,aAAA;MACA;IACA;EACA;AACA"}]}