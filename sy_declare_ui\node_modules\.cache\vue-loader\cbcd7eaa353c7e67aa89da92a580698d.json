{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearance\\index.vue?vue&type=style&index=0&id=5affd8c0&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\clearance\\clearance\\index.vue", "mtime": 1752737748522}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud2lkdGhDbGFzcyB7DQogIHdpZHRoOiAzNTBweA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAqbA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/clearance/clearance", "sourcesContent": ["<!--\r\n@create date 2020-07-09\r\n@desc 报关订舱表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline @keydown.native.enter.prevent=\"handleSearch\">\r\n      <FormItem prop=\"date\">\r\n        <DatePicker type=\"daterange\" v-model=\"searchForm.date\" placement=\"bottom-start\" @on-change=\"dateChange\"\r\n                    placeholder=\"发货开始日期-发货结束日期\" style=\"width: 200px\"></DatePicker>\r\n      </FormItem>\r\n      <FormItem prop=\"thdOrder\">\r\n        <Input type=\"text\" v-model=\"searchForm.thdOrder\" placeholder=\"货件号\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"clearanceRank\">\r\n        <Input type=\"text\" v-model=\"searchForm.clearanceRank\" placeholder=\"合并编码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"consignorId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.consignorId\" placeholder=\"境内发货人\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in consignorList\" :value=\"item.id\" :key=\"index\">{{ item['consignorName'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"consigneeId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.consigneeId\" placeholder=\"境外收货人\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in consigneeList\" :value=\"item.id\" :key=\"index\">{{ item['consigneeName'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"providerId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.providerId\" placeholder=\"物流商\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in providerList\" :value=\"item.id\" :key=\"index\">{{ item['providerCode'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"channelId\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.channelId\" placeholder=\"物流渠道\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in channelList\" :value=\"item.id\" :key=\"index\">{{ item['channelName'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"whCode\" :clear=\"true\">\r\n        <Input type=\"text\" v-model=\"searchForm.whCode\" placeholder=\"仓库代码\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.country\" placeholder=\"目的国家\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"emailStatus\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.emailStatus\" placeholder=\"发送邮件\" style=\"width:160px\" :clear=\"true\">\r\n          <Option v-for=\"(item,index) in [{key:0,name:'否'},{key:1,name:'是'}]\" :value=\"item.key\" :key=\"index\">{{ item['name'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch\">查询</Button>\r\n        <Button style=\"margin-left:10px\" @click=\"handleReset()\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <Button type=\"primary\" :disabled=\"selectData.length === 0\" @click=\"sendEmail\">发送邮件</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"columns\" :data=\"data\" :loading=\"loading\" ref=\"selectTable\"\r\n           @on-select=\"handleSelectRow\"\r\n           @on-select-cancel=\"handleCancelRow\"\r\n           @on-select-all=\"handleSelectAll\"\r\n           @on-select-all-cancel=\"handleSelectAll\" :max-height=\"autoTableHeight($refs.selectTable,55)\" >\r\n      <template v-slot:consignor=\"{ row }\">\r\n        <span v-for=\"(item, index) in consignorList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['consignorId']\">{{ item['consignorName'] }}</span>\r\n      </template>\r\n      <template v-slot:consignee=\"{ row }\">\r\n        <span v-for=\"(item, index) in consigneeList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['consigneeId']\">{{ item['consigneeName'] }}</span>\r\n      </template>\r\n      <template v-slot:provider=\"{ row }\">\r\n        <span v-for=\"(item, index) in providerList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['providerId']\">{{ item['providerCode'] }}</span>\r\n      </template>\r\n      <template v-slot:providerChannel=\"{ row }\">\r\n        <span v-for=\"(item, index) in channelList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['channelId']\">{{ item['channelName'] }}</span>\r\n      </template>\r\n      <template v-slot:shipType=\"{ row }\">\r\n        <span v-for=\"(item, index) in shipTypeList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['id'] === row['shipType']\">{{ item['name'] }}</span>\r\n      </template>\r\n      <template v-slot:country=\"{ row }\">\r\n        <span v-for=\"(item, index) in countryList\" :key=\"index\" v-copytext=\"item\"\r\n              v-if=\"item['two_code'] === row['country']\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row,index}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"lookBill(row)\" style=\"margin:0 2px\">查看</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"backBill(row)\" style=\"margin:0 2px\">撤回</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'\r\n          :transfer=\"true\"></Page>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n    <ClearanceInfo ref=\"clearanceInfoRef\" :clearanceVisible=\"clearanceInfoVisible\" :onCancel=\"()=>clearanceInfoVisible=false\" :currencyList=\"currencyList\"\r\n                   :channelList=\"channelList\" :consignorList=\"consignorList\" :providerList=\"providerList\" :shipTypeList=\"shipTypeList\"  @onSuccess=\"handleSearch\"/>\r\n\r\n    <!-- 发送邮件弹窗 -->\r\n    <div v-if=\"clearanceEmailVisible\">\r\n      <CustomEmail ref=\"clearanceEmailRef\" :emailVisible=\"clearanceEmailVisible\" @emailCancel=\"()=>{this.clearanceEmailVisible=false}\"/>\r\n    </div>\r\n  </Card>\r\n</template>\r\n<script>\r\n\r\nimport {getYearDate, isEmpty} from '@/libs/tools'; // 引入非空判断方法\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport Common from \"@/api/basic/common\";\r\nimport Consignor from \"@/api/custom/consignor\";\r\nimport Consignee from \"@/api/custom/consignee\";\r\nimport Provider from \"@/api/logistics/provider\";\r\nimport ProviderChannel from \"@/api/logistics/providerChannel\";\r\nimport ClearanceInvoice from \"@/api/custom/clearanceInvoice\";\r\nimport WhAddress from \"@/api/custom/whAddress\";\r\nimport Currency from \"@/api/basf/currency\";\r\nimport ClearanceInfo from \"@/view/module/custom/clearance/clearanceInfo/index.vue\";\r\nimport CustomEmail from \"@/view/module/custom/custom/customInfo/customEmail.vue\";\r\nimport ProviderEmail from \"@/api/logistics/providerEmail\";\r\nimport {autoTableHeight} from '@/libs/tools'; // 引入非空判断方法\r\nexport default {\r\n  name: 'clearance',\r\n  components: {CustomEmail, LogModel,ClearanceInfo},\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      modal: false,\r\n      yesNoOps: Common.yesNoOps,\r\n      logVisible: false,\r\n      saving: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      loading: false,\r\n      selectData:[],\r\n      clearanceInfoVisible:false,\r\n      clearanceEmailVisible:false,\r\n      title: '',\r\n      form:{id:null,},\r\n      searchForm: {\r\n        startDate: null,\r\n        endDate: null,\r\n        emailStatus: null,\r\n        consignorId: null,\r\n        consigneeId: null,\r\n        providerId: null,\r\n        channelId: null,\r\n        customRank: null,\r\n        date: [],\r\n      },\r\n      date: {start: null, end: null},\r\n      columns: [{type: 'selection',width: 70,align: 'center',fixed:'left',},\r\n        {title: '发货日期',key: 'picDate',align: 'center',width: 100,},\r\n        {title: '平台单号',key: 'thdOrder',width: 150,tooltip:true,align: 'center'},\r\n        {title: '平台跟踪号',key: 'thdRef',width: 150,tooltip:true,align: 'center'},\r\n        {title: '国内发货人',key: 'consignorId',align: 'center',width: 110,slot:\"consignor\"},\r\n        {title: '国外收货人',key: 'consigneeId',align: 'center',width: 110,slot:\"consignee\"},\r\n        {title: '物流商',key: 'providerId',align: 'center',width: 110,slot:\"provider\"},\r\n        {title: '物流渠道',key: 'channelId',align: 'center',width: 110,slot:\"providerChannel\"},\r\n        {title: '物流方式',key: 'shipType',align: 'center',width: 120,slot:\"shipType\"},\r\n        {title: '清关国',key: 'country',align: 'center',width: 100,slot:\"country\"},\r\n        {title: '仓库地址',key: 'address',tooltip:true,align: 'center',width: 150,},\r\n        {title: '仓库代码',key: 'whCode',align: 'center',width: 110,},\r\n        {title: '总箱数',key: 'boxQty',align: 'center',width: 100,},\r\n        {title: '总毛重（千克）',key: 'grossWeight',width: 90,align: 'center'},\r\n        {title: '总数量',key: 'qty',align: 'center',width: 100,},\r\n        {title: '清关合并编号',key: 'clearanceRank',width: 90,align: 'center'},\r\n        {title: '是否已发邮件',key: 'emailStatus',width:110,align: 'center',\r\n          render:(h,params)=>{\r\n          return h('span',{},  this.yesNoOps.filter(item=>item['key'] === params.row.emailStatus).map(item=>item['name']).join())\r\n        }},\r\n        {title: '生成人员',key: 'createUser',align: 'center',width: 110,},\r\n        {title: '生成日期',key: 'createTime',align: 'center',width: 100,},\r\n        {title: '备注',key: 'remark',align: 'center',width: 100,},\r\n        {title: '操作',key: 'none',slot:'action',align: 'center',width: 150,fixed:'left'}],\r\n      data: [],\r\n      consignorList: [],\r\n      consigneeList: [],\r\n      providerList: [],\r\n      shipTypeList: [],\r\n      addressList: [],\r\n      channelList: [],\r\n      countryList: [],\r\n      currencyList:[],\r\n      businessType:1,\r\n      refType: null,\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getAllConsignor();\r\n    this.getAllConsignee();\r\n    this.getAllProvider();\r\n    this.getAllProviderChannel();\r\n    this.handleShipType();\r\n    this.getAllAddress();\r\n    this.getCountryList();\r\n    this.getLogRefType();\r\n    this.handleCurrency();\r\n  },\r\n  methods: {\r\n    sendEmail () {\r\n      const logisticsSet = new Set(this.selectData.map(item=>item['providerId']));\r\n      if(logisticsSet.size !== 1) return this.$Message.error(\"您好，您选择的数据存在不同货代，请重新选择同一个货代的数据！\");\r\n      const flag = this.selectData.every(item=> 0 === item['emailStatus']);\r\n      if(flag){\r\n        this.confirmSendEmail()\r\n      }else{\r\n        this.$Modal.confirm({\r\n          title: '提示！',\r\n          content: '您好，你选择得数据中已发送过邮件，请问是需要重新发送吗？',\r\n          onOk: () => {\r\n            this.confirmSendEmail()\r\n          }\r\n        });\r\n      }\r\n    },\r\n    confirmSendEmail(){\r\n      let providerId = this.selectData[0]['providerId'];\r\n      let country = this.selectData[0]['country'];\r\n      let templatePro = CommonApi.getDictionaryValueBy(\"email_config\",\"clearance_email_template\");\r\n      let providerEmailPro = ProviderEmail.getProviderEmail({\"parentId\":providerId,\"businessType\":this.businessType,\"country\":country});\r\n      let providerName = this.providerList.filter(item=>item['id'] === providerId).map(item=>item['providerName']).join();\r\n      let ids = this.selectData.map(item=>item['id']);\r\n      Promise.all([templatePro,providerEmailPro])\r\n        .then(res => {\r\n          //拼接主题\r\n          let title = providerName +`${getYearDate(new Date())}发票资料`;\r\n          let emailData = res[1]['data'];\r\n          let email = emailData['email'];\r\n          let ccEmail = emailData['ccEmail'];\r\n          //邮件模板内容\r\n          let template = res[0].data.replace(/\\n/g,'<br>');\r\n          this.clearanceEmailVisible = true;\r\n          setTimeout(()=>{\r\n            const {clearanceEmailRef} = this.$refs;\r\n            if (clearanceEmailRef) {\r\n              clearanceEmailRef.setDefault({\"email\":email,\"ccEmail\":ccEmail,\"title\":title,\"content\":template,\"type\":2,\"ids\":ids});\r\n            }\r\n          },500)\r\n        }).catch(() => {\r\n      })\r\n    },\r\n    handleCurrency() {\r\n      Currency.getAll().then(res => {\r\n        this.currencyList = res.data;\r\n      });\r\n    },\r\n    handleShipType() {\r\n      CommonApi.getDictionaryValueBy(\"logistics_base\", \"shipType\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shipTypeList = JSON.parse(res.data);\r\n        }\r\n      })\r\n    },\r\n    getAllAddress() {\r\n      WhAddress.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.addressList = res.data;\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n            this.handleSearch();\r\n          }\r\n        }\r\n      })\r\n    },\r\n    changeSelect(v, row) {\r\n      this.id = v ? row.id : null;\r\n      this.data.map(item => {\r\n        this.$set(item, 'single', false);\r\n        item['single'] = false;\r\n      })\r\n      if (this.id) {\r\n        this.data.filter(item => item['customRank'] === row['customRank'] && item['customStatus'] !== 1).map(item => {\r\n          this.$set(item, 'single', true);\r\n          item['single'] = true;\r\n        });\r\n      }\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo};\r\n      ClearanceInvoice.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.loading = false;\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n          this.selectData = [];\r\n        }\r\n      })\r\n    },\r\n    dateChange(date) {\r\n      if (isEmpty(date)) {\r\n        this.searchForm.startDate = '';\r\n        this.searchForm.endDate = '';\r\n      } else {\r\n        this.searchForm.startDate = date[0];\r\n        this.searchForm.endDate = date[1];\r\n      }\r\n    },\r\n    getAllConsignor() {\r\n      Consignor.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.consignorList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllConsignee() {\r\n      Consignee.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.consigneeList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllProvider() {\r\n      Provider.getAll({\"providerType\":\"大货物流商\"}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.providerList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getAllProviderChannel() {\r\n      ProviderChannel.getAll({}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.channelList = res.data;\r\n        }\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n      this.pageInfo={\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10\r\n      };\r\n    },\r\n    changeConsignor(v) {\r\n      if (!isEmpty(v)) {\r\n        this.consigneeForm.consignorName = v.label;\r\n        this.consigneeForm.parentId = v.value;\r\n      }\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      ClearanceInvoice.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    lookBill(row) {\r\n      const {clearanceInfoRef} = this.$refs;\r\n      if (clearanceInfoRef) {\r\n        clearanceInfoRef.setDefault(null, row['id']);\r\n      }\r\n      this.clearanceInfoVisible = true;\r\n    },\r\n    backBill(row) {\r\n      this.$Modal.confirm({\r\n        title: '提示',\r\n        content: '您确认要删除这条数据吗？',\r\n        onOk: () => {\r\n          ClearanceInvoice.remove({id: row.id}).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.$Message.success('删除成功!');\r\n              this.handleSearch();\r\n            }\r\n          })\r\n        },\r\n      })\r\n    },\r\n    //全选与取消全选\r\n    handleSelectAll (selection){\r\n      if (selection.length === 0) {\r\n        let data = this.$refs.selectTable.data\r\n        for (let i = 0; i < data.length; i++) {\r\n          for (let j = 0; j < this.selectData.length; j++) {\r\n            if(data[i].id === this.selectData[j].id){\r\n              this.selectData.splice(j,1)\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        for( const t of selection){\r\n          if(this.selectData.find(c=> c.id === t.id)){ continue;}\r\n          this.selectData.push(t)\r\n        }\r\n      }\r\n    },\r\n    //  选中某一行\r\n    handleSelectRow (selection,row) {\r\n      this.selectData.push(row);\r\n    },\r\n    //  取消某一行\r\n    handleCancelRow (selection,row) {\r\n      this.selectData.map((item,index)=>{\r\n        if(item.id === row.id){\r\n          this.selectData.splice(index,1)\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}