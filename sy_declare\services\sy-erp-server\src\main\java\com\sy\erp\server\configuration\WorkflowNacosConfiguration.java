package com.sy.erp.server.configuration;

import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngineConfiguration;
import org.activiti.engine.impl.cfg.StandaloneProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 工作流Nacos配置类 - 使用Nacos配置中心的数据源
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.cloud.nacos.config.enabled", havingValue = "true", matchIfMissing = false)
public class WorkflowNacosConfiguration {

    @Autowired(required = false)
    @Qualifier("dataSource")
    private DataSource dataSource;

    /**
     * 工作流引擎配置 - 使用Nacos配置的数据源
     */
    @Bean(name = "workflowSafeProcessEngine")
    @Primary
    public ProcessEngine processEngine() {
        try {
            log.info("🔄 开始初始化Activiti ProcessEngine（Nacos配置）");

            // 检查数据源是否可用
            if (dataSource == null) {
                log.warn("⚠️ 数据源未初始化，可能是Nacos配置未加载");
                return null; // 返回null，避免启动失败
            }

            log.info("📊 使用Spring Boot自动配置的数据源（来自Nacos workflow.properties）");

            StandaloneProcessEngineConfiguration configuration = new StandaloneProcessEngineConfiguration();

            // 直接使用Spring Boot配置的数据源
            configuration.setDataSource(dataSource);
            
            // 连接池配置
            configuration.setJdbcMaxActiveConnections(10);
            configuration.setJdbcMaxIdleConnections(5);
            configuration.setJdbcMaxCheckoutTime(20000);
            configuration.setJdbcMaxWaitTime(20000);
            
            // 基础配置
            configuration.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
            configuration.setHistory("full");
            configuration.setDbIdentityUsed(false);
            configuration.setAsyncExecutorActivate(false);
            
            // 引擎名称
            configuration.setProcessEngineName("ErpWorkflowEngine");
            
            ProcessEngine engine = configuration.buildProcessEngine();
            
            log.info("✅ Activiti ProcessEngine 初始化成功");
            log.info("📊 引擎名称: {}", engine.getName());
            log.info("🔧 数据源: 来自Nacos workflow.properties");
            
            return engine;
            
        } catch (Exception e) {
            log.error("❌ Activiti ProcessEngine 初始化失败: {}", e.getMessage());
            log.info("💡 工作流引擎初始化失败，但应用将继续启动");
            log.info("💡 请检查Nacos配置中心的workflow.properties配置");
            return null; // 返回null而不是抛异常，让应用继续启动
        }
    }

    /**
     * 仓库服务 - 安全版本
     */
    @Bean(name = "workflowSafeRepositoryService")
    public org.activiti.engine.RepositoryService repositoryService() {
        ProcessEngine engine = processEngine();
        return engine != null ? engine.getRepositoryService() : null;
    }

    /**
     * 运行时服务 - 安全版本
     */
    @Bean(name = "workflowSafeRuntimeService")
    public org.activiti.engine.RuntimeService runtimeService() {
        ProcessEngine engine = processEngine();
        return engine != null ? engine.getRuntimeService() : null;
    }

    /**
     * 任务服务 - 安全版本
     */
    @Bean(name = "workflowSafeTaskService")
    public org.activiti.engine.TaskService taskService() {
        ProcessEngine engine = processEngine();
        return engine != null ? engine.getTaskService() : null;
    }

    /**
     * 历史服务 - 安全版本
     */
    @Bean(name = "workflowSafeHistoryService")
    public org.activiti.engine.HistoryService historyService() {
        ProcessEngine engine = processEngine();
        return engine != null ? engine.getHistoryService() : null;
    }

    /**
     * 管理服务 - 安全版本
     */
    @Bean(name = "workflowSafeManagementService")
    public org.activiti.engine.ManagementService managementService() {
        ProcessEngine engine = processEngine();
        return engine != null ? engine.getManagementService() : null;
    }
}
