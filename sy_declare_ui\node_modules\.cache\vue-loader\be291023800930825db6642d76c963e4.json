{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\dictionary\\index.vue", "mtime": 1752737748510}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2IA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/view/module/base/dictionary", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card :shadow=\"true\">\r\n      <Form ref=\"formSearch1\" class=\"searchForm\" :model=\"formQuery1\" inline\r\n            @keydown.enter.native=\"handleSearch('formSearch1')\">\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" v-model=\"formQuery1.name\" placeholder=\"请输入字典名称\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"code\">\r\n          <Input type=\"text\" v-model=\"formQuery1.code \" placeholder=\"请输入字典编号\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"formQuery1.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\"\r\n                  :transfer=\"true\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" @click=\"handleSearch('formSearch1')\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('formSearch1')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top \">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal()\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data1\" :loading=\"loading1\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <a @click=\"handleModal(row)\" v-if=\"hasAuthority('dictionaryEdit')\">编辑</a>&nbsp;\r\n          <a @click=\"handleSet(row)\" v-if=\"hasAuthority('dictionarySet')\">字典配置</a>&nbsp;\r\n          <a @click=\"handleClick('remove',row,'formSearch1')\" v-if=\"hasAuthority('dictionaryDel')\">删除</a>&nbsp;\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total1\" size=\"small\" :current=\"formQuery1.page\" :page-size=\"formQuery1.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage1\"\r\n            @on-page-size-change=\"handlePageSize1\"></Page>\r\n    </Card>\r\n    <!-- 数据字典添加与编辑 -->\r\n    <Modal v-model=\"modalVisible1\" :title=\"modalTitle1\" :width=\"500\" @on-cancel=\"handleReset('form1')\">\r\n      <Form ref=\"form1\" :model=\"formItem1\" :rules=\"formItemRules1\" :label-width=\"100\" class=\"dicListForm\">\r\n        <FormItem label=\"字典编号\" prop=\"code\">\r\n          <Input v-model=\"formItem1.code\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"字典名称\" prop=\"name\">\r\n          <Input v-model=\"formItem1.name\" :maxlength=\"50\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem1.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem1.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"\">\r\n        <Button type=\"default\" @click=\"handleReset('form1')\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit1()\" :loading=\"saving1\">保存</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 配置字典列表 -->\r\n    <Modal v-model=\"modalVisible2\" title=\"字典列表\" :width=\"900\" @on-cancel=\"handleReset('formSearch2')\"\r\n           class=\"dicListModal\">\r\n      <Form ref=\"formSearch2\" :model=\"formQuery2\" inline :label-width=\"40\"\r\n            @keydown.enter.native=\"handleSearch('formSearch2')\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formQuery2.name\" :maxlength=\"50\" placeholder=\"请输入内容\" style=\"width:200px\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"status\">\r\n          <Select v-model=\"formQuery2.status\" style=\"width:200px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem>\r\n          <Button type=\"primary\" style=\"margin-right:15px;\" @click=\"handleSearch('formSearch2')\">查询</Button>\r\n          <Button @click=\"handleResetForm('formSearch2')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"margin-top: 8px\">\r\n        <Button class=\"search-btn\" type=\"primary\" @click=\"handleModal2('add')\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :columns=\"valueColumns\" :data=\"data2\" :loading=\"loading2\" :border=\"true\" :max-height=\"560\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key ===0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row, index }\">\r\n          <Button type=\"text\" @click=\"handleModal2('edit',row)\">编辑</Button>\r\n          <Button type=\"text\" @click=\"handleClick('remove',row,'formSearch2')\">删除</Button>\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"total2\" :current=\"formQuery2.page\" :page-size=\"formQuery2.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage2\"\r\n            @on-page-size-change=\"handlePageSize2\"></Page>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('formSearch2')\">关闭</Button>\r\n      </template>\r\n    </Modal>\r\n    <!-- 添加与编辑字典列表 -->\r\n    <Modal v-model=\"modalVisible3\" :title=\"modalTitle2\" :width=\"500\" @on-cancel=\"handleReset('form2')\">\r\n      <Form ref=\"form2\" :model=\"formItem2\" :rules=\"formItemRules2\" :label-width=\"80\" class=\"dicListForm\">\r\n        <FormItem label=\"名称\" prop=\"name\">\r\n          <Input v-model=\"formItem2.name\" :maxlength=\"100\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"数据值\" prop=\"value\">\r\n          <Input v-model=\"formItem2.value\" :maxlength=\"5000\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"描述\" prop=\"description\">\r\n          <Input v-model=\"formItem2.description\" :autosize=\"{minRows: 2,maxRows: 6}\" type=\"textarea\"\r\n                 placeholder=\"请输入\"/>\r\n        </FormItem>\r\n        <FormItem label=\"排序值\" prop=\"sort\">\r\n          <InputNumber :min=\"1\" v-model=\"formItem2.sort\"></InputNumber> &nbsp;<span>值越小越靠前,支持小数</span>\r\n        </FormItem>\r\n        <FormItem label=\"是否启用\" prop=\"status\">\r\n          <i-switch size=\"large\" v-model=\"formItem2.status\" :true-value=\"0\" :false-value=\"1\">\r\n            <span slot=\"open\">开启</span>\r\n            <span slot=\"close\">关闭</span>\r\n          </i-switch>\r\n        </FormItem>\r\n      </Form>\r\n      <template v-slot:footer=\"{}\">\r\n        <Button type=\"default\" @click=\"handleReset('form2')\">关闭</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit2()\" :loading=\"saving2\" v-if=\"hasAuthority('dictionarySet')\">确定</Button>\r\n      </template>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Common from '@/api/basic/common'\r\nimport Dictionary from '@/api/base/dictionary'\r\nimport {autoTableHeight} from \"@/libs/tools.js\"\r\n\r\nexport default {\r\n  name: 'dict',\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.statusOps,\r\n\r\n      modalTitle1: '添加字典',\r\n      modalTitle2: '添加字典值',\r\n      state1: 'add',//字典操作\r\n      state2: 'add',//字典值操作\r\n      loading1: false,\r\n      loading2: false,\r\n      saving1: false,\r\n      saving2: false,\r\n      modalVisible1: false,//字典新增修改弹框\r\n      modalVisible2: false,//字典明细弹框\r\n      modalVisible3: false,//字典值新增修改弹框\r\n      data1: [],\r\n      data2: [],\r\n      total1: 0,\r\n      total2: 0,\r\n      selectRow: {},\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 80,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '字典名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '字典编号',\r\n          key: 'code',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.code}>{row.code}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 300,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      valueColumns: [\r\n        {\r\n          title: '名称',\r\n          key: 'name',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.name}>{row.name}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '数据值',\r\n          key: 'value',\r\n          align: 'center',\r\n          render: (h, {row}) => {\r\n            return <span v-copytext={row.value}>{row.value}</span>\r\n          },\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'description',\r\n          align: 'center',\r\n          tooltip: true\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          width: 100,\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 150,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      formQuery1: {\r\n        name: '',\r\n        code: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formQuery2: {\r\n        parentId: null,\r\n        name: '',\r\n        status: -1,\r\n        page: 1,\r\n        limit: 10\r\n      },\r\n      formItem2: {\r\n        id: '',\r\n        parentId: '',\r\n        name: '',\r\n        value: '',\r\n        description: '',\r\n        status: 0,\r\n        sort: 1\r\n      },\r\n      formItem1: {\r\n        id: '',\r\n        code: '',\r\n        name: '',\r\n        description: '',\r\n        status: 0\r\n      },\r\n      formItemRules1: {\r\n        code: [\r\n          {required: true, message: '字典编号不能为空', trigger: 'blur'}\r\n        ],\r\n        name: [\r\n          {required: true, message: '字典名称不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n      formItemRules2: {\r\n        name: [\r\n          {required: true, message: '字典值名称不能为空', trigger: 'blur'}\r\n        ],\r\n        value: [\r\n          {required: true, message: '字典值名称对应的值不能为空', trigger: 'blur'}\r\n        ]\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 数据字典添加与编辑\r\n     */\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.state1 = 'edit';\r\n        this.modalTitle1 = '编辑字典';\r\n        this.formItem1 = Object.assign({}, this.formItem1, data);\r\n      } else {\r\n        this.state1 = 'add';\r\n        this.modalTitle1 = '添加字典';\r\n        this.formItem1 = {};\r\n      }\r\n      this.modalVisible1 = true;\r\n    },\r\n    /**\r\n     * 数据字典值添加与编辑\r\n     */\r\n    handleModal2(action,data) {\r\n      this.state2 = action;\r\n      if (this.state2 === 'edit') {\r\n        this.modalTitle2 = '编辑字典值';\r\n        this.formItem2 = Object.assign({}, this.formItem2, data);\r\n      } else {\r\n        this.modalTitle2 = '添加字典值';\r\n        this.formItem2 = {id: '',\r\n          parentId: '',\r\n          name: '',\r\n          value: '',\r\n          description: '',\r\n          status: 0,\r\n          sort: 1};\r\n      }\r\n      this.formItem2.parentId = this.selectRow.id\r\n      this.modalVisible3 = true;\r\n    },\r\n    /**\r\n     * 查询，重置\r\n     */\r\n    handleSearch(name) {\r\n      if (name === 'formSearch1') {\r\n        this.loading1 = true;\r\n        Dictionary.listPage(this.formQuery1).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total1 = parseInt(res.data.total);\r\n            this.data1 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading1 = false\r\n        });\r\n      } else {\r\n        this.loading2 = true;\r\n        this.formQuery2.parentId = this.selectRow.id;\r\n        Dictionary.listValuePage(this.formQuery2).then(res => {\r\n          if (res[\"code\"] === 0) {\r\n            this.total2 = parseInt(res.data.total);\r\n            this.data2 = res.data.records;\r\n          }\r\n        }).finally(() => {\r\n          this.loading2 = false\r\n        });\r\n      }\r\n    },\r\n    handleSet(row) {\r\n      this.modalVisible2 = true\r\n      this.selectRow = row\r\n      this.handleResetForm(\"formSearch2\");\r\n    },\r\n    handleResetForm(name) {\r\n      if(name === \"formSearch1\"){\r\n        this.$refs[\"formSearch1\"].resetFields();\r\n      }else{\r\n        this.$refs[\"formSearch2\"].resetFields();\r\n      }\r\n      this.handleSearch(name, 1, 10);\r\n    },\r\n    handleRemove(data, type) {\r\n      this.$Modal.confirm({\r\n        title: '提示！',\r\n        content: '你确定要删除这条数据吗',\r\n        onOk: () => {\r\n          if (type === 'formSearch1') {\r\n            Dictionary.remove(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch1')\r\n              }\r\n            });\r\n          } else {\r\n            Dictionary.removeValue(data.id).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.$Message.success('删除成功!');\r\n                this.handleSearch('formSearch2')\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleClick(name,row, type) {\r\n      switch (name) {\r\n        case 'remove':\r\n          this.handleRemove(row, type)\r\n          break\r\n      }\r\n    },\r\n    handleSubmit1() {\r\n      this.saving1 = true;\r\n      this.$refs['form1'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state1 === 'add') {\r\n            Dictionary.add(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          } else {\r\n            Dictionary.edit(this.formItem1).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form1\");\r\n                this.$Message.success('编辑成功!');\r\n                this.handleSearch(\"formSearch1\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving1 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving1 = false\r\n        }\r\n      });\r\n    },\r\n    handleSubmit2() {\r\n      this.saving2 = true;\r\n      this.$refs['form2'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.state2 === 'add') {\r\n            Dictionary.addValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          } else {\r\n            Dictionary.editValue(this.formItem2).then(res => {\r\n              if (res[\"code\"] === 0) {\r\n                this.handleReset(\"form2\");\r\n                this.$Message.success('保存成功!');\r\n                this.handleSearch(\"formSearch2\")\r\n              }\r\n            }).finally(() => {\r\n              this.saving2 = false\r\n            })\r\n          }\r\n        } else {\r\n          this.saving2 = false\r\n        }\r\n      });\r\n    },\r\n\r\n    handleReset(name) {\r\n      if (name === 'form1') {\r\n        this.modalVisible1 = false;\r\n        this.saving1 = false\r\n        this.$refs['form1'].resetFields();\r\n      } else if (name === 'form2') {\r\n        this.saving3 = false\r\n        this.modalVisible3 = false;\r\n        this.$refs['form2'].resetFields();\r\n      } else if (name === 'formSearch2') { // name=formQuery2\r\n        this.modalVisible2 = false;\r\n        this.saving2 = false\r\n        this.$refs['formSearch2'].resetFields();\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage1(page) {\r\n      this.formQuery1.page = page;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    handlePageSize1(size) {\r\n      this.formQuery1.limit = size;\r\n      this.handleSearch('formSearch1')\r\n    },\r\n    /**\r\n     * 分页\r\n     */\r\n    handlePage2(page) {\r\n      this.formQuery2.page = page;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n    handlePageSize2(size) {\r\n      this.formQuery2.limit = size;\r\n      this.handleSearch('formSearch2')\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handlePage1(1)\r\n  }\r\n\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n\r\n.dicListModal {\r\n  .ivu-modal-body {\r\n    min-height: 450px;\r\n\r\n    .ivu-table-cell {\r\n      padding-left: 4px;\r\n      padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n.dicListForm {\r\n  padding-right: 25px;\r\n\r\n  textarea {\r\n    resize: none;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}