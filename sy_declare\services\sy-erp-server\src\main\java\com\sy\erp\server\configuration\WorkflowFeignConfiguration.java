package com.sy.erp.server.configuration;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 工作流专用的 Feign 配置
 * 解决 Activiti JavaDelegate 中 Feign 调用缺少认证信息的问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class WorkflowFeignConfiguration {

    @Value("${aimo.client.oauth2.admin.client-id:7gBZcbsC7kLIWCdELIl8nxcs}")
    private String clientId;

    @Value("${aimo.client.oauth2.admin.client-secret:0osTIhce7uPvDKHz6aa67bhCukaKoYl4}")
    private String clientSecret;

    /**
     * 工作流专用的 Feign 请求拦截器
     * 当没有 HTTP 请求上下文时，使用系统级别的认证信息
     */
    @Bean
    public RequestInterceptor workflowFeignRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                try {
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    if (attributes != null) {
                        HttpServletRequest request = attributes.getRequest();
                        String authorization = request.getHeader("Authorization");
                        if (authorization != null) {
                            template.header("Authorization", authorization);
                            return;
                        }
                    }
                    template.header("noAuth", "true");
                } catch (Exception e) {
                    template.header("noAuth", "true");
                }
            }
        };
    }

    /**
     * 获取系统级别的访问令牌
     * 这里可以实现具体的系统认证逻辑
     */
    private String getSystemToken() {
        return null;
    }
}
