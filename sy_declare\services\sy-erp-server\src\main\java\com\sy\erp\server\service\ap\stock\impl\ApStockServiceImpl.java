package com.sy.erp.server.service.ap.stock.impl;

import com.aimo.common.mybatis.base.service.impl.BaseServiceImpl;
import com.aimo.common.utils.CollectionUtils;
import com.aimo.common.utils.DateUtils;
import com.aimo.common.utils.StringUtils;
import com.sy.erp.client.constant.ErpConstant;
import com.sy.erp.client.model.stock.StockInfo;
import com.sy.erp.client.param.DateRangeParam;
import com.sy.erp.client.param.OutInParam;
import com.sy.erp.client.param.PurOrderParam;
import com.sy.erp.client.param.SaleOrderParam;
import com.sy.erp.client.vo.OutInResultVo;
import com.sy.erp.client.vo.PurOrderVo;
import com.sy.erp.client.vo.SaleOrderVo;
import com.sy.erp.server.configuration.DynamicDataSourceContextHolder;
import com.sy.erp.server.mapper.ap.stock.ApStockMapper;
import com.sy.erp.server.service.ap.stock.ApStockService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class ApStockServiceImpl extends BaseServiceImpl<ApStockMapper, StockInfo> implements ApStockService {
    @Resource
    private ApStockMapper apStockMapper;

    public List<StockInfo> getApStock(Integer accountType, List<String> itemList) {
        List<StockInfo> allStockList = new ArrayList<>();
        (accountType == null ? Arrays.asList(ErpConstant.ACCOUNT_XW2020, ErpConstant.ACCOUNT_A2019) : Collections.singletonList(accountType)).forEach(item -> {
//            if (ErpConstant.ACCOUNT_XW2020 == item) {
//                DynamicDataSourceContextHolder.markXW2020Sqlserver();
//            } else if (ErpConstant.ACCOUNT_A2019 == item) {
//                DynamicDataSourceContextHolder.markA2019Sqlserver();
//            }
            List<StockInfo> stockList = apStockMapper.getApStock(itemList);
            if (!CollectionUtils.isEmpty(stockList)) {
                stockList.forEach(stockInfo -> stockInfo.setAccountType(item));
            }
            allStockList.addAll(stockList);
        });
        return allStockList;
    }

    public List<OutInResultVo> getOutInList(OutInParam outInParam) {
        setDefaultDate(outInParam);
        List<OutInResultVo> allResultVoList = new ArrayList<>();
        (StringUtils.toInteger(outInParam.getAccountType(), 0) == 0 ? Arrays.asList(ErpConstant.ACCOUNT_XW2020, ErpConstant.ACCOUNT_A2019) : Collections.singletonList(outInParam.getAccountType())).forEach(item -> {
//            if (ErpConstant.ACCOUNT_XW2020 == item) {
//                DynamicDataSourceContextHolder.markXW2020Sqlserver();
//            } else if (ErpConstant.ACCOUNT_A2019 == item) {
//                DynamicDataSourceContextHolder.markA2019Sqlserver();
//            }
            List<OutInResultVo> resultVoList = apStockMapper.getOutInList(outInParam);
            if (!CollectionUtils.isEmpty(resultVoList)) {
                resultVoList.forEach(each -> each.setAccount(item));
            }
            allResultVoList.addAll(resultVoList);
        });
        return allResultVoList;
    }

    public List<PurOrderVo> getPurOrderList(PurOrderParam purOrderParam) {
        List<PurOrderVo> allResultVoList = new ArrayList<>();
        (StringUtils.toInteger(purOrderParam.getAccountType(), 0) == 0 ? Arrays.asList(ErpConstant.ACCOUNT_XW2020, ErpConstant.ACCOUNT_A2019) : Collections.singletonList(purOrderParam.getAccountType())).forEach(item -> {
//            if (ErpConstant.ACCOUNT_XW2020 == item) {
//                DynamicDataSourceContextHolder.markXW2020Sqlserver();
//            } else if (ErpConstant.ACCOUNT_A2019 == item) {
//                DynamicDataSourceContextHolder.markA2019Sqlserver();
//            }
            List<PurOrderVo> resultVoList = apStockMapper.getPurOrderList(purOrderParam);
            if (!CollectionUtils.isEmpty(resultVoList)) {
                resultVoList.forEach(each -> each.setAccount(item));
            }
            allResultVoList.addAll(resultVoList);
        });
        return allResultVoList;
    }

    public List<SaleOrderVo> getSaleOrderList(SaleOrderParam saleOrderParam) {
        setDefaultDate(saleOrderParam);
        List<SaleOrderVo> allResultVoList = new ArrayList<>();
        (StringUtils.toInteger(saleOrderParam.getAccountType(), 0) == 0 ? Arrays.asList(ErpConstant.ACCOUNT_XW2020, ErpConstant.ACCOUNT_A2019) : Collections.singletonList(saleOrderParam.getAccountType())).forEach(item -> {
//            if (ErpConstant.ACCOUNT_XW2020 == item) {
//                DynamicDataSourceContextHolder.markXW2020Sqlserver();
//            } else if (ErpConstant.ACCOUNT_A2019 == item) {
//                DynamicDataSourceContextHolder.markA2019Sqlserver();
//            }
            List<SaleOrderVo> resultVoList = apStockMapper.getSaleOrderList(saleOrderParam);
            if (!CollectionUtils.isEmpty(resultVoList)) {
                resultVoList.forEach(each -> each.setAccount(item));
            }
            allResultVoList.addAll(resultVoList);
        });
        return allResultVoList;
    }

    private void setDefaultDate(DateRangeParam dateRangeParam) {
        if (StringUtils.isEmpty(dateRangeParam.getStartDate())) {
            dateRangeParam.setStartDate(DateUtils.formatDate(DateUtils.addDays(DateUtils.getCurrentDate(), -30), DateUtils.DATE_TYPE_YMD));
        }
        if (StringUtils.isEmpty(dateRangeParam.getEndDate())) {
            dateRangeParam.setEndDate(DateUtils.formatDate(DateUtils.addDays(DateUtils.getCurrentDate(), 1), DateUtils.DATE_TYPE_YMD));
        }
    }
}
