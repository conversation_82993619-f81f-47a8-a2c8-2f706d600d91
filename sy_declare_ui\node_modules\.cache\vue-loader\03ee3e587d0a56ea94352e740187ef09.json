{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\clearanceLink\\index.vue?vue&type=style&index=0&id=5c254d17&lang=less&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\clearanceLink\\index.vue", "mtime": 1752737748520}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1752744819993}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoud2lkdGhDbGFzcyB7DQogIHdpZHRoOiAzNTBweA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwYA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/clearanceLink", "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 报关清关地址维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline>\r\n      <FormItem prop=\"spuName\">\r\n        <Input v-model=\"searchForm.spuName\" placeholder=\"请输入产品型号\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\">\r\n        <Select v-model=\"searchForm.country\" filterable clearable placeholder=\"请选择国家\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"isAuto\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.isAuto\" placeholder=\"更新形式\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"addClearanceLink\">新增</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"reCalcLink\">重新匹配</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"templateExport\">导入模板</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"clearanceLinkExport\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"column\" :data=\"data\" :loading=\"loading\" ref=\"selectTable\"\r\n           @on-select=\"handleSelectRow\"\r\n           @on-select-cancel=\"handleCancelRow\"\r\n           @on-select-all=\"handleSelectAll\"\r\n           @on-select-all-cancel=\"handleSelectAll\">\r\n      <template v-slot:photoUrl=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n            {{ row['photoUrl']}}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"min-width: 300px\" v-copytext=\"row['photoUrl']\">\r\n            {{row['photoUrl'].length>50?(row['photoUrl'].substring(0,50)+\"...\"):row['photoUrl'] }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:goodsUrl=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n            {{ row['goodsUrl']}}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"min-width: 300px\" v-copytext=\"row['goodsUrl']\">\r\n            {{row['goodsUrl'].length>50?(row['goodsUrl'].substring(0,50)+\"...\"):row['goodsUrl'] }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:price=\"{row}\">\r\n        <span v-if=\"row['price'] && row['currency']\">{{ row['price']+\"(\"+row['currency']+\")\"}}</span>\r\n        <span v-else>{{ row['price']||0}}</span>\r\n      </template>\r\n      <template v-slot:isAuto=\"{row}\">\r\n        <span v-for=\"item in statusList\" v-if=\"item['key'] === row['isAuto']\">{{ item['value'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editClearanceLink(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"lookLog(row)\" style=\"margin:0 2px\">日志</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem prop=\"className\" label=\"报关类目\">\r\n          <div class=\"widthClass\">\r\n            <treeselect v-model=\"form.parentId\"\r\n                        :options=\"classNameList\"\r\n                        :disabled=\"disabled\"\r\n                        @input=\"changeNameCn\"\r\n                        :default-expand-level=\"1\"\r\n                        noResultsText=\"暂无数据\"\r\n                        placeholder=\"请选清关品名\" />\r\n          </div>\r\n        </FormItem>\r\n        <FormItem label=\"国家\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"form.country\" filterable clearable placeholder=\"请选择所在国家\" class=\"widthClass\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"shopId\" :clear=\"true\" label=\"店铺\">\r\n          <Select type=\"text\" v-model=\"form.shopId\" placeholder=\"店铺\" class=\"widthClass\" >\r\n            <Option v-for=\"(item,index) in shopList\" :value=\"item.id\" :key=\"index\">{{ item.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"销售SKU\" prop=\"sellerSku\">\r\n          <Input v-model.trim=\"form.sellerSku\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"更新形式\">\r\n          <RadioGroup v-model=\"form.isAuto\" type=\"button\">\r\n            <Radio v-for=\"v in statusList\" :label=\"v.key\" v-bind:key=\"v.key\">{{ v['value'] }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveClearanceLink\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport ClearanceLink from \"@/api/custom/clearanceLink\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nimport {listAllSpu} from '@/api/basf/product.js'\r\nimport Shop from \"@/api/basf/shop\";\r\n\r\nexport default {\r\n  components: {LogModel},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      logVisible: false,\r\n      refType: null,\r\n      selectData:[],\r\n      title: '',\r\n      statusList:[{key:0,value:\"自动\"},{key:1,value:\"手动\"}],\r\n      searchForm: {spuName: '', country: '', isAuto:''},\r\n      pageInfo: {total: 0, page: 1, limit: 10},\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/clearanceLink/importFile\",\r\n      form: {parentId: null, country: null, sellerSku: null, shopId: null},\r\n      data: [],\r\n      classNameList:[],\r\n      shopList:[],\r\n      column: [\r\n        {title: '选项',type: 'selection',width: 70,align: 'center',fixed:'left',},\r\n        {title: '产品型号', key: 'spuName', minWidth: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.spuName}>{row.spuName}</span>}},\r\n        {title: '清关国', key: 'country', width: 100, align: 'center', slot: 'country'},\r\n        {title: '平台', key: 'platformId', width: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.platformId === '1'?'AMAZON':'WALMART'}>{row.platformId === '1'?'AMAZON':'WALMART'}</span>}},\r\n        {title: '网店', key: 'shopName', width: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.shopName}>{row.shopName}</span>}},\r\n        {title: 'asin', key: 'asin', width: 120, align: 'center',render: (h, {row}) => {return <span v-copytext={row.asin}>{row.asin}</span>}},\r\n        {title: 'sellerSku', key: 'sellerSku', minWidth: 150, align: 'center',render: (h, {row}) => {return <span v-copytext={row.sellerSku}>{row.sellerSku}</span>}},\r\n        {title: '销售价', key: 'price', width: 120, align: 'center',slot:'price'},\r\n        {title: '商品链接', key: 'goodsUrl', minWidth: 250, align: 'center',slot:'goodsUrl'},\r\n        {title: '商品图片', key: 'photoUrl', minWidth: 300, align: 'center',slot:'photoUrl'},\r\n        {title: '更新形式', key: 'isAuto', width: 100, align: 'center',slot:'isAuto'},\r\n        {title: '操作', key: 'action', width: 150, align: 'center', slot: 'action'}],\r\n      countryList: [],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getClassList();\r\n    this.getAllShop();\r\n    this.getLogRefType();\r\n  },\r\n  methods: {\r\n    getAllShop() {\r\n      Shop.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shopList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getClassList(){\r\n      listAllSpu({}).then((res) => {\r\n        if (res['code'] === 0) {\r\n          this.classNameList =res.data;\r\n          this.diGuiTree(this.classNameList)\r\n        }\r\n      })\r\n    },\r\n    diGuiTree(item) {  //递归便利树结构\r\n      item.forEach(item => {\r\n        item.id = item['id'];\r\n        item.label = item['spuName'];\r\n        !item['children'] || item['children'].length === 0 ? delete item.children : this.diGuiTree(item.children);\r\n      })\r\n    },\r\n    //全选与取消全选\r\n    handleSelectAll (selection){\r\n      if (selection.length === 0) {\r\n        let data = this.$refs.selectTable.data\r\n        for (let i = 0; i < data.length; i++) {\r\n          for (let j = 0; j < this.selectData.length; j++) {\r\n            if(data[i].id === this.selectData[j].id){\r\n              this.selectData.splice(j,1)\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        for( const t of selection){\r\n          if(this.selectData.find(c=> c.id === t.id)){ continue;}\r\n          this.selectData.push(t)\r\n        }\r\n      }\r\n    },\r\n    //  选中某一行\r\n    handleSelectRow (selection,row) {\r\n      this.selectData.push(row);\r\n    },\r\n    //  取消某一行\r\n    handleCancelRow (selection,row) {\r\n      this.selectData.map((item,index)=>{\r\n        if(item.id === row.id){\r\n          this.selectData.splice(index,1)\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n    changeNameCn(v){\r\n      if(v === undefined){\r\n        this.form.nameCn =''\r\n        this.form.nameEn = ''\r\n      }else{\r\n        this.form.nameCn =v.split('*-*')[0];\r\n        this.form.nameEn = v.split('*-*')[1];\r\n      }\r\n    },\r\n    reCalcLink(){\r\n      let ids = \"\";\r\n      if(this.selectData && this.selectData.length >0){\r\n        ids = this.selectData.map(item=>item['id']).join(',');\r\n      }\r\n      this.loading = true;\r\n      ClearanceLink.reCalcLink({\"ids\":ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('重新匹配');\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo}\r\n      ClearanceLink.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    templateExport(){\r\n      this.loading = true;\r\n      ClearanceLink.downloadTemplate({\"fileName\":\"清关连接导入模板.xls\"}, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    clearanceLinkExport() {\r\n      let params = {...this.searchForm};\r\n      params['fileName'] = \"清关连接\" + new Date().getTime() + \".xls\";\r\n      this.loading = true;\r\n      ClearanceLink.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    editClearanceLink(row) {\r\n      this.title = \"修改\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.form = Object.assign({}, row);\r\n    },\r\n    addClearanceLink() {\r\n      this.title = \"添加\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n\r\n    saveClearanceLink() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          ClearanceLink.saveClearanceLink(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields();\r\n      this.form = {};\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      ClearanceLink.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"]}]}