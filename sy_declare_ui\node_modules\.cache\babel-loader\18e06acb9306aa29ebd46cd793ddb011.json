{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\basf\\shop\\index.vue", "mtime": 1752737748516}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\.babelrc", "mtime": 1752737748316}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["personSelect", "Shop", "Platform", "Site", "autoTableHeight", "Common", "name", "components", "data", "h", "$createElement", "statusOps", "userStatusOps", "relateOps", "actionType", "personVisible", "loading", "saving", "modalVisible", "modalViewVisible", "shopNameShow", "siteRule", "required", "readName", "modalTitle", "platformList", "selectTreeData", "siteList", "<PERSON><PERSON><PERSON><PERSON>", "pageInfo", "total", "page", "limit", "status", "relate", "platformId", "siteId", "aliaName", "formItemRules", "message", "trigger", "departmentId", "formItem", "id", "platform", "site", "erpCustNo", "managedUserName", "phone", "remark", "columns", "type", "max<PERSON><PERSON><PERSON>", "title", "key", "min<PERSON><PERSON><PERSON>", "align", "width", "render", "_", "_ref", "row", "value", "slot", "_ref2", "sid", "sortable", "_ref3", "createTime", "updateTime", "fixed", "usersOptions", "allDepartments", "methods", "treeSelectNormalizer", "node", "label", "departmentName", "children", "onPersonCancel", "<PERSON><PERSON><PERSON>", "_this", "personArr", "arguments", "length", "undefined", "personIds", "map", "item", "push", "ArrayIsEqual", "addShopUsers", "userIds", "join", "then", "res", "$Message", "success", "error", "arr1", "arr2", "i", "syncShop", "_this2", "handleSearch", "finally", "handleModal", "Object", "assign", "handleView", "handleReset", "form", "$refs", "resetFields", "getDepartNames", "departmentIds", "results", "for<PERSON>ach", "find", "v", "handleSubmit", "_this3", "validate", "valid", "params", "_objectSpread", "toString", "trim", "operateUser", "userId", "managedUser", "nick<PERSON><PERSON>", "edit", "add", "handleResetForm", "deptId", "_this4", "listPage", "records", "parseInt", "handlePage", "current", "handlePageSize", "size", "handleRemove", "_this5", "modal", "$Modal", "confirm", "onOk", "remove", "handlePlatform", "_this6", "getAll", "unshift", "handleSite", "_this7", "change", "val", "_this8", "getByPlatformId", "onSortChange", "_ref4", "order", "sort", "platformChange", "pfId", "_this9", "handleClick", "setSelectInfo", "info", "open<PERSON>erson", "personSelectRef", "selectedId", "<PERSON><PERSON><PERSON><PERSON>", "filter", "mounted"], "sources": ["src/view/module/basf/shop/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card class=\"shopManage\" :shadow=\"true\">\r\n      <Form ref=\"searchForm\" class=\"searchForm\" :model=\"pageInfo\" inline @submit.native.prevent>\r\n        <FormItem prop=\"platformId\">\r\n          <Select type=\"text\" v-model=\"pageInfo.platformId\" placeholder=\"平台名称\" @on-change=\"platformChange\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"siteId\">\r\n          <Select v-model=\"pageInfo.siteId\" placeholder=\"站点名称\" style=\"width:160px\" >\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"name\">\r\n          <Input type=\"text\" placeholder=\"店铺名称\" v-model=\"pageInfo.name\" />\r\n        </FormItem>\r\n        <FormItem prop=\"aliaName\">\r\n          <Input type=\"text\" placeholder=\"店铺编号\" v-model=\"pageInfo.aliaName\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" placeholder=\"店铺状态\" style=\"width:160px\">\r\n            <Option v-for=\"v in statusOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"relate\">\r\n          <Select v-model=\"pageInfo.relate\" placeholder=\"关联领星\" style=\"width:160px\">\r\n            <Option v-for=\"v in relateOps\" :value=\"v.key\" :key=\"v.key\">{{v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem class=\"rightBtn\" :label-width=\"20\">\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>&nbsp;\r\n          <Button @click=\"handleResetForm('searchForm')\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\" style=\"padding-bottom: 10px\">\r\n        <Button type=\"primary\" @click=\"handleModal()\" v-if=\"hasAuthority('shopAdd')\">添加</Button>\r\n        <Button @click=\"syncShop()\" style=\"margin-left: 15px\" :loading=\"loading\" v-if=\"hasAuthority('shopSync')\">同步</Button>\r\n      </div>\r\n      <Table :border=\"true\" ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\" :columns=\"columns\"\r\n             :data=\"data\" :loading=\"loading\">\r\n        <template v-slot:status=\"{ row }\">\r\n          <Badge v-for=\"v in statusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleModal(row)\">编辑</a>&nbsp;\r\n          <a @click=\"handleClick('view',row)\">查看</a>&nbsp;\r\n          <a v-if=\"hasAuthority('shopEdit')\" @click=\"handleClick('remove',row)\">{{row.status === 0?\"停用\":(row.status === 1?\"启用\":\"解锁\")}}</a>\r\n          <!--<a @click=\"handleAuth(row)\">AWM授权</a>&nbsp;-->\r\n        </template>\r\n      </Table>\r\n      <Page :total=\"pageInfo.total\" size=\"small\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\" class-name=\"shopManageEditModal\">\r\n      <Form ref=\"form\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"platformId\">\r\n          <Select v-model=\"formItem.platformId\" @on-change=\"change\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in platformList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteId\" :rules=\"siteRule\">\r\n          <Select v-model=\"formItem.siteId\" :disabled=\"actionType==='view'\">\r\n            <Option v-for=\"(item, index) in siteList\" :value=\"item.id\" :key=\"index\" >{{ item.name }}</Option >\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n        <FormItem prop=\"managed_user\" label=\"店铺负责人\" class=\"sellerSelectItem\">\r\n          <Select type=\"text\" v-model=\"formItem.managedUser\" :filterable=\"true\" style=\"width: 555px\" placeholder=\"请选择\" :transfer=\"true\" >\r\n            <Option v-for=\"item in usersOptions\" :value=\"item.userId\" :key=\"item.userId\" >{{ item.nickName }}</Option >\r\n          </Select>\r\n          <Button type=\"dashed\" @click=\"openPerson\" style=\"margin-left: 3px\" size=\"default\" >选择</Button >\r\n          <div class=\"closeIcon\" v-show=\"!!formItem.managedUser\" @click=\"() => (formItem.managedUser = undefined)\" >\r\n            <Icon type=\"md-close\" size=\"14\" />\r\n          </div>\r\n          <person-select :visible=\"personVisible\" :onCancel=\"() => (personVisible = false)\"\r\n                         @setPerson=\"arr => (formItem.managedUser = arr.map(v => v.id)[0])\"\r\n                         @setSelectInfo=\"setSelectInfo\" ref=\"personSelectRef\" groupName=\"shopmanage_operateuser_config\" :isQuery=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" maxlength=\"11\" width=\"100%\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\" prop=\"remark\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"drawer-footer\">\r\n        <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;\r\n        <Button type=\"primary\" @click=\"handleSubmit\" :loading=\"saving\" >保存</Button >\r\n      </div>\r\n    </Modal>\r\n    <Modal v-model=\"modalViewVisible\" :title=\"modalTitle\" @on-cancel=\"handleReset\" width=\"765px\">\r\n      <Form ref=\"viewForm\" :model=\"formItem\" :label-width=\"100\">\r\n        <FormItem label=\"平台名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.platform\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"站点名称\" prop=\"siteName\">\r\n          <Input v-model=\"formItem.site\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺名称\" prop=\"name\">\r\n          <Input v-model=\"formItem.name\" placeholder=\"\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺编号\" prop=\"aliaName\">\r\n          <Input v-model=\"formItem.aliaName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"客户编号\" prop=\"erpCustNo\">\r\n          <Input v-model=\"formItem.erpCustNo\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"店铺负责人\" prop=\"managedUserName\">\r\n          <Input v-model=\"formItem.managedUserName\" placeholder=\"请输入内容\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n        <FormItem label=\"手机号码\" prop=\"phone\">\r\n          <Input v-model=\"formItem.phone\" placeholder=\"\" :readonly=\"true\" />\r\n        </FormItem>\r\n        <FormItem label=\"状态\">\r\n          <RadioGroup v-model=\"formItem.status\" type=\"button\" :readonly=\"true\">\r\n            <Radio v-for=\"v in statusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n        <FormItem label=\"备注\" prop=\"remark\">\r\n          <Input v-model=\"formItem.remark\" type=\"textarea\" :readonly=\"true\"></Input>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\"></div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\n\r\nimport personSelect from \"@/components/person-select-radio/index.vue\";\r\nimport Shop from \"@/api/basf/shop\";\r\nimport Platform from \"@/api/basf/platform\";\r\nimport Site from \"@/api/basf/site\";\r\nimport {autoTableHeight} from \"@/libs/tools.js\";\r\nimport Common from '@/api/basic/common'\r\n\r\nexport default {\r\n  name: \"shopList\",\r\n  components: {\r\n    personSelect\r\n  },\r\n  data() {\r\n    return {\r\n      autoTableHeight,\r\n      statusOps: Common.userStatusOps,\r\n      relateOps: Common.relateOps,\r\n      actionType:'view',\r\n      personVisible: false,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      modalViewVisible: false,\r\n      shopNameShow: false,\r\n      siteRule: { required: false },\r\n      readName: false,\r\n      modalTitle: \"\",\r\n      platformList: [],\r\n      selectTreeData: [],\r\n      siteList: [],\r\n      selectPersons: [],\r\n      pageInfo: {\r\n        total: 0,\r\n        page: 1,\r\n        limit: 10,\r\n        status: -1,\r\n        relate: -1,\r\n        platformId:-1,\r\n        siteId:-1,\r\n        name: \"\",\r\n        aliaName: \"\"\r\n      },\r\n      formItemRules: {\r\n        name: [\r\n          { required: true, message: \"店铺名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        platformId: [\r\n          { required: true, message: \"所属平台不能为空\", trigger: \"blur\" }\r\n        ],\r\n        aliaName: [\r\n          { required: true, message: \"店铺编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        departmentId: [{ required: true, message: \"部门不能为空\" }]\r\n      },\r\n      formItem: {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      },\r\n      columns: [\r\n        {\r\n          type: \"selection\",\r\n          maxWidth: 40\r\n        },\r\n        {\r\n          title: \"平台名称\",\r\n          key: \"platform\",\r\n          minWidth: 120,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"站点名称\",\r\n          key: \"site\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺名称\",\r\n          key: \"name\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺编号\",\r\n          key: \"aliaName\",\r\n          minWidth: 200,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"客户编号\",\r\n          key: \"erpCustNo\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"店铺负责人\",\r\n          key: \"managedUserName\",\r\n          width: 110,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div v-copytext={row.managedUserName}>{row.managedUserName}</div>\r\n          )\r\n        },\r\n        {\r\n          title: \"手机号码\",\r\n          key: \"phone\",\r\n          minWidth: 160,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          key: \"status\",\r\n          slot: \"status\",\r\n          minWidth: 100,\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"关联领星\",\r\n          key: \"sid\",\r\n          minWidth: 160,\r\n          align: \"center\",\r\n          render:(_, { row }) => (\r\n              <span>{row.sid>0?\"已关联\":\"未关联\"}</span>\r\n          )\r\n        },\r\n        {\r\n          title: \"新增修改时间\",\r\n          key: \"updateTime\",\r\n          sortable: \"custom\",\r\n          width: 140,\r\n          align: \"center\",\r\n          render: (_, { row }) => (\r\n              <div>\r\n                <p title=\"新增时间\">{row.createTime}</p>\r\n                <p title=\"修改时间\">{row.updateTime}</p>\r\n              </div>\r\n          )\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          minWidth: 150,\r\n          align: \"center\"\r\n        }\r\n      ],\r\n      data: [],\r\n      usersOptions: [],\r\n      allDepartments: []\r\n    };\r\n  },\r\n  methods: {\r\n    treeSelectNormalizer(node) {\r\n      return {\r\n        id: node.id,\r\n        label: node.departmentName,\r\n        children: node.children\r\n      };\r\n    },\r\n    onPersonCancel() {\r\n      this.personVisible = false;\r\n    },\r\n    // 把选中的人员设置在父组件中\r\n    setPerson(personArr = []) {\r\n      let personIds = [];\r\n      personArr.map(item => {\r\n        personIds.push(item.id);\r\n      });\r\n      if (!this.ArrayIsEqual(this.selectPersons, personIds)) {\r\n        Shop.addShopUsers({\r\n          id: this.id,\r\n          userIds: personIds.join(\",\")\r\n        }).then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"授权成功\");\r\n          } else {\r\n            this.$Message.error(\"授权失败\");\r\n          }\r\n        });\r\n      }\r\n    },\r\n    ArrayIsEqual(arr1, arr2) {\r\n      //判断2个数组是否相等\r\n      if (arr1 === arr2) {\r\n        //如果2个数组对应的指针相同，那么肯定相等，同时也对比一下类型\r\n        return true;\r\n      } else {\r\n        if (arr1.length !== arr2.length) {\r\n          return false;\r\n        } else {\r\n          //长度相同\r\n          for (let i in arr1) {\r\n            //循环遍历对比每个位置的元素\r\n            if (arr1[i] !== arr2[i]) {\r\n              //只要出现一次不相等，那么2个数组就不相等\r\n              return false;\r\n            }\r\n          } //for循环完成，没有出现不相等的情况，那么2个数组相等\r\n          return true;\r\n        }\r\n      }\r\n    },\r\n    syncShop(){\r\n      this.loading = true;\r\n      Shop.syncShop().then((res)=>{\r\n        if(res['code'] ===0){\r\n          this.$Message.success(\"同步成功\");\r\n          this.handleSearch();\r\n        }\r\n      }).finally(()=>{this.loading = false})\r\n    },\r\n    handleModal(data) {\r\n      if (data) {\r\n        this.readName = true;\r\n        this.shopNameShow = true;\r\n        this.modalTitle = \"编辑\";\r\n        this.actionType = \"edit\";\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      } else {\r\n        this.modalTitle = \"添加\";\r\n        this.actionType = \"add\";\r\n      }\r\n      this.modalVisible = true;\r\n    },\r\n    handleView(data) {\r\n      this.formItem = data;\r\n      this.modalTitle = \"查看店铺\";\r\n      this.actionType = \"view\";\r\n      this.formItem = Object.assign({}, this.formItem, data);\r\n      this.modalViewVisible = true;\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        id: \"\",\r\n        platformId: \"\",\r\n        platform: \"\",\r\n        siteId: \"\",\r\n        site: \"\",\r\n        name: \"\",\r\n        aliaName: \"\",\r\n        erpCustNo: \"\",\r\n        managedUserName:\"\",\r\n        phone:\"\",\r\n        status: 0,\r\n        remark: \"\"\r\n      };\r\n      let form = this.$refs[\"form\"];\r\n      form.resetFields();\r\n      this.modalVisible = false;\r\n      this.shopNameShow = false;\r\n      this.readName = false;\r\n      this.siteRule = { required: false };\r\n      this.saving = false;\r\n    },\r\n\r\n    getDepartNames() {\r\n      const { allDepartments, formItem } = this;\r\n      const departmentIds = formItem.departmentId || [];\r\n      const results = [];\r\n      departmentIds.forEach(id => {\r\n        const departmentName = (allDepartments.find(v => v.id === id) || {})\r\n            .departmentName;\r\n        if (departmentName) results.push(departmentName);\r\n      });\r\n      return results;\r\n    },\r\n    handleSubmit() {\r\n      let form = this.$refs[\"form\"];\r\n      const { formItem, usersOptions } = this;\r\n      form.validate(valid => {\r\n        const params = {\r\n          ...this.formItem,\r\n          departmentId: (this.formItem.departmentId || []).toString(),\r\n          departmentName: this.getDepartNames().toString(),\r\n          name: formItem.name ? formItem.name.trim() : formItem.name,\r\n          operateUser: (\r\n              usersOptions.find(v => v.userId === formItem.managedUser) || {}\r\n          ).nickName\r\n        };\r\n        if (valid) {\r\n          this.saving = true;\r\n          if (this.formItem.id) {\r\n            Shop.edit(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"保存成功\");\r\n                this.handleReset()\r\n                this.handleSearch();\r\n              }}).finally(() => {this.saving = false;});\r\n          } else {\r\n            Shop.add(params).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.handleReset();\r\n                this.handleSearch();\r\n                this.$Message.success(\"保存成功\");\r\n              }}).finally(() => {this.saving = false;});\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleResetForm(form) {\r\n      if (this.$refs[form]) this.$refs[form].resetFields();\r\n      this.pageInfo.deptId = undefined;\r\n    },\r\n    handleSearch(page) {\r\n      const { pageInfo } = this;\r\n      if (page) {\r\n        pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        ...pageInfo,\r\n        total: undefined,\r\n        deptId: pageInfo.deptId\r\n            ? pageInfo.deptId.toString() || undefined\r\n            : undefined\r\n      };\r\n      Shop.listPage(params)\r\n          .then(res => {\r\n            this.data = res.data.records;\r\n            this.pageInfo.total = parseInt(res.data.total);\r\n          })\r\n          .finally(() => {\r\n            this.loading = false;\r\n          });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleRemove(data) {\r\n      let modal = this.$Modal;\r\n      let title = data.status === 0?\"停用\":(data.status === 1?\"启用\":\"解锁\");\r\n      modal.confirm({\r\n        title: \"确定\"+title+\"吗？\",\r\n        onOk: () => {\r\n          Shop.remove(data.id).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.pageInfo.page = 1;\r\n              this.$Message.success(title+\"成功\");\r\n            }\r\n            this.handleSearch();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handlePlatform() {\r\n      Platform.getAll().then(res => {\r\n        this.platformList = res.data;\r\n        this.platformList.unshift({ id: -1,  name: \"全部\" });\r\n      });\r\n    },\r\n    handleSite() {\r\n      Site.getAll().then(res => {\r\n        this.siteList = res.data;\r\n        this.siteList.unshift({ id: -1, name: \"全部\" });\r\n      });\r\n    },\r\n    change(val) {\r\n      if (val === \"1\") {\r\n        this.siteRule = {\r\n          required: true,\r\n          message: \"所属站点不能为空\",\r\n          trigger: \"blur\"\r\n        };\r\n      } else {\r\n        this.siteRule = { required: false };\r\n      }\r\n      if (val != null) {\r\n        this.formItem.siteId = \"\";\r\n        Site.getByPlatformId(val).then(res => {\r\n          this.siteList = res.data;\r\n          if (val !== \"1\") {\r\n            this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //主表格排序查询\r\n    onSortChange({order }) {\r\n      if (order && order !== \"normal\") {\r\n        this.pageInfo.sort = \"c_update_time\";\r\n        this.pageInfo.order = order;\r\n      } else {\r\n        this.pageInfo.sort = null;\r\n        this.pageInfo.order = null;\r\n      }\r\n      this.handleSearch();\r\n    },\r\n    platformChange(pfId) {\r\n      if (pfId != null) {\r\n        Site.getByPlatformId(pfId).then(res => {\r\n          this.siteList = res.data;\r\n          this.siteList.unshift({ id: -1, name: \"全部\" });\r\n          this.pageInfo.siteId = -1;\r\n        });\r\n      }\r\n    },\r\n    handleClick(name, row) {\r\n      switch (name) {\r\n        case \"remove\":\r\n          this.handleRemove(row);\r\n          break;\r\n        case \"view\":\r\n          this.handleView(row);\r\n          break;\r\n      }\r\n    },\r\n    // 人员选择相关\r\n    setSelectInfo(info = {}) {\r\n      this.usersOptions = info.personArr || [];\r\n    },\r\n    openPerson() {\r\n      //打开人员选择\r\n      const { personSelectRef } = this.$refs;\r\n      const { usersOptions, formItem } = this;\r\n      const selectedId = formItem.managedUser || undefined;\r\n      if (personSelectRef)\r\n        personSelectRef.setDefault(usersOptions\r\n            .filter(v => selectedId === v.userId)\r\n            .map(v => ({ name: v.nickName, id: v.userId }))\r\n        ); //给组件设置默认选中\r\n      this.personVisible = true;\r\n    }\r\n  },\r\n  mounted: function() {\r\n    this.handleSearch();\r\n    this.handlePlatform();\r\n    this.handleSite();\r\n  }\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.shopManage {\r\n  .searchForm {\r\n    position: relative;\r\n    padding-right: 200px;\r\n    .rightBtn {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n    }\r\n  }\r\n  .ivu-table-cell {\r\n    padding-left: 8px;\r\n    padding-right: 8px;\r\n  }\r\n}\r\n.shopManageEditModal {\r\n  .ivu-modal {\r\n    top: 30px;\r\n    .ivu-modal-body {\r\n      max-height: 720px;\r\n      overflow: auto;\r\n    }\r\n  }\r\n  .sellerSelectItem {\r\n    position: relative;\r\n    .ivu-select-multiple .ivu-tag span:not(.ivu-select-max-tag) {\r\n      max-width: 45px;\r\n    }\r\n    .closeIcon {\r\n      color: #ccc;\r\n      width: 16px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 80px;\r\n      font-size: 9px;\r\n      &:hover {\r\n        cursor: pointer;\r\n        color: #e53935;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA+IA,OAAAA,YAAA;AACA,OAAAC,IAAA;AACA,OAAAC,QAAA;AACA,OAAAC,IAAA;AACA,SAAAC,eAAA;AACA,OAAAC,MAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAP,YAAA,EAAAA;EACA;EACAQ,IAAA,WAAAA,KAAA;IAAA,IAAAC,CAAA,QAAAC,cAAA;IACA;MACAN,eAAA,EAAAA,eAAA;MACAO,SAAA,EAAAN,MAAA,CAAAO,aAAA;MACAC,SAAA,EAAAR,MAAA,CAAAQ,SAAA;MACAC,UAAA;MACAC,aAAA;MACAC,OAAA;MACAC,MAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,QAAA;QAAAC,QAAA;MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,YAAA;MACAC,cAAA;MACAC,QAAA;MACAC,aAAA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA;QACA9B,IAAA;QACA+B,QAAA;MACA;MACAC,aAAA;QACAhC,IAAA,GACA;UAAAgB,QAAA;UAAAiB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,UAAA,GACA;UAAAb,QAAA;UAAAiB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAH,QAAA,GACA;UAAAf,QAAA;UAAAiB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA;UAAAnB,QAAA;UAAAiB,OAAA;QAAA;MACA;MACAG,QAAA;QACAC,EAAA;QACAR,UAAA;QACAS,QAAA;QACAR,MAAA;QACAS,IAAA;QACAvC,IAAA;QACA+B,QAAA;QACAS,SAAA;QACAC,eAAA;QACAC,KAAA;QACAf,MAAA;QACAgB,MAAA;MACA;MACAC,OAAA,GACA;QACAC,IAAA;QACAC,QAAA;MACA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAG,KAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;UAAA,OAAApD,CAAA;YAAA;cAAAH,IAAA;cAAAwD,KAAA,EACAD,GAAA,CAAAd;YAAA;UAAA,IAAAc,GAAA,CAAAd,eAAA;QAAA;MAEA,GACA;QACAM,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAS,IAAA;QACAR,QAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,QAAA;QACAC,KAAA;QACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAK,KAAA;UAAA,IAAAH,GAAA,GAAAG,KAAA,CAAAH,GAAA;UAAA,OAAApD,CAAA,UACAoD,GAAA,CAAAI,GAAA;QAAA;MAEA,GACA;QACAZ,KAAA;QACAC,GAAA;QACAY,QAAA;QACAT,KAAA;QACAD,KAAA;QACAE,MAAA,WAAAA,OAAAC,CAAA,EAAAQ,KAAA;UAAA,IAAAN,GAAA,GAAAM,KAAA,CAAAN,GAAA;UAAA,OAAApD,CAAA,SAAAA,CAAA;YAAA;cAAA,SAEA;YAAA;UAAA,IAAAoD,GAAA,CAAAO,UAAA,IAAA3D,CAAA;YAAA;cAAA,SACA;YAAA;UAAA,IAAAoD,GAAA,CAAAQ,UAAA;QAAA;MAGA,GACA;QACAhB,KAAA;QACAU,IAAA;QACAO,KAAA;QACAf,QAAA;QACAC,KAAA;MACA,EACA;MACAhD,IAAA;MACA+D,YAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA;IACAC,oBAAA,WAAAA,qBAAAC,IAAA;MACA;QACAhC,EAAA,EAAAgC,IAAA,CAAAhC,EAAA;QACAiC,KAAA,EAAAD,IAAA,CAAAE,cAAA;QACAC,QAAA,EAAAH,IAAA,CAAAG;MACA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAhE,aAAA;IACA;IACA;IACAiE,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,SAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAG,SAAA;MACAJ,SAAA,CAAAK,GAAA,WAAAC,IAAA;QACAF,SAAA,CAAAG,IAAA,CAAAD,IAAA,CAAA7C,EAAA;MACA;MACA,UAAA+C,YAAA,MAAA9D,aAAA,EAAA0D,SAAA;QACArF,IAAA,CAAA0F,YAAA;UACAhD,EAAA,OAAAA,EAAA;UACAiD,OAAA,EAAAN,SAAA,CAAAO,IAAA;QACA,GAAAC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA;YACAd,KAAA,CAAAe,QAAA,CAAAC,OAAA;UACA;YACAhB,KAAA,CAAAe,QAAA,CAAAE,KAAA;UACA;QACA;MACA;IACA;IACAR,YAAA,WAAAA,aAAAS,IAAA,EAAAC,IAAA;MACA;MACA,IAAAD,IAAA,KAAAC,IAAA;QACA;QACA;MACA;QACA,IAAAD,IAAA,CAAAf,MAAA,KAAAgB,IAAA,CAAAhB,MAAA;UACA;QACA;UACA;UACA,SAAAiB,CAAA,IAAAF,IAAA;YACA;YACA,IAAAA,IAAA,CAAAE,CAAA,MAAAD,IAAA,CAAAC,CAAA;cACA;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAvF,OAAA;MACAf,IAAA,CAAAqG,QAAA,GAAAR,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA;UACAQ,MAAA,CAAAP,QAAA,CAAAC,OAAA;UACAM,MAAA,CAAAC,YAAA;QACA;MACA,GAAAC,OAAA;QAAAF,MAAA,CAAAvF,OAAA;MAAA;IACA;IACA0F,WAAA,WAAAA,YAAAlG,IAAA;MACA,IAAAA,IAAA;QACA,KAAAe,QAAA;QACA,KAAAH,YAAA;QACA,KAAAI,UAAA;QACA,KAAAV,UAAA;QACA,KAAA4B,QAAA,GAAAiE,MAAA,CAAAC,MAAA,UAAAlE,QAAA,EAAAlC,IAAA;MACA;QACA,KAAAgB,UAAA;QACA,KAAAV,UAAA;MACA;MACA,KAAAI,YAAA;IACA;IACA2F,UAAA,WAAAA,WAAArG,IAAA;MACA,KAAAkC,QAAA,GAAAlC,IAAA;MACA,KAAAgB,UAAA;MACA,KAAAV,UAAA;MACA,KAAA4B,QAAA,GAAAiE,MAAA,CAAAC,MAAA,UAAAlE,QAAA,EAAAlC,IAAA;MACA,KAAAW,gBAAA;IACA;IACA2F,WAAA,WAAAA,YAAA;MACA,KAAApE,QAAA;QACAC,EAAA;QACAR,UAAA;QACAS,QAAA;QACAR,MAAA;QACAS,IAAA;QACAvC,IAAA;QACA+B,QAAA;QACAS,SAAA;QACAC,eAAA;QACAC,KAAA;QACAf,MAAA;QACAgB,MAAA;MACA;MACA,IAAA8D,IAAA,QAAAC,KAAA;MACAD,IAAA,CAAAE,WAAA;MACA,KAAA/F,YAAA;MACA,KAAAE,YAAA;MACA,KAAAG,QAAA;MACA,KAAAF,QAAA;QAAAC,QAAA;MAAA;MACA,KAAAL,MAAA;IACA;IAEAiG,cAAA,WAAAA,eAAA;MACA,IAAA1C,cAAA,QAAAA,cAAA;QAAA9B,QAAA,QAAAA,QAAA;MACA,IAAAyE,aAAA,GAAAzE,QAAA,CAAAD,YAAA;MACA,IAAA2E,OAAA;MACAD,aAAA,CAAAE,OAAA,WAAA1E,EAAA;QACA,IAAAkC,cAAA,IAAAL,cAAA,CAAA8C,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA5E,EAAA,KAAAA,EAAA;QAAA,UACAkC,cAAA;QACA,IAAAA,cAAA,EAAAuC,OAAA,CAAA3B,IAAA,CAAAZ,cAAA;MACA;MACA,OAAAuC,OAAA;IACA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAV,IAAA,QAAAC,KAAA;MACA,IAAAtE,QAAA,QAAAA,QAAA;QAAA6B,YAAA,QAAAA,YAAA;MACAwC,IAAA,CAAAW,QAAA,WAAAC,KAAA;QACA,IAAAC,MAAA,GAAAC,aAAA,CAAAA,aAAA,KACAJ,MAAA,CAAA/E,QAAA;UACAD,YAAA,GAAAgF,MAAA,CAAA/E,QAAA,CAAAD,YAAA,QAAAqF,QAAA;UACAjD,cAAA,EAAA4C,MAAA,CAAAP,cAAA,GAAAY,QAAA;UACAxH,IAAA,EAAAoC,QAAA,CAAApC,IAAA,GAAAoC,QAAA,CAAApC,IAAA,CAAAyH,IAAA,KAAArF,QAAA,CAAApC,IAAA;UACA0H,WAAA,GACAzD,YAAA,CAAA+C,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAU,MAAA,KAAAvF,QAAA,CAAAwF,WAAA;UAAA,UACAC;QAAA,EACA;QACA,IAAAR,KAAA;UACAF,MAAA,CAAAxG,MAAA;UACA,IAAAwG,MAAA,CAAA/E,QAAA,CAAAC,EAAA;YACA1C,IAAA,CAAAmI,IAAA,CAAAR,MAAA,EAAA9B,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA0B,MAAA,CAAAzB,QAAA,CAAAC,OAAA;gBACAwB,MAAA,CAAAX,WAAA;gBACAW,MAAA,CAAAjB,YAAA;cACA;YAAA,GAAAC,OAAA;cAAAgB,MAAA,CAAAxG,MAAA;YAAA;UACA;YACAhB,IAAA,CAAAoI,GAAA,CAAAT,MAAA,EAAA9B,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA;gBACA0B,MAAA,CAAAX,WAAA;gBACAW,MAAA,CAAAjB,YAAA;gBACAiB,MAAA,CAAAzB,QAAA,CAAAC,OAAA;cACA;YAAA,GAAAQ,OAAA;cAAAgB,MAAA,CAAAxG,MAAA;YAAA;UACA;QACA;MACA;IACA;IACAqH,eAAA,WAAAA,gBAAAvB,IAAA;MACA,SAAAC,KAAA,CAAAD,IAAA,QAAAC,KAAA,CAAAD,IAAA,EAAAE,WAAA;MACA,KAAApF,QAAA,CAAA0G,MAAA,GAAAlD,SAAA;IACA;IACAmB,YAAA,WAAAA,aAAAzE,IAAA;MAAA,IAAAyG,MAAA;MACA,IAAA3G,QAAA,QAAAA,QAAA;MACA,IAAAE,IAAA;QACAF,QAAA,CAAAE,IAAA,GAAAA,IAAA;MACA;MACA,KAAAf,OAAA;MACA,IAAA4G,MAAA,GAAAC,aAAA,CAAAA,aAAA,KACAhG,QAAA;QACAC,KAAA,EAAAuD,SAAA;QACAkD,MAAA,EAAA1G,QAAA,CAAA0G,MAAA,GACA1G,QAAA,CAAA0G,MAAA,CAAAT,QAAA,MAAAzC,SAAA,GACAA;MAAA,EACA;MACApF,IAAA,CAAAwI,QAAA,CAAAb,MAAA,EACA9B,IAAA,WAAAC,GAAA;QACAyC,MAAA,CAAAhI,IAAA,GAAAuF,GAAA,CAAAvF,IAAA,CAAAkI,OAAA;QACAF,MAAA,CAAA3G,QAAA,CAAAC,KAAA,GAAA6G,QAAA,CAAA5C,GAAA,CAAAvF,IAAA,CAAAsB,KAAA;MACA,GACA2E,OAAA;QACA+B,MAAA,CAAAxH,OAAA;MACA;IACA;IACA4H,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAhH,QAAA,CAAAE,IAAA,GAAA8G,OAAA;MACA,KAAArC,YAAA;IACA;IACAsC,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAlH,QAAA,CAAAG,KAAA,GAAA+G,IAAA;MACA,KAAAvC,YAAA;IACA;IACAwC,YAAA,WAAAA,aAAAxI,IAAA;MAAA,IAAAyI,MAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAA9F,KAAA,GAAA7C,IAAA,CAAAyB,MAAA,gBAAAzB,IAAA,CAAAyB,MAAA;MACAiH,KAAA,CAAAE,OAAA;QACA/F,KAAA,SAAAA,KAAA;QACAgG,IAAA,WAAAA,KAAA;UACApJ,IAAA,CAAAqJ,MAAA,CAAA9I,IAAA,CAAAmC,EAAA,EAAAmD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA;cACAkD,MAAA,CAAApH,QAAA,CAAAE,IAAA;cACAkH,MAAA,CAAAjD,QAAA,CAAAC,OAAA,CAAA5C,KAAA;YACA;YACA4F,MAAA,CAAAzC,YAAA;UACA;QACA;MACA;IACA;IACA+C,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACAtJ,QAAA,CAAAuJ,MAAA,GAAA3D,IAAA,WAAAC,GAAA;QACAyD,MAAA,CAAA/H,YAAA,GAAAsE,GAAA,CAAAvF,IAAA;QACAgJ,MAAA,CAAA/H,YAAA,CAAAiI,OAAA;UAAA/G,EAAA;UAAArC,IAAA;QAAA;MACA;IACA;IACAqJ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACAzJ,IAAA,CAAAsJ,MAAA,GAAA3D,IAAA,WAAAC,GAAA;QACA6D,MAAA,CAAAjI,QAAA,GAAAoE,GAAA,CAAAvF,IAAA;QACAoJ,MAAA,CAAAjI,QAAA,CAAA+H,OAAA;UAAA/G,EAAA;UAAArC,IAAA;QAAA;MACA;IACA;IACAuJ,MAAA,WAAAA,OAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,GAAA;QACA,KAAAzI,QAAA;UACAC,QAAA;UACAiB,OAAA;UACAC,OAAA;QACA;MACA;QACA,KAAAnB,QAAA;UAAAC,QAAA;QAAA;MACA;MACA,IAAAwI,GAAA;QACA,KAAApH,QAAA,CAAAN,MAAA;QACAjC,IAAA,CAAA6J,eAAA,CAAAF,GAAA,EAAAhE,IAAA,WAAAC,GAAA;UACAgE,MAAA,CAAApI,QAAA,GAAAoE,GAAA,CAAAvF,IAAA;UACA,IAAAsJ,GAAA;YACAC,MAAA,CAAApI,QAAA,CAAA+H,OAAA;cAAA/G,EAAA;cAAArC,IAAA;YAAA;UACA;QACA;MACA;IACA;IACA;IACA2J,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,KAAA,GAAAD,KAAA,CAAAC,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA;QACA,KAAAtI,QAAA,CAAAuI,IAAA;QACA,KAAAvI,QAAA,CAAAsI,KAAA,GAAAA,KAAA;MACA;QACA,KAAAtI,QAAA,CAAAuI,IAAA;QACA,KAAAvI,QAAA,CAAAsI,KAAA;MACA;MACA,KAAA3D,YAAA;IACA;IACA6D,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,IAAA;QACAnK,IAAA,CAAA6J,eAAA,CAAAM,IAAA,EAAAxE,IAAA,WAAAC,GAAA;UACAwE,MAAA,CAAA5I,QAAA,GAAAoE,GAAA,CAAAvF,IAAA;UACA+J,MAAA,CAAA5I,QAAA,CAAA+H,OAAA;YAAA/G,EAAA;YAAArC,IAAA;UAAA;UACAiK,MAAA,CAAA1I,QAAA,CAAAO,MAAA;QACA;MACA;IACA;IACAoI,WAAA,WAAAA,YAAAlK,IAAA,EAAAuD,GAAA;MACA,QAAAvD,IAAA;QACA;UACA,KAAA0I,YAAA,CAAAnF,GAAA;UACA;QACA;UACA,KAAAgD,UAAA,CAAAhD,GAAA;UACA;MACA;IACA;IACA;IACA4G,aAAA,WAAAA,cAAA;MAAA,IAAAC,IAAA,GAAAvF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAAZ,YAAA,GAAAmG,IAAA,CAAAxF,SAAA;IACA;IACAyF,UAAA,WAAAA,WAAA;MACA;MACA,IAAAC,eAAA,QAAA5D,KAAA,CAAA4D,eAAA;MACA,IAAArG,YAAA,QAAAA,YAAA;QAAA7B,QAAA,QAAAA,QAAA;MACA,IAAAmI,UAAA,GAAAnI,QAAA,CAAAwF,WAAA,IAAA7C,SAAA;MACA,IAAAuF,eAAA,EACAA,eAAA,CAAAE,UAAA,CAAAvG,YAAA,CACAwG,MAAA,WAAAxD,CAAA;QAAA,OAAAsD,UAAA,KAAAtD,CAAA,CAAAU,MAAA;MAAA,GACA1C,GAAA,WAAAgC,CAAA;QAAA;UAAAjH,IAAA,EAAAiH,CAAA,CAAAY,QAAA;UAAAxF,EAAA,EAAA4E,CAAA,CAAAU;QAAA;MAAA,EACA;MACA,KAAAlH,aAAA;IACA;EACA;EACAiK,OAAA,WAAAA,QAAA;IACA,KAAAxE,YAAA;IACA,KAAA+C,cAAA;IACA,KAAAI,UAAA;EACA;AACA"}]}