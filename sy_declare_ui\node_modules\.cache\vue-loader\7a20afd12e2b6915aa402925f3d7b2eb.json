{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\clearanceLink\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\custom\\base\\clearanceLink\\index.vue", "mtime": 1752737748520}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQ29tbW9uQXBpIGZyb20gIkAvYXBpL2Jhc2UvY29tbW9uQXBpIjsNCmltcG9ydCBDbGVhcmFuY2VMaW5rIGZyb20gIkAvYXBpL2N1c3RvbS9jbGVhcmFuY2VMaW5rIjsNCmltcG9ydCBMb2dNb2RlbCBmcm9tICJAL3ZpZXcvbW9kdWxlL2Jhc2UvYnVzc2luZXNzTG9nL2xvZ01vZGVsLnZ1ZSI7DQppbXBvcnQge2dldFRva2VuLCBnZXRVcmx9IGZyb20gIkAvbGlicy91dGlsIjsNCmltcG9ydCB7bGlzdEFsbFNwdX0gZnJvbSAnQC9hcGkvYmFzZi9wcm9kdWN0LmpzJw0KaW1wb3J0IFNob3AgZnJvbSAiQC9hcGkvYmFzZi9zaG9wIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7TG9nTW9kZWx9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNhdmluZzogZmFsc2UsDQogICAgICBtb2RhbDogZmFsc2UsDQogICAgICBzcGluU2hvdzogZmFsc2UsDQogICAgICBkaXNhYmxlZDogZmFsc2UsDQogICAgICBsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHJlZlR5cGU6IG51bGwsDQogICAgICBzZWxlY3REYXRhOltdLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgc3RhdHVzTGlzdDpbe2tleTowLHZhbHVlOiLoh6rliqgifSx7a2V5OjEsdmFsdWU6IuaJi+WKqCJ9XSwNCiAgICAgIHNlYXJjaEZvcm06IHtzcHVOYW1lOiAnJywgY291bnRyeTogJycsIGlzQXV0bzonJ30sDQogICAgICBwYWdlSW5mbzoge3RvdGFsOiAwLCBwYWdlOiAxLCBsaW1pdDogMTB9LA0KICAgICAgbG9naW5JbmZvOiB7DQogICAgICAgIEFjY2VwdDogJ2FwcGxpY2F0aW9uL2pzb24sdGV4dC9wbGFpbiwgKi8qLCAqLyonLA0KICAgICAgICBtb2RlOiAnY29ycycsDQogICAgICAgIEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCkNCiAgICAgIH0sDQogICAgICBpbXBvcnRVUmw6IGdldFVybCgpICsgIi9iYXNlL2NsZWFyYW5jZUxpbmsvaW1wb3J0RmlsZSIsDQogICAgICBmb3JtOiB7cGFyZW50SWQ6IG51bGwsIGNvdW50cnk6IG51bGwsIHNlbGxlclNrdTogbnVsbCwgc2hvcElkOiBudWxsfSwNCiAgICAgIGRhdGE6IFtdLA0KICAgICAgY2xhc3NOYW1lTGlzdDpbXSwNCiAgICAgIHNob3BMaXN0OltdLA0KICAgICAgY29sdW1uOiBbDQogICAgICAgIHt0aXRsZTogJ+mAiemhuScsdHlwZTogJ3NlbGVjdGlvbicsd2lkdGg6IDcwLGFsaWduOiAnY2VudGVyJyxmaXhlZDonbGVmdCcsfSwNCiAgICAgICAge3RpdGxlOiAn5Lqn5ZOB5Z6L5Y+3Jywga2V5OiAnc3B1TmFtZScsIG1pbldpZHRoOiAxMDAsIGFsaWduOiAnY2VudGVyJyxyZW5kZXI6IChoLCB7cm93fSkgPT4ge3JldHVybiA8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuc3B1TmFtZX0+e3Jvdy5zcHVOYW1lfTwvc3Bhbj59fSwNCiAgICAgICAge3RpdGxlOiAn5riF5YWz5Zu9Jywga2V5OiAnY291bnRyeScsIHdpZHRoOiAxMDAsIGFsaWduOiAnY2VudGVyJywgc2xvdDogJ2NvdW50cnknfSwNCiAgICAgICAge3RpdGxlOiAn5bmz5Y+wJywga2V5OiAncGxhdGZvcm1JZCcsIHdpZHRoOiAxMDAsIGFsaWduOiAnY2VudGVyJyxyZW5kZXI6IChoLCB7cm93fSkgPT4ge3JldHVybiA8c3BhbiB2LWNvcHl0ZXh0PXtyb3cucGxhdGZvcm1JZCA9PT0gJzEnPydBTUFaT04nOidXQUxNQVJUJ30+e3Jvdy5wbGF0Zm9ybUlkID09PSAnMSc/J0FNQVpPTic6J1dBTE1BUlQnfTwvc3Bhbj59fSwNCiAgICAgICAge3RpdGxlOiAn572R5bqXJywga2V5OiAnc2hvcE5hbWUnLCB3aWR0aDogMTAwLCBhbGlnbjogJ2NlbnRlcicscmVuZGVyOiAoaCwge3Jvd30pID0+IHtyZXR1cm4gPHNwYW4gdi1jb3B5dGV4dD17cm93LnNob3BOYW1lfT57cm93LnNob3BOYW1lfTwvc3Bhbj59fSwNCiAgICAgICAge3RpdGxlOiAnYXNpbicsIGtleTogJ2FzaW4nLCB3aWR0aDogMTIwLCBhbGlnbjogJ2NlbnRlcicscmVuZGVyOiAoaCwge3Jvd30pID0+IHtyZXR1cm4gPHNwYW4gdi1jb3B5dGV4dD17cm93LmFzaW59Pntyb3cuYXNpbn08L3NwYW4+fX0sDQogICAgICAgIHt0aXRsZTogJ3NlbGxlclNrdScsIGtleTogJ3NlbGxlclNrdScsIG1pbldpZHRoOiAxNTAsIGFsaWduOiAnY2VudGVyJyxyZW5kZXI6IChoLCB7cm93fSkgPT4ge3JldHVybiA8c3BhbiB2LWNvcHl0ZXh0PXtyb3cuc2VsbGVyU2t1fT57cm93LnNlbGxlclNrdX08L3NwYW4+fX0sDQogICAgICAgIHt0aXRsZTogJ+mUgOWUruS7tycsIGtleTogJ3ByaWNlJywgd2lkdGg6IDEyMCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J3ByaWNlJ30sDQogICAgICAgIHt0aXRsZTogJ+WVhuWTgemTvuaOpScsIGtleTogJ2dvb2RzVXJsJywgbWluV2lkdGg6IDI1MCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J2dvb2RzVXJsJ30sDQogICAgICAgIHt0aXRsZTogJ+WVhuWTgeWbvueJhycsIGtleTogJ3Bob3RvVXJsJywgbWluV2lkdGg6IDMwMCwgYWxpZ246ICdjZW50ZXInLHNsb3Q6J3Bob3RvVXJsJ30sDQogICAgICAgIHt0aXRsZTogJ+abtOaWsOW9ouW8jycsIGtleTogJ2lzQXV0bycsIHdpZHRoOiAxMDAsIGFsaWduOiAnY2VudGVyJyxzbG90Oidpc0F1dG8nfSwNCiAgICAgICAge3RpdGxlOiAn5pON5L2cJywga2V5OiAnYWN0aW9uJywgd2lkdGg6IDE1MCwgYWxpZ246ICdjZW50ZXInLCBzbG90OiAnYWN0aW9uJ31dLA0KICAgICAgY291bnRyeUxpc3Q6IFtdLA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgIHRoaXMuZ2V0Q291bnRyeUxpc3QoKTsNCiAgICB0aGlzLmdldENsYXNzTGlzdCgpOw0KICAgIHRoaXMuZ2V0QWxsU2hvcCgpOw0KICAgIHRoaXMuZ2V0TG9nUmVmVHlwZSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0QWxsU2hvcCgpIHsNCiAgICAgIFNob3AuZ2V0QWxsKCkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzICYmIHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgdGhpcy5zaG9wTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0Q2xhc3NMaXN0KCl7DQogICAgICBsaXN0QWxsU3B1KHt9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgdGhpcy5jbGFzc05hbWVMaXN0ID1yZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmRpR3VpVHJlZSh0aGlzLmNsYXNzTmFtZUxpc3QpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBkaUd1aVRyZWUoaXRlbSkgeyAgLy/pgJLlvZLkvr/liKnmoJHnu5PmnoQNCiAgICAgIGl0ZW0uZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5pZCA9IGl0ZW1bJ2lkJ107DQogICAgICAgIGl0ZW0ubGFiZWwgPSBpdGVtWydzcHVOYW1lJ107DQogICAgICAgICFpdGVtWydjaGlsZHJlbiddIHx8IGl0ZW1bJ2NoaWxkcmVuJ10ubGVuZ3RoID09PSAwID8gZGVsZXRlIGl0ZW0uY2hpbGRyZW4gOiB0aGlzLmRpR3VpVHJlZShpdGVtLmNoaWxkcmVuKTsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvL+WFqOmAieS4juWPlua2iOWFqOmAiQ0KICAgIGhhbmRsZVNlbGVjdEFsbCAoc2VsZWN0aW9uKXsNCiAgICAgIGlmIChzZWxlY3Rpb24ubGVuZ3RoID09PSAwKSB7DQogICAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5zZWxlY3RUYWJsZS5kYXRhDQogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgdGhpcy5zZWxlY3REYXRhLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgICBpZihkYXRhW2ldLmlkID09PSB0aGlzLnNlbGVjdERhdGFbal0uaWQpew0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdERhdGEuc3BsaWNlKGosMSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGZvciggY29uc3QgdCBvZiBzZWxlY3Rpb24pew0KICAgICAgICAgIGlmKHRoaXMuc2VsZWN0RGF0YS5maW5kKGM9PiBjLmlkID09PSB0LmlkKSl7IGNvbnRpbnVlO30NCiAgICAgICAgICB0aGlzLnNlbGVjdERhdGEucHVzaCh0KQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyAg6YCJ5Lit5p+Q5LiA6KGMDQogICAgaGFuZGxlU2VsZWN0Um93IChzZWxlY3Rpb24scm93KSB7DQogICAgICB0aGlzLnNlbGVjdERhdGEucHVzaChyb3cpOw0KICAgIH0sDQogICAgLy8gIOWPlua2iOafkOS4gOihjA0KICAgIGhhbmRsZUNhbmNlbFJvdyAoc2VsZWN0aW9uLHJvdykgew0KICAgICAgdGhpcy5zZWxlY3REYXRhLm1hcCgoaXRlbSxpbmRleCk9PnsNCiAgICAgICAgaWYoaXRlbS5pZCA9PT0gcm93LmlkKXsNCiAgICAgICAgICB0aGlzLnNlbGVjdERhdGEuc3BsaWNlKGluZGV4LDEpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KDQogICAgY2hhbmdlTmFtZUNuKHYpew0KICAgICAgaWYodiA9PT0gdW5kZWZpbmVkKXsNCiAgICAgICAgdGhpcy5mb3JtLm5hbWVDbiA9JycNCiAgICAgICAgdGhpcy5mb3JtLm5hbWVFbiA9ICcnDQogICAgICB9ZWxzZXsNCiAgICAgICAgdGhpcy5mb3JtLm5hbWVDbiA9di5zcGxpdCgnKi0qJylbMF07DQogICAgICAgIHRoaXMuZm9ybS5uYW1lRW4gPSB2LnNwbGl0KCcqLSonKVsxXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlQ2FsY0xpbmsoKXsNCiAgICAgIGxldCBpZHMgPSAiIjsNCiAgICAgIGlmKHRoaXMuc2VsZWN0RGF0YSAmJiB0aGlzLnNlbGVjdERhdGEubGVuZ3RoID4wKXsNCiAgICAgICAgaWRzID0gdGhpcy5zZWxlY3REYXRhLm1hcChpdGVtPT5pdGVtWydpZCddKS5qb2luKCcsJyk7DQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgQ2xlYXJhbmNlTGluay5yZUNhbGNMaW5rKHsiaWRzIjppZHN9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygn6YeN5paw5Yy56YWNJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVTZWFyY2goKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHBhcmFtcyA9IHsuLi50aGlzLnNlYXJjaEZvcm0sIC4uLnRoaXMucGFnZUluZm99DQogICAgICBDbGVhcmFuY2VMaW5rLmxpc3RQYWdlKHBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsNCiAgICAgICAgICB0aGlzLmRhdGEgPSByZXMuZGF0YS5yZWNvcmRzOw0KICAgICAgICAgIHRoaXMucGFnZUluZm8udG90YWwgPSBOdW1iZXIocmVzLmRhdGEudG90YWwpDQogICAgICAgIH0NCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ3NlYXJjaEZvcm0nXS5yZXNldEZpZWxkcygpOw0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0U3VjY2VzcyhyZXMpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ3VwbG9hZEZpbGVSZWYnXS5jbGVhckZpbGVzKCk7DQogICAgICBpZiAocmVzWydjb2RlJ10gPT09IDApIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCflr7zlhaXmiJDlip8nKTsNCiAgICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJE1lc3NhZ2Uud2FybmluZyhyZXNbJ21lc3NhZ2UnXSk7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVJbXBvcnRGb3JtYXRFcnJvcihmaWxlKSB7DQogICAgICAvL+agvOW8j+mqjOivgeWksei0peeahOmSqeWtkA0KICAgICAgdGhpcy4kTW9kYWwuZXJyb3Ioew0KICAgICAgICB0aXRsZTogJ+aWh+S7tuagvOW8j+S4jeato+ehricsDQogICAgICAgIGNvbnRlbnQ6ICfmlofku7YgJyArIGZpbGUubmFtZSArICfmoLzlvI/kuI3mraPnoa7vvIzmlK/mjIHkuIrkvKDnmoTmlofku7bnsbvlnovvvJp4bHMseGxzeCcsDQogICAgICAgIG9rVGV4dDogJ+ehruiupCcNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlSW1wb3J0RXJyb3IoZXJyLCBmaWxlKSB7DQogICAgICB0aGlzLiRNZXNzYWdlLndhcm5pbmcoZmlsZS5tZXNzYWdlKTsNCiAgICB9LA0KICAgIGhhbmRsZU1heFNpemUoKSB7DQogICAgICB0aGlzLiRNZXNzYWdlLndhcm5pbmcoJ+WbvueJh+Wkp+Wwj+S4jeiDvei2hei/hzJNLicpOw0KICAgIH0sDQogICAgdGVtcGxhdGVFeHBvcnQoKXsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBDbGVhcmFuY2VMaW5rLmRvd25sb2FkVGVtcGxhdGUoeyJmaWxlTmFtZSI6Iua4heWFs+i/nuaOpeWvvOWFpeaooeadvy54bHMifSwgKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGNsZWFyYW5jZUxpbmtFeHBvcnQoKSB7DQogICAgICBsZXQgcGFyYW1zID0gey4uLnRoaXMuc2VhcmNoRm9ybX07DQogICAgICBwYXJhbXNbJ2ZpbGVOYW1lJ10gPSAi5riF5YWz6L+e5o6lIiArIG5ldyBEYXRlKCkuZ2V0VGltZSgpICsgIi54bHMiOw0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIENsZWFyYW5jZUxpbmsuZG93bmxvYWQocGFyYW1zLCAoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgZWRpdENsZWFyYW5jZUxpbmsocm93KSB7DQogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuSI7DQogICAgICB0aGlzLm1vZGFsID0gdHJ1ZTsNCiAgICAgIHRoaXMuZGlzYWJsZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCk7DQogICAgICB0aGlzLmZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCByb3cpOw0KICAgIH0sDQogICAgYWRkQ2xlYXJhbmNlTGluaygpIHsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75YqgIjsNCiAgICAgIHRoaXMubW9kYWwgPSB0cnVlOw0KICAgICAgdGhpcy5kaXNhYmxlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldEZvcm0oKTsNCiAgICB9LA0KDQogICAgc2F2ZUNsZWFyYW5jZUxpbmsoKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuc2F2aW5nID0gdHJ1ZTsNCiAgICAgICAgICBDbGVhcmFuY2VMaW5rLnNhdmVDbGVhcmFuY2VMaW5rKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgaWYgKHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgICAgIHRoaXMuc2F2aW5nID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfIScpOw0KICAgICAgICAgICAgICB0aGlzLnJlc2V0Rm9ybSgpOw0KICAgICAgICAgICAgICB0aGlzLm1vZGFsID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlU2VhcmNoKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnNhdmluZyA9IGZhbHNlOw0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvL+iOt+WPluebrueahOWbveWutg0KICAgIGdldENvdW50cnlMaXN0KCkgew0KICAgICAgQ29tbW9uQXBpLkxpc3REaWN0aW9uYXJ5VmFsdWVCeSgiYmFzZV9jb3VudHJ5IikudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzICYmIHJlc1snY29kZSddID09PSAwKSB7DQogICAgICAgICAgbGV0IGRhdGEgPSByZXNbJ2RhdGEnXQ0KICAgICAgICAgIGlmIChkYXRhKSB7DQogICAgICAgICAgICB0aGlzLmNvdW50cnlMaXN0ID0gZGF0YS5tYXAoaXRlbSA9PiBKU09OLnBhcnNlKGl0ZW0udmFsdWUpKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVQYWdlKHBhZ2UpIHsNCiAgICAgIHRoaXMucGFnZUluZm8ucGFnZSA9IHBhZ2U7DQogICAgICB0aGlzLmhhbmRsZVNlYXJjaCgpOw0KICAgIH0sDQogICAgaGFuZGxlUGFnZVNpemUoc2l6ZSkgew0KICAgICAgdGhpcy5wYWdlSW5mby5wYWdlID0gMTsNCiAgICAgIHRoaXMucGFnZUluZm8ubGltaXQgPSBzaXplOw0KICAgICAgdGhpcy5oYW5kbGVTZWFyY2goKTsNCiAgICB9LA0KICAgIGNhbmNlbEZvcm0oKSB7DQogICAgICB0aGlzLm1vZGFsID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgpOw0KICAgIH0sDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLmZvcm0gPSB7fTsNCiAgICB9LA0KICAgIC8v5pel5b+XDQogICAgbG9va0xvZyhyb3cpIHsNCiAgICAgIGNvbnN0IHtsb2dNb2RlbFJlZn0gPSB0aGlzLiRyZWZzOw0KICAgICAgaWYgKGxvZ01vZGVsUmVmKSB7DQogICAgICAgIGxvZ01vZGVsUmVmLnNldERlZmF1bHQocm93LmlkLCB0aGlzLnJlZlR5cGUpOw0KICAgICAgfQ0KICAgICAgdGhpcy5sb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIGdldExvZ1JlZlR5cGUoKSB7DQogICAgICBDbGVhcmFuY2VMaW5rLmdldExvZ1JlZlR5cGUoKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXNbJ2NvZGUnXSA9PT0gMCkgew0KICAgICAgICAgIHRoaXMucmVmVHlwZSA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA+HA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/view/module/custom/base/clearanceLink", "sourcesContent": ["<!--\r\n@create date 2020-12-14\r\n<AUTHOR>\r\n@desc 报关清关地址维护表\r\n-->\r\n<template>\r\n  <Card>\r\n    <Form ref=\"searchForm\" :model=\"searchForm\" inline>\r\n      <FormItem prop=\"spuName\">\r\n        <Input v-model=\"searchForm.spuName\" placeholder=\"请输入产品型号\"/>\r\n      </FormItem>\r\n      <FormItem prop=\"country\">\r\n        <Select v-model=\"searchForm.country\" filterable clearable placeholder=\"请选择国家\" style=\"width:150px\">\r\n          <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n          </Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem prop=\"isAuto\" :clear=\"true\">\r\n        <Select type=\"text\" v-model=\"searchForm.isAuto\" placeholder=\"更新形式\" style=\"width:160px\">\r\n          <Option v-for=\"(item,index) in statusList\" :value=\"item.key\" :key=\"index\">{{ item['value'] }}</Option>\r\n        </Select>\r\n      </FormItem>\r\n      <FormItem>\r\n        <Button type=\"primary\" @click=\"handleSearch()\">查询</Button>\r\n        <Button @click=\"handleReset()\" style=\"margin-left:10px\">重置</Button>\r\n      </FormItem>\r\n    </Form>\r\n    <div style=\"margin-bottom:10px\">\r\n      <div style=\"float:left\">\r\n        <Upload ref=\"uploadFileRef\" name=\"importFile\" :action=\"importURl\" :max-size=\"10240\"\r\n                :on-success=\"handleImportSuccess\" :format=\"['xls', 'xlsx']\" :show-upload-list=\"false\"\r\n                :on-format-error=\"handleImportFormatError\"\r\n                :on-error=\"handleImportError\" :headers=\"loginInfo\" :on-exceeded-size=\"handleMaxSize\">\r\n          <Button class=\"search-btn\" type=\"primary\">导入</Button>\r\n        </Upload>\r\n      </div>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"addClearanceLink\">新增</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"reCalcLink\">重新匹配</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"templateExport\">导入模板</Button>\r\n      <Button class=\"search-btn\" style=\"margin-left:10px;\" @click=\"clearanceLinkExport\">导出</Button>\r\n    </div>\r\n    <Table :border=\"true\" :columns=\"column\" :data=\"data\" :loading=\"loading\" ref=\"selectTable\"\r\n           @on-select=\"handleSelectRow\"\r\n           @on-select-cancel=\"handleCancelRow\"\r\n           @on-select-all=\"handleSelectAll\"\r\n           @on-select-all-cancel=\"handleSelectAll\">\r\n      <template v-slot:photoUrl=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n            {{ row['photoUrl']}}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"min-width: 300px\" v-copytext=\"row['photoUrl']\">\r\n            {{row['photoUrl'].length>50?(row['photoUrl'].substring(0,50)+\"...\"):row['photoUrl'] }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:goodsUrl=\"{ row }\">\r\n        <Tooltip :transfer=\"true\" placement=\"right-end\" :max-width=\"500\">\r\n          <div slot=\"content\" style=\"word-break: break-all; white-space: pre-wrap\">\r\n            {{ row['goodsUrl']}}\r\n          </div>\r\n          <div class=\"overflowText\" style=\"min-width: 300px\" v-copytext=\"row['goodsUrl']\">\r\n            {{row['goodsUrl'].length>50?(row['goodsUrl'].substring(0,50)+\"...\"):row['goodsUrl'] }}\r\n          </div>\r\n        </Tooltip>\r\n      </template>\r\n      <template v-slot:country=\"{row}\">\r\n        <span v-for=\"item in countryList\" v-if=\"item['two_code'] === row.country\">{{ item['name_cn'] }}</span>\r\n      </template>\r\n      <template v-slot:price=\"{row}\">\r\n        <span v-if=\"row['price'] && row['currency']\">{{ row['price']+\"(\"+row['currency']+\")\"}}</span>\r\n        <span v-else>{{ row['price']||0}}</span>\r\n      </template>\r\n      <template v-slot:isAuto=\"{row}\">\r\n        <span v-for=\"item in statusList\" v-if=\"item['key'] === row['isAuto']\">{{ item['value'] }}</span>\r\n      </template>\r\n      <template v-slot:action=\"{row}\">\r\n        <Button size=\"small\" type=\"info\" @click=\"editClearanceLink(row)\" style=\"margin:0 2px\">编辑</Button>\r\n        <Button size=\"small\" type=\"info\" @click=\"lookLog(row)\" style=\"margin:0 2px\">日志</Button>\r\n      </template>\r\n    </Table>\r\n    <Page :total=\"pageInfo.total \" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" :show-elevator=\"true\"\r\n          :show-sizer=\"true\"\r\n          :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize' :transfer=\"true\"></Page>\r\n    <Modal :width=\"530\" v-model=\"modal\" :title=\"title\" @on-cancel=\"cancelForm\">\r\n      <Spin :fix=\"true\" v-if=\"spinShow\">加载中...</Spin>\r\n      <Form ref=\"form\" :model=\"form\" inline label-position=\"right\" :label-width=\"110\">\r\n        <FormItem prop=\"className\" label=\"报关类目\">\r\n          <div class=\"widthClass\">\r\n            <treeselect v-model=\"form.parentId\"\r\n                        :options=\"classNameList\"\r\n                        :disabled=\"disabled\"\r\n                        @input=\"changeNameCn\"\r\n                        :default-expand-level=\"1\"\r\n                        noResultsText=\"暂无数据\"\r\n                        placeholder=\"请选清关品名\" />\r\n          </div>\r\n        </FormItem>\r\n        <FormItem label=\"国家\" prop=\"country\" :rules=\"{required: true, message: '不能为空', trigger: 'blur'}\">\r\n          <Select v-model=\"form.country\" filterable clearable placeholder=\"请选择所在国家\" class=\"widthClass\" :disabled=\"disabled\">\r\n            <Option v-for=\"(item,index) in countryList\" :value=\"item['two_code']\" :key=\"index\">{{ item['name_cn'] }}\r\n            </Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem prop=\"shopId\" :clear=\"true\" label=\"店铺\">\r\n          <Select type=\"text\" v-model=\"form.shopId\" placeholder=\"店铺\" class=\"widthClass\" >\r\n            <Option v-for=\"(item,index) in shopList\" :value=\"item.id\" :key=\"index\">{{ item.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem label=\"销售SKU\" prop=\"sellerSku\">\r\n          <Input v-model.trim=\"form.sellerSku\" placeholder=\"请输入\" class=\"widthClass\"/>\r\n        </FormItem>\r\n        <FormItem label=\"更新形式\">\r\n          <RadioGroup v-model=\"form.isAuto\" type=\"button\">\r\n            <Radio v-for=\"v in statusList\" :label=\"v.key\" v-bind:key=\"v.key\">{{ v['value'] }}</Radio>\r\n          </RadioGroup>\r\n        </FormItem>\r\n      </Form>\r\n      <div slot=\"footer\">\r\n        <Button type=\"primary\" :disabled=\"disabled\" :loading=\"saving\" @click=\"saveClearanceLink\">保存</Button>\r\n        <Button @click=\"cancelForm\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <LogModel ref=\"logModelRef\" :logVisible=\"logVisible\" :onCancel=\"()=>logVisible=false\"/>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport CommonApi from \"@/api/base/commonApi\";\r\nimport ClearanceLink from \"@/api/custom/clearanceLink\";\r\nimport LogModel from \"@/view/module/base/bussinessLog/logModel.vue\";\r\nimport {getToken, getUrl} from \"@/libs/util\";\r\nimport {listAllSpu} from '@/api/basf/product.js'\r\nimport Shop from \"@/api/basf/shop\";\r\n\r\nexport default {\r\n  components: {LogModel},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      saving: false,\r\n      modal: false,\r\n      spinShow: false,\r\n      disabled: false,\r\n      logVisible: false,\r\n      refType: null,\r\n      selectData:[],\r\n      title: '',\r\n      statusList:[{key:0,value:\"自动\"},{key:1,value:\"手动\"}],\r\n      searchForm: {spuName: '', country: '', isAuto:''},\r\n      pageInfo: {total: 0, page: 1, limit: 10},\r\n      loginInfo: {\r\n        Accept: 'application/json,text/plain, */*, */*',\r\n        mode: 'cors',\r\n        Authorization: 'Bearer ' + getToken()\r\n      },\r\n      importURl: getUrl() + \"/base/clearanceLink/importFile\",\r\n      form: {parentId: null, country: null, sellerSku: null, shopId: null},\r\n      data: [],\r\n      classNameList:[],\r\n      shopList:[],\r\n      column: [\r\n        {title: '选项',type: 'selection',width: 70,align: 'center',fixed:'left',},\r\n        {title: '产品型号', key: 'spuName', minWidth: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.spuName}>{row.spuName}</span>}},\r\n        {title: '清关国', key: 'country', width: 100, align: 'center', slot: 'country'},\r\n        {title: '平台', key: 'platformId', width: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.platformId === '1'?'AMAZON':'WALMART'}>{row.platformId === '1'?'AMAZON':'WALMART'}</span>}},\r\n        {title: '网店', key: 'shopName', width: 100, align: 'center',render: (h, {row}) => {return <span v-copytext={row.shopName}>{row.shopName}</span>}},\r\n        {title: 'asin', key: 'asin', width: 120, align: 'center',render: (h, {row}) => {return <span v-copytext={row.asin}>{row.asin}</span>}},\r\n        {title: 'sellerSku', key: 'sellerSku', minWidth: 150, align: 'center',render: (h, {row}) => {return <span v-copytext={row.sellerSku}>{row.sellerSku}</span>}},\r\n        {title: '销售价', key: 'price', width: 120, align: 'center',slot:'price'},\r\n        {title: '商品链接', key: 'goodsUrl', minWidth: 250, align: 'center',slot:'goodsUrl'},\r\n        {title: '商品图片', key: 'photoUrl', minWidth: 300, align: 'center',slot:'photoUrl'},\r\n        {title: '更新形式', key: 'isAuto', width: 100, align: 'center',slot:'isAuto'},\r\n        {title: '操作', key: 'action', width: 150, align: 'center', slot: 'action'}],\r\n      countryList: [],\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch();\r\n    this.getCountryList();\r\n    this.getClassList();\r\n    this.getAllShop();\r\n    this.getLogRefType();\r\n  },\r\n  methods: {\r\n    getAllShop() {\r\n      Shop.getAll().then(res => {\r\n        if (res && res['code'] === 0) {\r\n          this.shopList = res.data;\r\n        }\r\n      })\r\n    },\r\n    getClassList(){\r\n      listAllSpu({}).then((res) => {\r\n        if (res['code'] === 0) {\r\n          this.classNameList =res.data;\r\n          this.diGuiTree(this.classNameList)\r\n        }\r\n      })\r\n    },\r\n    diGuiTree(item) {  //递归便利树结构\r\n      item.forEach(item => {\r\n        item.id = item['id'];\r\n        item.label = item['spuName'];\r\n        !item['children'] || item['children'].length === 0 ? delete item.children : this.diGuiTree(item.children);\r\n      })\r\n    },\r\n    //全选与取消全选\r\n    handleSelectAll (selection){\r\n      if (selection.length === 0) {\r\n        let data = this.$refs.selectTable.data\r\n        for (let i = 0; i < data.length; i++) {\r\n          for (let j = 0; j < this.selectData.length; j++) {\r\n            if(data[i].id === this.selectData[j].id){\r\n              this.selectData.splice(j,1)\r\n            }\r\n          }\r\n        }\r\n      } else {\r\n        for( const t of selection){\r\n          if(this.selectData.find(c=> c.id === t.id)){ continue;}\r\n          this.selectData.push(t)\r\n        }\r\n      }\r\n    },\r\n    //  选中某一行\r\n    handleSelectRow (selection,row) {\r\n      this.selectData.push(row);\r\n    },\r\n    //  取消某一行\r\n    handleCancelRow (selection,row) {\r\n      this.selectData.map((item,index)=>{\r\n        if(item.id === row.id){\r\n          this.selectData.splice(index,1)\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n    changeNameCn(v){\r\n      if(v === undefined){\r\n        this.form.nameCn =''\r\n        this.form.nameEn = ''\r\n      }else{\r\n        this.form.nameCn =v.split('*-*')[0];\r\n        this.form.nameEn = v.split('*-*')[1];\r\n      }\r\n    },\r\n    reCalcLink(){\r\n      let ids = \"\";\r\n      if(this.selectData && this.selectData.length >0){\r\n        ids = this.selectData.map(item=>item['id']).join(',');\r\n      }\r\n      this.loading = true;\r\n      ClearanceLink.reCalcLink({\"ids\":ids}).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.$Message.success('重新匹配');\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleSearch() {\r\n      this.loading = true;\r\n      let params = {...this.searchForm, ...this.pageInfo}\r\n      ClearanceLink.listPage(params).then(res => {\r\n        if (res['code'] === 0) {\r\n          this.data = res.data.records;\r\n          this.pageInfo.total = Number(res.data.total)\r\n        }\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.$refs['searchForm'].resetFields();\r\n    },\r\n    handleImportSuccess(res) {\r\n      this.$refs['uploadFileRef'].clearFiles();\r\n      if (res['code'] === 0) {\r\n        this.$Message.success('导入成功');\r\n        this.handleSearch();\r\n      } else {\r\n        this.$Message.warning(res['message']);\r\n      }\r\n    },\r\n    handleImportFormatError(file) {\r\n      //格式验证失败的钩子\r\n      this.$Modal.error({\r\n        title: '文件格式不正确',\r\n        content: '文件 ' + file.name + '格式不正确，支持上传的文件类型：xls,xlsx',\r\n        okText: '确认'\r\n      });\r\n    },\r\n    handleImportError(err, file) {\r\n      this.$Message.warning(file.message);\r\n    },\r\n    handleMaxSize() {\r\n      this.$Message.warning('图片大小不能超过2M.');\r\n    },\r\n    templateExport(){\r\n      this.loading = true;\r\n      ClearanceLink.downloadTemplate({\"fileName\":\"清关连接导入模板.xls\"}, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    clearanceLinkExport() {\r\n      let params = {...this.searchForm};\r\n      params['fileName'] = \"清关连接\" + new Date().getTime() + \".xls\";\r\n      this.loading = true;\r\n      ClearanceLink.download(params, () => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    editClearanceLink(row) {\r\n      this.title = \"修改\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n      this.form = Object.assign({}, row);\r\n    },\r\n    addClearanceLink() {\r\n      this.title = \"添加\";\r\n      this.modal = true;\r\n      this.disabled = false;\r\n      this.resetForm();\r\n    },\r\n\r\n    saveClearanceLink() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          this.saving = true;\r\n          ClearanceLink.saveClearanceLink(this.form).then(res => {\r\n            if (res['code'] === 0) {\r\n              this.saving = false;\r\n              this.$Message.success('保存成功!');\r\n              this.resetForm();\r\n              this.modal = false;\r\n              this.handleSearch();\r\n            }\r\n          }).finally(() => {\r\n            this.saving = false;\r\n          })\r\n        }\r\n      })\r\n    },\r\n    //获取目的国家\r\n    getCountryList() {\r\n      CommonApi.ListDictionaryValueBy(\"base_country\").then(res => {\r\n        if (res && res['code'] === 0) {\r\n          let data = res['data']\r\n          if (data) {\r\n            this.countryList = data.map(item => JSON.parse(item.value));\r\n          }\r\n        }\r\n      })\r\n    },\r\n    handlePage(page) {\r\n      this.pageInfo.page = page;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    cancelForm() {\r\n      this.modal = false;\r\n      this.resetForm();\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields();\r\n      this.form = {};\r\n    },\r\n    //日志\r\n    lookLog(row) {\r\n      const {logModelRef} = this.$refs;\r\n      if (logModelRef) {\r\n        logModelRef.setDefault(row.id, this.refType);\r\n      }\r\n      this.logVisible = true;\r\n    },\r\n    getLogRefType() {\r\n      ClearanceLink.getLogRefType().then(res => {\r\n        if (res['code'] === 0) {\r\n          this.refType = res.data;\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.widthClass {\r\n  width: 350px\r\n}\r\n</style>\r\n"]}]}