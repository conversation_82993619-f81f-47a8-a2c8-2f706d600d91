package com.sy.erp.server.configuration;

import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.lang.NonNull;
import org.springframework.util.Assert;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Log4j2
public class DynamicRoutingDataSource extends AbstractRoutingDataSource {

    private Object aimoProdMysqlDataSource;
    private Object aimoTestMysqlDataSource;

    @Override
    public void afterPropertiesSet() {
        setDefaultTargetDataSource(aimoTestMysqlDataSource);
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceEnum.AimoProdMysql.getValue(), aimoProdMysqlDataSource);
        targetDataSources.put(DataSourceEnum.AimoTestMysql.getValue(), aimoTestMysqlDataSource);
        setTargetDataSources(targetDataSources);
        super.afterPropertiesSet();
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return DynamicDataSourceContextHolder.getDataSourceType();
    }

    @Override

    protected @NonNull DataSource determineTargetDataSource() {
        DataSource dataSource = super.determineTargetDataSource();
        Assert.notNull(dataSource, "DataSource router not initialized");
        if (this.logger.isDebugEnabled()) {
            Object lookupKey = determineCurrentLookupKey();
            this.logger.debug(lookupKey);
        }
        return dataSource;
    }

    public void setAimoProdMysqlDataSource(Object aimoProdMysqlDataSource) {
        this.aimoProdMysqlDataSource = aimoProdMysqlDataSource;
    }

    public void setAimoTestMysqlDataSource(Object aimoTestMysqlDataSource) {
        this.aimoTestMysqlDataSource = aimoTestMysqlDataSource;
    }


}
