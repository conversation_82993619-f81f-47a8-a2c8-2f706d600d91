{"remainingRequest": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\user\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\src\\view\\module\\base\\user\\index.vue", "mtime": 1753847200919}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Users\\admini\\Desktop\\dev\\sy_declare_ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/view/module/base/user", "sourcesContent": ["<template>\r\n  <div>\r\n    <Card class=\"userManage\">\r\n      <Form ref=\"searchForm\" :model=\"pageInfo\" class=\"searchForm\" inline\r\n            @keydown.enter.native=\" e => {e.preventDefault();handleSearch(1);}\">\r\n        <FormItem prop=\"userName\">\r\n          <Input style=\"width: 160px\" v-model=\"pageInfo.userName\" placeholder=\"请输入登录名\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"nickName\">\r\n          <Input style=\"width: 180px\" v-model=\"pageInfo.nickName\" placeholder=\"请输入姓名\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"formSelectRoles\">\r\n          <Select v-model=\"formSelectRoles\" multiple=\"multiple\" placeholder=\"角色\" @on-change=\"getRuleIds\"\r\n                  :max-tag-count=\"1\" style=\"width:250px\" :multiple=\"true\" :filterable=\"true\" :transfer=\"false\">\r\n            <Option v-for=\"(item, index) in roleAll\" :value=\"item.id\" :key=\"index\">{{ item.roleName }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n\r\n        <FormItem prop=\"departmentId\">\r\n          <DepartmentSelect v-model=\"pageInfo.departmentId\" placeholder=\"所属部门\" groupName=\"user-manage-search\"/>\r\n        </FormItem>\r\n        <FormItem prop=\"status\">\r\n          <Select v-model=\"pageInfo.status\" style=\"width: 100px\" placeholder=\"请选择状态\" :clearable=\"false\" :transfer=\"true\">\r\n            <Option v-for=\"v in userStatusOps\" :value=\"v.key\" :key=\"v.key\">{{ v.name }}</Option>\r\n          </Select>\r\n        </FormItem>\r\n        <FormItem :label-width=\"0\">\r\n          <Button type=\"primary\" @click=\"handleSearch(1)\">查询</Button>\r\n          <Button @click=\"handleResetForm('searchForm')\" style=\"\">重置</Button>\r\n        </FormItem>\r\n      </Form>\r\n      <div class=\"search-con search-con-top\">\r\n        <Button @click=\"personSyn('searchForm')\" :loading=\"flag\" v-if=\"hasAuthority('userSync')\">人员同步</Button>\r\n        <Button type=\"primary\" @click=\"handleModal('add',null)\" v-if=\"hasAuthority('userAdd')\" style=\"margin-left: 15px\">\r\n          <span>添加</span>\r\n        </Button>\r\n      </div>\r\n      <Table :border=\"true\" :columns=\"columns\" :data=\"data\" @on-sort-change=\"roleSort\" :loading=\"loading\"\r\n             ref=\"autoTableRef\" :max-height=\"autoTableHeight($refs.autoTableRef)\">\r\n        <template v-slot:status=\"{row}\">\r\n          <Badge v-for=\"v in userStatusOps\" :text=\"v.name\" v-if=\"v.key === row.status\"\r\n                 :status=\"v.key === 0?'success':(v.key ===1?'error':'warning')\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:intentAccess=\"{ row }\">\r\n          <Badge v-for=\"v in yesNoOps\" :text=\"v.name\" v-if=\"v.key === row.intentAccess\"\r\n                 :status=\"v.key === 0?'success':'warning'\" v-bind:key=\"v.key\"></Badge>\r\n        </template>\r\n        <template v-slot:action=\"{ row }\">\r\n          <a @click=\"handleModal('view',row)\">查看</a>&nbsp\r\n          <a @click=\"handleModal('edit',row)\" v-if=\"hasAuthority('userEdit')\" >编辑</a>&nbsp\r\n        </template>\r\n        <template v-slot:isAllShop=\"{ row }\">\r\n          <Checkbox\r\n              v-model=\"row.isAllShop === 1\"\r\n              @on-change=\"value => checkAllShop(value, row)\"\r\n              v-if=\"changeShow\" :disabled=\"changeLoading\"\r\n          ></Checkbox>\r\n        </template>\r\n      </Table>\r\n      <Page :transfer=\"true\" :total=\"pageInfo.total\" :current=\"pageInfo.page\" :page-size=\"pageInfo.limit\" size=\"small\"\r\n            :show-elevator=\"true\"\r\n            :show-sizer=\"true\" :show-total=\"true\" @on-change=\"handlePage\" @on-page-size-change=\"handlePageSize\">\r\n      </Page>\r\n    </Card>\r\n    <Modal v-model=\"modalVisible\" :title=\"modalTitle\" width=\"40\" :styles=\"{ top: '20px' }\" @on-cancel=\"handleReset\"\r\n           @on-visible-change=\"onVisibleChange\">\r\n      <Tabs @on-click=\"handleTabClick\" :value=\"current\">\r\n        <TabPane label=\"用户信息\" name=\"form1\">\r\n          <Form v-show=\"current === 'form1'\" ref=\"form1\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"姓名\" prop=\"nickName\">\r\n              <Input v-model=\"formItem.nickName\" placeholder=\"请输入\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"登录名\" prop=\"userName\">\r\n              <Input :disabled=\"!!formItem.id\" v-model=\"formItem.userName\" placeholder=\"请输入\"></Input>\r\n            </FormItem>\r\n            <FormItem v-if=\"!formItem.id\" label=\"登录密码\" prop=\"password\">\r\n              <Input type=\"password\" v-model=\"formItem.password\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem v-if=\"!formItem.id\" label=\"再次确认密码\" prop=\"passwordConfirm\">\r\n              <Input type=\"password\" v-model=\"formItem.passwordConfirm\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"所属公司\" prop=\"companyId\">\r\n              <Select v-model=\"formItem.companyId\" @on-change=\"getDeptList\">\r\n                <Option v-for=\"(item, index) in companyList\" :value=\"item.id\" :key=\"index\">{{\r\n                    item.companyName\r\n                  }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"所属部门\" prop=\"departmentId\">\r\n              <DepartmentSelect v-model=\"formItem.departmentId\" groupName=\"user-manage-edit\" width=\"100%\"\r\n                                :companyId=\"formItem.companyId || '0'\" :appendToBody=\"false\"/>\r\n            </FormItem>\r\n            <FormItem label=\"邮箱\" prop=\"email\">\r\n              <Input v-model=\"formItem.email\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"手机号\" prop=\"mobile\">\r\n              <Input v-model=\"formItem.mobile\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"外网访问\">\r\n              <RadioGroup v-model=\"formItem.intentAccess\" type=\"button\">\r\n                <Radio v-for=\"v in yesNoOps\" :label=\"v.key\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"状态\">\r\n              <RadioGroup v-model=\"formItem.status\" type=\"button\">\r\n                <Radio v-for=\"v in userStatusOps\" :label=\"v.key\" v-if=\"v.key !== -1\" v-bind:key=\"v.key\">{{ v.name }}</Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"描述\">\r\n              <Input v-model=\"formItem.userDesc\" type=\"textarea\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"分配角色\" name=\"form2\">\r\n          <Form v-show=\"current === 'form2'\" ref=\"form2\" :model=\"formItem\" :label-width=\"100\" :rules=\"formItemRules\">\r\n            <FormItem label=\"\" prop=\"grantRoles\" style=\"margin-bottom:0;\">\r\n              <Checkbox v-model=\"bool1\" @on-change=\"changeRoleCheck\">只显示已勾选的角色</Checkbox>\r\n            </FormItem>\r\n            <FormItem label=\"分配角色\" prop=\"grantRoles\">\r\n              <CheckboxGroup v-model=\"formItem.grantRoles\">\r\n                <Checkbox v-for=\"item in selectRoles\" :key=\"item.id\" :label=\"item.id\"><span>{{item.roleName }}</span></Checkbox>\r\n              </CheckboxGroup>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"分配权限\" name=\"form3\">\r\n          <Alert type=\"info\" :show-icon=\"true\">支持用户单独分配功能权限<code>(除角色已经分配菜单功能,禁止勾选!)</code>\r\n          </Alert>\r\n          <Form @keydown.enter.native=\" e => {e.preventDefault();}\" v-show=\"current === 'form3'\" ref=\"form3\"\r\n                :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"菜单查询\" prop=\"menuName\" style=\"margin-bottom:10px;\">\r\n              <Input v-model=\"formItem.menuName\" @on-enter=\"queryMenuName\" placeholder=\"请输入菜单名称，且点击回车\"\r\n                     style=\"width:200px;margin-right:10px\"/>\r\n            </FormItem>\r\n            <FormItem label=\"功能菜单\" prop=\"grantMenus\">\r\n              <tree-table ref=\"tree\" style=\"max-height:450px;overflow: auto\" expand-key=\"menuName\" :expand-type=\"false\" :is-fold=\"false\"\r\n                          :tree-type=\"true\" :selectable=\"true\" :columns=\"treeColumns\" :data=\"selectMenus\">\r\n                <template v-slot:operation=\"scope\">\r\n                  <CheckboxGroup v-model=\"formItem.grantActions\">\r\n                    <Checkbox :disabled=\"item.disabled\" v-for=\"item in scope.row['actionList']\" :label=\"item['authorityId']\" v-bind:key=\"item['authorityId']\">\r\n                      <span :title=\"item.actionDesc\">{{ item.actionName }}</span>\r\n                    </Checkbox>\r\n                  </CheckboxGroup>\r\n                </template>\r\n              </tree-table>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"授权网店\" name=\"form4\">\r\n          <Form @keydown.enter.native=\" e => {e.preventDefault();}\" v-show=\"current === 'form4'\" ref=\"form4\" :model=\"formItem\"\r\n                :label-width=\"100\" :rules=\"formItemRules\">\r\n            <FormItem label=\"店铺名称\" prop=\"searchVal\">\r\n              <Input v-model=\"formItem.searchVal\" @on-enter=\"queryShopName\" placeholder=\"请输入店铺名称，点击回车健\"/>\r\n            </FormItem>\r\n            <div style=\"position:relative;padding-left:40px;\">\r\n              <Spin :fix=\"true\" v-if=\"checkBoxLoading\" size=\"large\"></Spin>\r\n              <div v-for=\"(items1, index) in selectShops\" style=\"border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;\">\r\n                <CheckboxGroups ref=\"CheckboxGroupsEls\" :checkItem=\"items1\" :index=\"index\" :checkAllGroup=\"checkAllGroup[index]\"\r\n                    :loading=\"checkBoxLoading\" @onChange=\"changeCheckBox\"\r\n                ></CheckboxGroups>\r\n              </div>\r\n            </div>\r\n          </Form>\r\n        </TabPane>\r\n        <TabPane :disabled=\"!formItem.id\" label=\"修改密码\" name=\"form5\">\r\n          <Form v-show=\"current === 'form5'\" ref=\"form5\" :model=\"formItem\" :rules=\"formItemRules\" :label-width=\"100\">\r\n            <FormItem label=\"登录名\" prop=\"userName\">\r\n              <Input :disabled=\"!!formItem.id\" v-model=\"formItem.userName\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"登录密码\" prop=\"password\">\r\n              <Input type=\"password\" v-model=\"formItem.password\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n            <FormItem label=\"再次确认密码\" prop=\"passwordConfirm\">\r\n              <Input type=\"password\" v-model=\"formItem.passwordConfirm\" placeholder=\"请输入内容\"></Input>\r\n            </FormItem>\r\n          </Form>\r\n        </TabPane>\r\n      </Tabs>\r\n      <div class=\"drawer-footer\" style=\"border-top: none\">\r\n        <Button type=\"default\" @click=\"handleReset\">取消</Button>&nbsp;&nbsp;&nbsp;\r\n        <Button type=\"primary\" v-if=\"ActionType !== 'view'\" @click=\"handleSubmit\" :loading=\"saving\" :disabled=\"!hasAuthority('userEdit')\">保存</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport Role from \"@/api/base/role\";\r\nimport {listConvertTree} from \"@/libs/util\";\r\nimport Authority from \"@/api/system/authority_1\";\r\nimport DepartmentSelect from \"_c/department-select/index.vue\"; // 引入部门选择组件\r\nimport {autoTableHeight, isEmpty} from \"@/libs/tools.js\";\r\nimport Common from '@/api/basic/common'\r\nimport CommonApi from '@/api/base/commonApi'\r\nimport Shop from '@/api/basf/shop'\r\nimport User from '@/api/base/user'\r\nimport {getByCompanyId} from '@/api/base/department'\r\nimport CheckboxGroups from \"@/view/module/basic/ShopAuthority/components/checkboxGroups.vue\";\r\nexport default {\r\n  name: \"systemUser\",\r\n  components: {CheckboxGroups, DepartmentSelect},\r\n  data() {\r\n    const validateEn = (rule, value, callback) => {\r\n      let reg = /^[A-Za-z0-9_\\-]+$/gi;\r\n      if (value === \"\") {\r\n        callback(new Error(\"登录名不能为空\"));\r\n      } else if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"只允许字母、数字、下划线,英文中划线\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatePass = (rule, value, callback) => {\r\n      let reg2 = /^.{6,18}$/;\r\n      if (value === \"\") {\r\n        callback(new Error(\"请输入密码\"));\r\n      } else if (value !== this.formItem.password) {\r\n        callback(new Error(\"两次输入密码不一致\"));\r\n      } else if (value !== \"\" && !reg2.test(value)) {\r\n        callback(new Error(\"长度6到18个字符\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatePassConfirm = (rule, value, callback) => {\r\n      if (value === \"\") {\r\n        callback(new Error(\"请再次输入密码\"));\r\n      } else if (value !== this.formItem.password) {\r\n        callback(new Error(\"两次输入密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateMobile = (rule, value, callback) => {\r\n      let reg = /^1\\d{10}$/;\r\n      if (value !== \"\" && !reg.test(value)) {\r\n        callback(new Error(\"手机号码格式不正确\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      ActionType: 'none',\r\n      autoTableHeight,\r\n      userStatusOps: Common.userStatusOps,\r\n      yesNoOps: Common.yesNoOps,\r\n      checkBoxLoading:false,\r\n      loading: false,\r\n      saving: false,\r\n      modalVisible: false,\r\n      changeShow:true,\r\n      changeLoading:false,\r\n      modalTitle: \"\",\r\n      current: \"form1\",\r\n      forms: [\"form1\", \"form2\", \"form3\", \"form4\", \"form5\"],\r\n      selectMenus: [],\r\n      selectRoles: [],\r\n      selectShops: [],\r\n      companyList: [],\r\n      deptSearchList: [],\r\n      selectPlatforms: [],\r\n      checkAllGroup: [],\r\n      selectTreeData: [],\r\n      formSelectRoles: [],\r\n      pageInfo: {\r\n        page: 1,\r\n        pageSize: 10,\r\n        sort: \"createTime\",\r\n        order: \"desc\",\r\n        userName: '',\r\n        nickName: '',\r\n        roleIds: '',\r\n        departmentId: '',\r\n        status: -1\r\n      },\r\n      roleAll: [],\r\n      formItemRules: {\r\n        departmentId: [{required: true, message: \"部门不能为空\"}],\r\n        companyId: [\r\n          {required: true, message: \"公司不能为空\", trigger: \"blur\"}\r\n        ],\r\n        password: [\r\n          {required: true, validator: validatePass, trigger: \"blur\"}\r\n        ],\r\n        passwordConfirm: [\r\n          {required: true, validator: validatePassConfirm, trigger: \"blur\"}\r\n        ],\r\n        searchVal: [\r\n          {required: false, trigger: \"blur\"}\r\n        ],\r\n        nickName: [\r\n          {required: true, message: \"姓名不能为空\", trigger: \"blur\"}\r\n        ],\r\n        email: [{required: false, type: \"email\", message: \"邮箱格式不正确\", trigger: \"blur\"}],\r\n        mobile: [{validator: validateMobile, trigger: \"blur\"}]\r\n      },\r\n      formItem: {\r\n        userId: \"\",\r\n        userName: \"\",\r\n        nickName: \"\",\r\n        enName: \"\",\r\n        dingId: \"\",\r\n        password: \"\",\r\n        passwordConfirm: \"\",\r\n        status: 0,\r\n        intentAccess: 1,\r\n        companyId: \"\",\r\n        email: \"\",\r\n        mobile: \"\",\r\n        userDesc: \"\",\r\n        searchVal:\"\",\r\n        avatar: \"\",\r\n        grantRoles: [],\r\n        grantShops: [],\r\n        grantActions: [],\r\n        grantMenus: [],\r\n        departmentId: null\r\n      },\r\n      columns: [\r\n        {\r\n          title: \"登录名\",\r\n          key: \"userName\",\r\n          align: \"center\",\r\n          minWidth: 120,\r\n          render: (h, {row}) => (\r\n            <span v-copytext={row.userName}>{row.userName}</span>\r\n          )\r\n        },\r\n        {\r\n          title: \"姓名\",\r\n          key: \"nickName\",\r\n          align: \"center\",\r\n          minWidth: 120,\r\n          render: (h, {row}) => (\r\n            <span v-copytext={row.nickName}>{row.nickName}</span>\r\n          )\r\n        },\r\n        {\r\n          title: '所属公司',\r\n          key: 'companyName',\r\n          width: 200\r\n        },\r\n        {\r\n          title: \"所属部门\",\r\n          key: \"departmentName\",\r\n          minWidth: 200\r\n        },\r\n        {\r\n          title: \"角色\",\r\n          key: \"roleNames\",\r\n          align: \"center\",\r\n          minWidth: 150,\r\n          sortable: true\r\n        },\r\n        {\r\n          title: \"状态\",\r\n          slot: \"status\",\r\n          key: \"status\",\r\n          align: \"center\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"外网访问\",\r\n          key: \"intentAccess\",\r\n          align: \"center\",\r\n          slot: \"intentAccess\",\r\n          width: 100\r\n        },\r\n        {\r\n          title: \"注册时间\",\r\n          key: \"createTime\",\r\n          align: \"center\",\r\n          width: 145\r\n        },\r\n        {\r\n          title: '描述',\r\n          key: 'userDesc',\r\n          width: 180,\r\n        },\r\n        {\r\n          title: \"全部店铺\",\r\n          key: \"isAllShop\",\r\n          width: 80,\r\n          slot: \"isAllShop\",\r\n          align: \"center\"\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          slot: \"action\",\r\n          fixed: \"right\",\r\n          align: \"center\",\r\n          width: 120\r\n        }\r\n      ],\r\n      treeColumns: [\r\n        {\r\n          title: \"菜单\",\r\n          key: \"menuName\",\r\n          minWidth: \"250px\"\r\n        },\r\n        {\r\n          title: \"操作\",\r\n          type: \"template\",\r\n          template: \"operation\",\r\n          minWidth: \"200px\"\r\n        }\r\n      ],\r\n      data: [],\r\n      flag: false,\r\n      bool1: false,\r\n      bool2: false,\r\n      productCategoryOps: [],\r\n      checkedIds: [] //勾选的类目、系列id\r\n    };\r\n  },\r\n  mounted: function () {\r\n    this.handleSearch();\r\n    this.handleCompany();\r\n    this.getRolesAll();\r\n  },\r\n  methods: {\r\n    changeRoleCheck(v) {\r\n      if (v) {\r\n        this.selectRoles = this.CommselectRoles.filter(item => this.formItem.grantRoles.includes(item.roleId))\r\n      } else {\r\n        this.selectRoles = this.CommselectRoles;\r\n      }\r\n    },\r\n    queryMenuName() {\r\n      const commData = JSON.parse(JSON.stringify(this.CommMenus))\r\n      let mapTree = (value, arr) => {\r\n        let newArr = [];\r\n        arr.forEach(element => {\r\n          if (element.menuName.indexOf(value) > -1) { // 判断条件\r\n            newArr.push(element);\r\n          } else {\r\n            if (element.children && element.children.length > 0) {\r\n              let reData = mapTree(value, element.children);\r\n              if (reData && reData.length > 0) {\r\n                let obj = {...element, children: reData};\r\n                newArr.push(obj);\r\n              }\r\n            }\r\n          }\r\n        });\r\n        return newArr;\r\n      };\r\n      if (isEmpty(this.formItem.menuName)) {\r\n        this.selectMenus = this.CommMenus;\r\n      } else {\r\n        this.selectMenus = mapTree(this.formItem.menuName, commData)\r\n      }\r\n    },\r\n    queryShopName(){\r\n      this.handleLoadShopGranted(this.formItem.id,this.formItem.searchVal);\r\n    },\r\n    checkAllShop(value, row = {}) {\r\n      if (!row.id) return;\r\n      this.$Modal.confirm({\r\n        title:row.isAllShop === 1? `确定要取消赋予 ${row.nickName} 全部店铺权限吗？` : `确定要给 ${row.nickName} 赋予全部店铺的权限吗？`,\r\n        content: row.isAllShop === 1 ? \"注：此操作会保留该用户现有的店铺权限，但是后续新增店铺时不会自动授予该用户所有店铺权限！\" : \"\",\r\n        okText: \"确定\",\r\n        cancelText: \"取消\",\r\n        onOk: () => {\r\n          this.changeLoading = true;\r\n          User.addAllShops({\"id\":row.id,\"isCheck\":value?1:0}).then(res => {\r\n            if (res && res['code'] === 0 && res['message'] === \"success\") {\r\n              this.$Message.success(\"授权成功！\");\r\n            }\r\n            this.handleSearch();\r\n          }).finally(() => {\r\n            this.changeLoading = false;\r\n          });\r\n        },\r\n        onCancel: () => {\r\n          this.changeShow = false;\r\n          setTimeout(() => {\r\n            this.changeShow = true;\r\n          }, 100);\r\n        }\r\n      });\r\n    },\r\n    personSyn() {\r\n      this.flag = true;\r\n      User.syncWechatUser().then(res => {\r\n          if (res['code'] === 0) {\r\n            this.$Message.success(\"同步成功\");\r\n            this.handleSearch();\r\n          }\r\n        }).finally(() => {\r\n        this.flag = false;\r\n      });\r\n    },\r\n    handleModal(actionType, data) {\r\n      this.ActionType = actionType;\r\n      if(this.ActionType === 'add'){\r\n        this.formItem.userName='';\r\n        this.formItem.password= '';\r\n      }\r\n      this.row = data;\r\n      this.bool1 = false;\r\n      this.bool2 = false;\r\n      if (data) {\r\n        this.getDeptList(data.companyId);\r\n        this.formItem = Object.assign({}, this.formItem, data);\r\n      }\r\n      if (this.current === this.forms[0]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑用户 - \" : '查看用户 - ') + data.userName : \"添加用户\";\r\n        this.modalVisible = true;\r\n      }\r\n      if (this.current === this.forms[1]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑分配角色 - \" : \"查看分配角色 -\") + data.userName : \"分配角色\";\r\n        this.handleLoadRoles(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[2]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑分配权限 - \" : \"查看分配权限 -\") + data.userName : \"分配权限\";\r\n        this.handleLoadUserGranted(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[3]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"编辑网店权限 - \" : \"查看网店权限 -\") + data.userName : \"网店权限\";\r\n        this.handleLoadShopGranted(this.formItem.id);\r\n      }\r\n      if (this.current === this.forms[4]) {\r\n        this.modalTitle = data ? (this.ActionType === 'edit' ? \"修改密码 - \" : \"查看密码 -\") + data.userName : \"修改密码\";\r\n        this.modalVisible = true;\r\n      }\r\n    },\r\n    //角色排序\r\n    roleSort(obj) {\r\n      this.pageInfo.order = obj.order;\r\n      this.pageInfo.page = 1;\r\n      this.pageInfo.pageSize = 10;\r\n      this.handleSearch();\r\n    },\r\n    getRuleIds() {\r\n      this.pageInfo.roleIds = this.formSelectRoles.join(\",\");\r\n    },\r\n    handleResetForm(form) {\r\n      if (this.$refs[form] !== undefined) {\r\n        this.$refs[form].resetFields();\r\n        if (form === \"searchForm\") {\r\n          this.formSelectRoles = [];\r\n          this.pageInfo.departmentId = undefined;\r\n        }\r\n      }\r\n    },\r\n    handleReset() {\r\n      this.formItem = {\r\n        userId: \"\",\r\n        userName: \"\",\r\n        nickName: \"\",\r\n        enName: \"\",\r\n        password: \"\",\r\n        passwordConfirm: \"\",\r\n        status: 0,\r\n        intentAccess: 1,\r\n        companyId: \"\",\r\n        departmentId: null,\r\n        email: \"\",\r\n        mobile: \"\",\r\n        userDesc: \"\",\r\n        avatar: \"\",\r\n        grantRoles: [],\r\n        grantShops: [],\r\n        grantMenus: [],\r\n        grantActions: []\r\n      };\r\n      //重置验证\r\n      this.forms.map(form => {\r\n        this.handleResetForm(form);\r\n      });\r\n      this.current = this.forms[0];\r\n      this.formItem.grantMenus = [];\r\n      this.formItem.grantActions = [];\r\n      this.selectTreeData = [];\r\n      this.modalVisible = false;\r\n      this.saving = false;\r\n      this.checkedIds = [];\r\n    },\r\n    handleSubmit() {\r\n      if (this.current === this.forms[0]) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            if (this.formItem.id) {\r\n              User.edit(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.modalVisible = false;\r\n                    this.handleReset();\r\n                  }\r\n                  this.handleSearch();\r\n                })\r\n                .finally(() => {\r\n                  this.saving = false;\r\n                });\r\n            } else {\r\n              User.add(this.formItem).then(res => {\r\n                  if (res['code'] === 0) {\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.handleReset();\r\n                  }\r\n                  this.handleSearch();\r\n                })\r\n                .finally(() => {\r\n                  this.saving = false;\r\n                });\r\n            }\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[1] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            User.saveUserRoles(this.formItem)\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"分配角色成功\");\r\n                  this.modalVisible = false;\r\n                  this.handleReset();\r\n                }\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n              });\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[2] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            const authorityIds = this.getCheckedAuthorities();\r\n            //解决部分勾选问题\r\n            const getParentIds = (arr = []) => {\r\n              for (const xItem of arr) {\r\n                const childrenIds = xItem.children ? xItem.children.map(v => v.authorityId) : [];\r\n                let flag = false;\r\n                for (const yItem of childrenIds) {\r\n                  if (authorityIds.includes(yItem)) {\r\n                    flag = true;\r\n                    break;\r\n                  }\r\n                }\r\n                if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                  authorityIds.push(xItem.authorityId);\r\n                }\r\n                if (xItem.children) getParentIds(xItem.children);\r\n              }\r\n            };\r\n            getParentIds(this.selectMenus);\r\n            for (const xItem of this.selectMenus) {\r\n              const childrenIds = xItem.children ? xItem.children.map(v => v.authorityId) : [];\r\n              let flag = false;\r\n              for (const yItem of childrenIds) {\r\n                if (authorityIds.includes(yItem)) {\r\n                  flag = true;\r\n                  break;\r\n                }\r\n              }\r\n              if (flag === true && !authorityIds.includes(xItem.authorityId)) {\r\n                authorityIds.push(xItem.authorityId);\r\n              }\r\n            }\r\n            this.saving = true;\r\n            Authority.grantAuthorityForUser({\r\n              userId: this.formItem.id,\r\n              authorityIds: authorityIds\r\n            }).then(res => {\r\n              if (res['code'] === 0) {\r\n                this.$Message.success(\"授权成功\");\r\n                this.modalVisible = false;\r\n                this.handleReset();\r\n              }\r\n              this.handleSearch();\r\n            }).finally(() => {\r\n              this.saving = false;\r\n            });\r\n          }\r\n        });\r\n      }\r\n      if (this.current === this.forms[3] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (!valid) {\r\n            return;\r\n          }\r\n          this.saving = true;\r\n          let shopIds = \"\";\r\n          this.checkAllGroup.map(function(item) {\r\n            if (item) shopIds += item + \",\";\r\n          });\r\n          let allShopIds = \"\";\r\n          this.selectShops.map(item=> item['shopList'].map(each=>allShopIds += each.id+\",\"))\r\n          User.saveUserShops({id: this.formItem.id,\"shopIds\":shopIds,\"allShopIds\":allShopIds}).then(res=>{\r\n            if (res['code'] === 0) {\r\n              this.$Message.success(\"分配店铺权限成功\");\r\n              this.formItem.searchVal = \"\";\r\n            }\r\n            this.handleSearch();\r\n          }).finally(() => {\r\n            this.saving = false;\r\n            this.modalVisible = false;\r\n          })\r\n        });\r\n      }\r\n      if (this.current === this.forms[4] && this.formItem.id) {\r\n        this.$refs[this.current].validate(valid => {\r\n          if (valid) {\r\n            this.saving = true;\r\n            User.updatePassword({id: this.formItem.id, password: this.formItem.password})\r\n              .then(res => {\r\n                if (res['code'] === 0) {\r\n                  this.$Message.success(\"修改成功\");\r\n                  this.modalVisible = false;\r\n                  this.handleReset();\r\n                }\r\n                this.handleSearch();\r\n              })\r\n              .finally(() => {\r\n                this.saving = false;\r\n                this.modalVisible = false;\r\n              });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleSearch(page) {\r\n      if (page) {\r\n        this.pageInfo.page = page;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        ...this.pageInfo,\r\n        departmentIds: this.pageInfo.departmentId ? this.pageInfo.departmentId.toString() : undefined\r\n      };\r\n      delete params.departmentId;\r\n      User.listPage(params).then(res => {\r\n        this.data = res.data.records;\r\n        this.pageInfo.total = parseInt(res.data.total);\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getCheckedAuthorities() {\r\n      const menus = this.$refs[\"tree\"].getCheckedProp(\"authorityId\");\r\n      return menus.concat(this.formItem.grantActions);\r\n    },\r\n    handleLoadShopGranted(id,shopName){\r\n      const that = this;\r\n      that.checkBoxLoading = true;\r\n      let res1 = User.getUserShops({id:id});\r\n      let res2 = User.getUserShops({name:shopName});\r\n      Promise.all([res1,res2]).then((result)=>{\r\n        const res1 = result[0] || {};\r\n        const res2 = result[1] || {};\r\n        let allShop = res2.data||[];\r\n        let shopObj= {};\r\n        allShop.map(item=>{\r\n          let nameKey = item['nameKey'];\r\n          let list = shopObj[nameKey]||[];\r\n          list.push(item);\r\n          shopObj[nameKey] = list;\r\n        })\r\n        let selectShops = [];\r\n        for (let nameKey in shopObj) {\r\n          selectShops.push({\"nameKey\":nameKey,\"shopList\":shopObj[nameKey]});\r\n        }\r\n        selectShops.sort((o1,o2)=> o1['nameKey'].localeCompare(o2['nameKey']));\r\n        that.selectShops = selectShops;\r\n        let checkArray = [];\r\n        let userShop = (res1.data||[]).map(item=>item[\"id\"]);\r\n        for (let i = 0; i < selectShops.length; i++) {\r\n          let data = new Set();\r\n          let shopList = selectShops[i][\"shopList\"];\r\n          for(let j = 0; j < shopList.length; j++){\r\n            if(userShop.indexOf(shopList[j][\"id\"])>=0){\r\n              data.add(shopList[j][\"id\"]);\r\n            }\r\n          }\r\n          checkArray[i] = Array.from(data);\r\n        }\r\n        that.checkAllGroup = checkArray;\r\n        that.checkBoxLoading = false;\r\n        that.modalVisible = true;\r\n      })\r\n    },\r\n    changeCheckBox(res) {\r\n      //多选按钮改变\r\n      this.checkAllGroup[res.index] = res.data;\r\n      //this.setCheckData();\r\n      this.checkAllGroup = JSON.parse(\r\n          JSON.stringify(this.checkAllGroup)\r\n      );\r\n    },\r\n    setCheckData() {\r\n      //更新多选数据\r\n      let checkData = new Set(),\r\n          data = [-1, 0, 1, 2, 3, 4];\r\n      let check = this.checkAllGroupArray.slice();\r\n      check.map(item => {\r\n        item.slice();\r\n        let array = item.filter(item => {\r\n          return data.indexOf(item) < 0;\r\n        });\r\n        array.map(item => {\r\n          checkData.add(item);\r\n        });\r\n      });\r\n      this.checkAllGroup = Array.from(checkData);\r\n    },\r\n    handleLoadUserGranted(userId) {\r\n      const that = this;\r\n      const p1 = Authority.getAuthorityMenu(\"\");\r\n      const p2 = Authority.getAuthorityForUser(userId);\r\n      const roleAuthorites = [];\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0 && res1.data) {\r\n          let opt = {primaryKey: \"id\", parentKey: \"parentId\", startPid: \"0\"};\r\n          if (res2.code === 0 && res2.data && res2.data.length > 0) {\r\n            let menus = [];\r\n            let actions = [];\r\n            res2.data.map(item => {\r\n              if (item.owner === \"role\") {\r\n                roleAuthorites.push(item.authorityId);\r\n              }\r\n              // 菜单权限\r\n              if (item.authority.indexOf(\"MENU_\") !== -1 && !menus.includes(item.authorityId)) {\r\n                menus.push(item.authorityId);\r\n              }\r\n              // 操作权限\r\n              if (item.authority.indexOf(\"ACTION_\") !== -1 && !actions.includes(item.authorityId)) {\r\n                actions.push(item.authorityId);\r\n              }\r\n            });\r\n            that.formItem.grantMenus = menus;\r\n            that.formItem.grantActions = actions;\r\n          }\r\n          res1.data.map(item => {\r\n            // 菜单选中\r\n            if (that.formItem.grantMenus.includes(item.authorityId)) {\r\n              item._isChecked = true;\r\n              // 归属角色权限,禁止授权\r\n              if (roleAuthorites.includes(item.authorityId)) {\r\n                // 插件不支持,禁用\r\n                item.disabled = true;\r\n                item.menuName += \" (禁止勾选)\";\r\n              }\r\n            }\r\n            // 功能权限\r\n            item.actionList.map(action => {\r\n              if (roleAuthorites.includes(action.authorityId)) {\r\n                action.disabled = true;\r\n              }\r\n            });\r\n          });\r\n          that.selectMenus = listConvertTree(res1.data, opt);\r\n          that.CommMenus = JSON.parse(JSON.stringify(that.selectMenus))\r\n        }\r\n        that.modalVisible = true;\r\n      });\r\n    },\r\n    handleLoadRoles(userId) {\r\n      if (!userId) {\r\n        return;\r\n      }\r\n      const that = this;\r\n      const p1 = Role.getAll();\r\n      const p2 = User.getUserRoles(userId);\r\n      Promise.all([p1, p2]).then(function (values) {\r\n        let res1 = values[0];\r\n        let res2 = values[1];\r\n        if (res1.code === 0) {\r\n          let result1 = [];\r\n          res1.data.map(item => {\r\n            if (item.roleId !== 1) {\r\n              result1.push(item);\r\n            }\r\n          });\r\n          that.selectRoles = result1;\r\n          that.CommselectRoles = result1;\r\n        }\r\n        if (res2.code === 0) {\r\n          let result = [];\r\n          res2.data.map(item => {\r\n            result.push(item.id);\r\n          });\r\n          that.formItem.grantRoles = result;\r\n        }\r\n        that.modalVisible = true;\r\n      });\r\n    },\r\n    handlePage(current) {\r\n      this.pageInfo.page = current;\r\n      this.handleSearch();\r\n    },\r\n    handlePageSize(size) {\r\n      this.pageInfo.limit = size;\r\n      this.handleSearch();\r\n    },\r\n    handleCompany() {\r\n      CommonApi.getAllCompany().then(res => {\r\n        this.companyList = res.data;\r\n      });\r\n    },\r\n    getDeptList(val) {\r\n      if (val != null) {\r\n        this.formItem.departmentId = null;\r\n        getByCompanyId(val).then(res => {\r\n          this.deptSearchList = res.data;\r\n          let opt = {\r\n            primaryKey: \"id\",\r\n            parentKey: \"parentId\",\r\n            startPid: \"0\"\r\n          };\r\n          this.selectTreeData = listConvertTree(res.data, opt);\r\n        });\r\n      }\r\n    },\r\n    getRolesAll() {\r\n      CommonApi.getAllRole().then(res => {\r\n        if (res['code'] === 0) {\r\n          res.data.map(item => {\r\n            if (item.roleId !== \"1\") this.roleAll.push(item);\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleTabClick(name) {\r\n      this.current = name;\r\n      this.handleModal(this.actionType, this.row);\r\n    },\r\n    setTreeData(arr = [], reset = false) {\r\n      const {checkedIds} = this;\r\n      arr.forEach(item => {\r\n        if (reset === true) {\r\n          item.indeterminate = false;\r\n          item.checked = false;\r\n        } else {\r\n          item.title = item.description;\r\n          item.expand = item.level < 2;\r\n          item.checked = checkedIds.includes(item.id);\r\n        }\r\n        if (item.children && item.children.length > 0) {\r\n          this.setTreeData(item.children, reset);\r\n        }\r\n      });\r\n    },\r\n    onVisibleChange(visible) {\r\n      if (visible === false) {\r\n        this.setTreeData(this.productCategoryOps, true);\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.userManage {\r\n  .userManageForm {\r\n    position: relative;\r\n    padding-right: 135px;\r\n  }\r\n\r\n  .ivu-table-wrapper {\r\n    .ivu-table-cell {\r\n      padding-left: 4px;\r\n      padding-right: 4px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}